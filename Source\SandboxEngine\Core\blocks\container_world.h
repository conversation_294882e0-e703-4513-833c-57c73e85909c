#ifndef __WORLDCONTAINERMGR_H__
#define __WORLDCONTAINERMGR_H__

#include "ChunkSave_generated.h"
#include "world_types.h"
#include "container.h"
#include "OgreHashTable.h"
#include "proto_common.h"
#include "proto_common.pb.h"
#include "defdata.h"
#include "SandboxEngine.h"
#include "Threads/ReadWriteLock.h"

#include <chrono>

namespace google {
    namespace protobuf {
        template <typename Element>
        class RepeatedPtrField;
    }
}

namespace game {
    namespace common {
        class PB_ItemRune;
    }
}


class EffectParticle;
class VehicleWorld;
class World;
struct CustomActorModelData;
struct CustomAvatarModelData;

#define STORAGEBOX_CAPACITY 30
#define BOOKCABINET_CAPACITY 6
#define BONFIRE_CAPACITY 5
#define BONFIRE_ATTRACT_CAPACITY 30
#define BONFIRE_ICONNUM 3

namespace Rainbow{
class ProgressBarWithText3D;
class Image3D;
class Entity;
}

class IClientActor;
class ChunkRandGen;
struct tagMPItemData;
class BlockMaterial;
class ContainerBuildBluePrint;
class ClientActorBlockLaser;
class WorldPot;

#ifndef SAVE_BUFFER_BUILDER
typedef flatbuffers::FlatBufferBuilder SAVE_BUFFER_BUILDER;
#endif
//tolua_begin
struct BrushMonsterAttr
{
	int MobResID;
	int everyNum;
	int maxNum;
	int spawnWide;
	int spawnHigh;
	int spawnDelay;
	bool numSwitch;
	bool DelaySwitch;
};
//tolua_end

//tolua_begin
enum LootItemResultInfo
{
	VILLAGER_NAME = 1,
	VILLAGER_OWNER_NAME,
	VILLAGER_PROFESSION,
	VILLAGER_CHARACTER,
	VILLAGER_ALIVE_DAYS,
	VILLAGER_ISAWAYED,
	VILLAGER_REASON,
	MAX_NUM
};
//tolua_end

class EXPORT_SANDBOXENGINE WorldContainer;
class WorldContainer : public BaseContainer//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	WorldContainer(int baseindex = 0) : BaseContainer(baseindex), m_World(NULL), m_NeedSave(false), m_NeedClear(false), m_vehicleWorld(NULL),m_isRideContainer(false),m_NeedDropItem(true)
	{
		updateContainingBlockInfo();
	}

	WorldContainer(const WCoord& blockpos, int baseindex) : BaseContainer(baseindex), m_World(NULL), m_BlockPos(blockpos), m_NeedSave(false), m_NeedClear(false), m_vehicleWorld(NULL)
	{
		updateContainingBlockInfo();
	}

	virtual ~WorldContainer();


	virtual void enterWorld(World* pworld);


	virtual void startVehicle()
	{

	}

	virtual void leaveWorld();

	virtual int getObjType() const = 0;
	virtual FBSave::ContainerUnion getUnionType() = 0;//not_auto_export_to_lua

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) = 0; //not_auto_export_to_lua
	virtual bool load(const void* srcdata) = 0; //not_auto_export_to_lua
	virtual bool canAddToChunk(Chunk* pchunk)
	{
		return true;
	}

	virtual int getItemAndAttrib(google::protobuf::RepeatedPtrField<game::common::PB_ItemData>* pItemInfos, google::protobuf::RepeatedField<float>* pAttrInfos)
	{
		return 0;
	}

	virtual void syncItemUserdata(IClientPlayer* player)
	{
	}

	virtual void syncCustomActorModelData(IClientPlayer* player)
	{

	}
	virtual bool syncFullyCustomModelData(IClientPlayer* player)
	{
		return false;
	}
	virtual void updateCustomActorModelData(CustomActorModelData& data)
	{

	}
	virtual void updateFullyCustomModelData(std::string skey)
	{

	}

	virtual void updateTick()
	{
	}
	virtual void updateDisplay(float dtime)
	{
	}

	virtual void dropItems()
	{
	}
	virtual int calComparatorInputOverride()
	{
		return 0;
	}

	virtual BackPackGrid* index2Grid(int index) override
	{
		return NULL;
	}
	virtual bool canPutItem(int index)
	{
		return false;
	}
	virtual void onAttachUI()
	{

	}
	virtual void onDetachUI()
	{
	}
	virtual void onAttachToMech() //粘连到滑动方块上
	{

	}
	virtual void onDetachFromMech() //从滑动方块上解开， 放到场景
	{

	}
	virtual void addOpenUIN(int uin);
	virtual void removeOpenUIN(int uin);

	virtual bool isOpenning();

	virtual void afterChangeGrid(int index);

	virtual void resetOpenUINs();
	void updateContainingBlockInfo()
	{
		m_BlockMtl = NULL;
		m_BlockData = -1;
		m_BlockPosEx = WCoord(0, 0, 0);
	}
	BlockMaterial* getBlockMtl();

	virtual bool checkPutItem(int itemid, int num)
	{
		return false;
	}

	virtual BackPackGrid* getGridByItemID(int itemid)
	{
		return NULL;
	}

	virtual void replaceGridByBluePrint(WorldContainer* container);

	std::vector<int> m_OpenUINs; //被这些玩家打开
	BlockMaterial* m_BlockMtl;
	int m_BlockData;

	virtual const char* getText() const
	{
		return NULL;
	}
	virtual void setText(const char* text)
	{
	}

	virtual float getAttrib(int i)
	{
		return 0;
	}
	virtual	bool needSetPassword()
	{
		return false;
	}
	virtual void setPassword(int password)
	{

	}

	virtual int getPassword()
	{
		return 0;
	}

	virtual std::string getBPFileName()
	{
		return "";
	}

	virtual std::string getBPDataStr()
	{
		return "";
	}

	virtual void StartWorking(const char* sheetName, const char* authorName)
	{

	}

	virtual void MakeCustoModel(int uin, const char* modelname, const char* modeldesc, int modeltype = 0)
	{

	}

	virtual void setBrushMonsterAttr(int MobResID, int everyNum, int maxNum, int spawnWide, int spawnHigh, int spawnDelay, bool numSwitch, bool DelaySwitch)
	{

	}

	virtual BrushMonsterAttr getBrushMonsterAttr()
	{
		return BrushMonsterAttr();
	}

	virtual void bindCustomAvatar(std::string bonename, std::string modelfilename, float scale, float yaw, float pitch, short offsetx, short offsety, short offsetz)
	{

	}
	virtual void setRateByPower(int power)
	{
	}
	virtual bool outsideWorkArea()
	{
		return false;
	}
	virtual int getMachineAttribute(int iType, bool bNeedCal) { return 0; }
	virtual int checkIfCanGenVehicle() { return 0; }
	// 预览物理机械
	virtual void preVehicleBlock() {}
	virtual void buildVehicleBlock() {}
	virtual bool getVehicleBuildInfo() { return false; }

	virtual void VehicleCentroidDisplay(bool isDisplay) {}

	virtual void clear() {}//清空储物箱
	virtual int addItemByCount(int itemid, int num) { return 0; }
	virtual void removeItemByCount(int itemid, int num) {}
	//动作序列控制器
	virtual bool updateActionerData(const char* jsonstr, WCoord startPos = WCoord(0, 0, 0), int rotateType = -1) { return false; }
	virtual std::string getActionerDataStr(bool fromWorkShop = false, WCoord start = WCoord(0, 0, 0)) { return ""; }

	virtual void setSensorValue(int value) {}
	virtual int getSensorValue() { return 0; }
	virtual bool getIsSensorRever() { return false; }
	virtual void setIsSensorRever(bool b) {}
	virtual void setSensorValueAndIsRever(int value, bool b) {}
	virtual bool getEditFlag() { return false; }
	virtual int getLootResultInfoSize() { return 0; }
	virtual std::string getLootResultInfoByType(int type) { return ""; }
	virtual void setTombStoneTitle(std::string title) {}
	virtual void setTombStoneContent(std::string content) {}
	virtual std::string getTombStoneTitle() const { return ""; }
	virtual std::string getTombStoneContent() const { return ""; }
	virtual int  getMobGiftNum() { return 0; }
	virtual void setTemperatureLev(int lev) { }; //调节温度
	virtual void setSummonerCD(int cd) {}
	virtual void setSummonerCDType(int type) {}
	virtual void setSummonerLevel(int level) {}
	virtual void setSummonerLevelType(int type) {}
	virtual int getSummonerLevelType() { return 0; }
	virtual int getSummonerLevel(){ return 0; }
	virtual bool canEditActorModel()
	{
		return false;
	}

	virtual bool canEditFullyCustomModel()
	{
		return false;
	}

	virtual int getGridNum()
	{
		return 0;
	}

	virtual int getBaseIndex()
	{
		return m_BaseIndex;
	}

	virtual bool remoteMarkBlockForUpdate()
	{
		return false;
	}

	virtual void resetEntityBySyncData(std::string skey)
	{

	}

	virtual void convertRotateByBluePrint(int rotatetype)
	{

	}

	virtual bool shieldDialogue()
	{
		return false;
	}

	virtual bool doOpenContainer();

	//类名
	virtual const char* getContainerName()const {
		return "WorldContainer";
	};
	//tolua_end
	//滑动方块销毁container时的回调
	virtual void mechaRemoveCallBack() {};
	void setBlockPos(const WCoord& pos) { m_BlockPos = pos; };
	void registerUpdateTick();
	void registerUpdateDisplay();
	void unRegisterUpdateTick();
	void unRegisterUpdateDisplay();
	bool getNeedClear() { return m_NeedClear; }
	void setNeedClear();

	bool getNeedDropItem() { return m_NeedDropItem; }
	void setNeedDropItem(bool needDropItem) { m_NeedDropItem = needDropItem; }

	virtual BackPackGrid* getReplaceGrid(int itemid) { return nullptr; }
protected:
	void dropOneItem(BackPackGrid& itemgrid);
	void dropOneItem(BackPackGrid& itemgrid, const WCoord& BlockPos);
	flatbuffers::Offset<FBSave::ContainerCommon> saveContainerCommon(SAVE_BUFFER_BUILDER& builder);
	bool loadContainerCommon(const FBSave::ContainerCommon* srcdata);
	void notifyChange2Openers(int gridindex, bool attrib, std::string text = "");

public:
	//tolua_begin
	World* m_World;
	World* m_vehicleWorld;
	WCoord m_BlockPos;
	bool m_isRideContainer;
	unsigned int m_OwnerUin;
	long long m_ObjId;
	bool m_NeedSave;
	WCoord m_BlockPosEx;//额外位移记录载具中心位置
	//tolua_end
protected:
	Rainbow::Mutex m_ContainerMutex;
private: 
	friend class WorldContainerMgr;
	bool m_NeedClear;
	bool m_NeedDropItem;
};//tolua_exports

class EXPORT_SANDBOXENGINE WorldValueContainer : public WorldContainer //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	WorldValueContainer() : m_SubType(0), m_Value(0)
	{
	}

	WorldValueContainer(const WCoord& blockpos) : WorldContainer(blockpos, 0), m_SubType(0), m_Value(0)
	{
	}

	virtual int getObjType() const override;
	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerValue;
	}
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual bool load(const void* srcdata);

	void setValueInt(int s)
	{
		m_Value = s;
	}
	int getValueInt()
	{
		return m_Value;
	}
	//tolua_end
public:
	//tolua_begin
	int m_SubType;
	int m_Value;
	//tolua_end
}; //tolua_exports

class EXPORT_SANDBOXENGINE WorldStringContainer : public WorldContainer //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	WorldStringContainer() : m_SubType(0)
	{
	}

	WorldStringContainer(const WCoord& blockpos) : WorldContainer(blockpos, 0), m_SubType(0)
	{
	}

	virtual int getObjType() const override;
	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerString;
	}
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual bool load(const void* srcdata);

	virtual const char* getText()
	{
		return m_Str.c_str();
	}
	virtual void setText(const char* text);
	//tolua_end
public:
	//tolua_begin
	int m_SubType;
	std::string m_Str;
	//tolua_end
}; //tolua_exports

class EXPORT_SANDBOXENGINE WorldFurnace : public WorldContainer //tolua_exports
{ //tolua_exports
public:
	enum
	{
		GRID_MTL = 0,
		GRID_FUEL,
		GRID_RESULT
	};
public:
	//tolua_begin
	WorldFurnace();
	WorldFurnace(const WCoord& blockpos);
	virtual ~WorldFurnace();

	virtual int getObjType() const override
	{
		return OBJ_TYPE_FURNACE;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerFurnace;
	}

	virtual BackPackGrid* index2Grid(int index) override;
	virtual void afterChangeGrid(int index);
	virtual bool canPutItem(int index);
	virtual void onAttachUI();
	virtual void onDetachUI();

	virtual void dropItems();
	virtual void updateTick();

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual bool load(const void* srcdata);
	virtual int getItemAndAttrib(google::protobuf::RepeatedPtrField<game::common::PB_ItemData>* pItemInfos, google::protobuf::RepeatedField<float>* pAttrInfos) override;
	virtual bool canInsertItem(const BackPackGrid& grid, int param) override;
	virtual float getAttrib(int i)
	{
		if (i == 0) return getHeatPercent();
		else return getMeltTicksPercent();
	}
	virtual int onInsertItem(const BackPackGrid& grid, int num, int params) override;
	virtual BackPackGrid* onExtractItem(int params) override;
	virtual int calComparatorInputOverride() override;

	void clear();
	int addItemByCount(int itemid, int num);
	void removeItemByCount(int itemid, int num);

	float getHeatPercent();
	float getMeltTicksPercent();

	virtual int getGridNum()
	{
		return 3;
	}
	const char* getContainerName() const override {
		return "WorldFurnace";
	}
	//tolua_end
	virtual void enterWorld(World* pworld) override;
private:
	void meltOnce();
	void addHeatOnce();
	void onHeatOnOff();

public:
	//tolua_begin
	BackPackGrid m_Grids[3]; //0: mtl,  1: fuel,   2: result
	int m_CurHeat;
	int m_MaxHeat;
	int m_MeltTicks;
	bool m_isMelting;
	float m_meltOnce_Time;	//消耗一个材料的时间(tick数, 如200即10秒)
	//tolua_end
};//tolua_exports


/*LLDO:氧气炉,Oxygen_Begin,******************************************************************* */
class EXPORT_SANDBOXENGINE WorldFurnaceOxy : public WorldFurnace//tolua_exports
{//tolua_exports
public:
	enum
	{
		GRID_MTL = 0,
		GRID_FUEL,
		GRID_RESULT
	};
public:
	//tolua_begin
	WorldFurnaceOxy();
	WorldFurnaceOxy(const WCoord& blockpos);
	virtual ~WorldFurnaceOxy();

	virtual int getObjType() const override
	{
		return OBJ_TYPE_FURNACE;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerFurnaceOxy;
	}

	virtual void updateTick();

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual bool load(const void* srcdata);
	virtual void onAttachUI();
	virtual void onDetachUI();
	virtual void afterChangeGrid(int index);
	virtual void leaveWorld() override;
	virtual void enterWorld(World* pworld)
	{
		WorldContainer::enterWorld(pworld);
		registerUpdateTick();
		ShowCover();
	}
	virtual int getItemAndAttrib(google::protobuf::RepeatedPtrField<game::common::PB_ItemData>* pItemInfos, google::protobuf::RepeatedField<float>* pAttrInfos) override;
	virtual float getAttrib(int i)
	{
		if (i == 0) return getHeatPercent();
		else if (i == 1) return getMeltTicksPercent();
		else if (i == 2) return GetOxygenValue();
		else if (i == 3) return GetDegree();
		else return (float)m_IsCoverExisted;
	}
	virtual int onInsertItem(const BackPackGrid& grid, int num, int params) override;
	virtual bool canInsertItem(const BackPackGrid& grid, int param) override;
	void ShowCover();

	float GetOxygenValue();
	float GetDegree();
	//tolua_end
	const char* getContainerName() const override {
		return "WorldFurnaceOxy";
	}
private:
	void meltOnce();
	void addHeatOnce();
	void deleteOxy();		//消耗氧气
	void onHeatOnOff();
public:
	//tolua_begin
	int m_nOxygenValue;		//氧气值
	int m_IsCoverExisted;	//氧气罩是否存在, 0: 不存在; 1: 存在
	int m_OxyTick;
	EffectParticle* m_SafeAreaFX;	//蓝色氧气罩特效
	bool m_bSafeArea;
	//tolua_end
private:
	float maxOxyValue;
	int m_RefreshOxygenTick; //产生氧气时， 每隔一段时间刷新区域
};//tolua_exports
/**************Oxygen_End********************************************************************* */

/**********************************************************************************************
类    名：WorldBlueprint
功    能："蓝图工作台"容器
********************************************************************************************* */
class EXPORT_SANDBOXENGINE WorldBlueprint : public WorldContainer//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	enum GRID_TEMP
	{
		GRID_MTL = 0,
		GRID_RESULT,
		GRID_MAX
	};
	//tolua_end
public:
	//tolua_begin
	WorldBlueprint();
	WorldBlueprint(const WCoord& blockpos);
	virtual ~WorldBlueprint();

	virtual int getObjType() const override
	{
		return OBJ_TYPE_FURNACE;
	}
	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerBlueprint;
	}

	virtual void updateTick();

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual bool load(const void* srcdata);
	virtual void onAttachUI();
	virtual void onDetachUI();
	virtual BackPackGrid* index2Grid(int index) override;
	virtual void afterChangeGrid(int index);
	virtual bool canPutItem(int index);
	virtual void leaveWorld() override;
	virtual void enterWorld(World* pworld)
	{
		WorldContainer::enterWorld(pworld);
		registerUpdateTick();
	}
	virtual int getItemAndAttrib(google::protobuf::RepeatedPtrField<game::common::PB_ItemData>* pItemInfos, google::protobuf::RepeatedField<float>* pAttrInfos) override;
	virtual float getAttrib(int i)
	{
		if (i == 0)
			return m_nCurWorkTick / m_nMakeOneTick;	//制作进度
		else
			return 0;
	}
	virtual int onInsertItem(const BackPackGrid& grid, int num, int params) override;

	virtual void StartWorking(const char* sheetName, const char* authorName);

	void SetFileInfo(char* pszFileName, const char* pszNickName, WCoord dim, WCoord copyBlockPos, WCoord knotPos);

	virtual int getGridNum()
	{
		return GRID_MAX;
	}
	//tolua_end
	const char* getContainerName() const override {
		return "WorldBlueprint";
	}
private:
	void ProductOne();	//产生一个蓝图图纸
	void SetSheetData(std::string& retStr);

public:
	//tolua_begin
	BackPackGrid m_Grids[GRID_MAX];
	std::string m_SheetName;
	std::string m_DataFileName;
	std::string m_DataNickName;
	std::string m_DataAuthorName;
	WCoord m_DataDim;
	WCoord m_copyBlockPos;
	WCoord m_knotPos;
	//tolua_end
private:
	bool m_bIsWorking;
	float m_nMakeOneTick;	//制作一个所需要的时间(tick数)
	float m_nCurWorkTick;
	int m_nRetID;		//生成物(蓝图图纸道具)id.
};//tolua_exports

/**********************************************************************************************
类    名：WorldMeasureDistance
功    能："测距方块"容器
********************************************************************************************* */
class EXPORT_SANDBOXENGINE WorldMeasureDistance : public WorldContainer//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	WorldMeasureDistance();
	WorldMeasureDistance(const WCoord& blockpos);
	virtual ~WorldMeasureDistance() {};
	virtual int getObjType() const override
	{
		return OBJ_TYPE_FURNACE;
	}
	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerMeasureDistance;
	}
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual bool load(const void* srcdata);
	virtual void enterWorld(World* pworld);
	virtual void leaveWorld() override;
	virtual void updateDisplay(float dtime);

	void OnBlockChanged();
	void UpdateFindPos();
	//tolua_end
	const char* getContainerName() const override {
		return "WorldMeasureDistance";
	}
public:
	//tolua_begin
	int m_BlockID;
	std::vector<WCoord> m_findPos;
	//tolua_end
public:
};//tolua_exports

class EXPORT_SANDBOXENGINE WorldStorageBox : public WorldContainer//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	WorldStorageBox();
	WorldStorageBox(const WCoord& blockpos);
	virtual ~WorldStorageBox();

	virtual int getObjType() const override
	{
		return OBJ_TYPE_BOX;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerStorage;
	}

	virtual void leaveWorld();

	virtual void afterChangeGrid(int index);
	virtual bool canPutItem(int index);
	virtual void onAttachUI() override;
	virtual void onDetachUI() override;

	virtual int getItemAndAttrib(google::protobuf::RepeatedPtrField<game::common::PB_ItemData>* pItemInfos, google::protobuf::RepeatedField<float>* pAttrInfos) override;
	virtual void syncItemUserdata(IClientPlayer* player) override;
	virtual int onInsertItem(const BackPackGrid& grid, int num, int params) override;
	virtual bool canInsertItem(const BackPackGrid& grid, int param) override;
	virtual BackPackGrid* onExtractItem(int params) override;
	virtual void onSubtractItem(BackPackGrid* grid, int num) override; //从属于container的grid里面减去多少个物品
	virtual int calComparatorInputOverride() override;

	virtual void dropItems();
	virtual void dropItems(WCoord BlockPos);

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual bool load(const void* srcdata);

	virtual int addItem_byGridCopyData(const GridCopyData& grid) override;
	////tolua_end

	//virtual int addItem(int resid, int num, int durable = -1, int enchantnum = 0, const int enchants[] = 0, void* userdata = 0, const char* userdata_str = "") override;

	////tolua_begin
	void append(WorldStorageBox* box);
	virtual void addOpenUIN(int uin);
	virtual void removeOpenUIN(int uin);
	virtual bool checkPutItem(int itemid, int num) override;
	virtual BackPackGrid* getGridByItemID(int itemid) override;

	BackPackGrid* genRandomGrid(bool clear_others); //得到一个有物品的随机格子

	bool checkEmptyGrid(int resid);
	WorldStorageBox* getAppend()
	{
		return m_AppendBox;
	}
	void setItem(int offset, int resid, int num, const char* userdata = NULL);
	virtual int getGridNum()
	{
		return STORAGEBOX_CAPACITY;
	}
	virtual int getGridCount();
	virtual BackPackGrid* index2Grid(int index) override;

	void clear(); //清空储物箱
	int addItemByCount(int itemid, int num);
	void removeItemByCount(int itemid, int num);
	void removeItemByIndex(int index, int num);
	//tolua_end
	const char* getContainerName() const override {
		return "WorldStorageBox";
	}
	void setAppendBox(int blockid);//主箱Grid空时，副箱Grid不空时，合并箱子

protected:
	virtual flatbuffers::Offset<FBSave::ContainerStorage> saveContainerStorage(SAVE_BUFFER_BUILDER& builder);
public:
	//tolua_begin
	BackPackGrid m_Grids[STORAGEBOX_CAPACITY];
	WorldStorageBox* m_AppendBox;
	WorldStorageBox* m_ParentBox;
	int m_GridCount;
	//tolua_end
};//tolua_exports

class EXPORT_SANDBOXENGINE WorldStorageBoxPassword : public WorldStorageBox//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	WorldStorageBoxPassword();
	WorldStorageBoxPassword(const WCoord& blockpos);
	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerStoragePassword;
	}
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual bool load(const void* srcdata);
	bool needSetPassword()
	{
		return true;
	}
	void setPassword(int password)
	{
		m_nPassWord = password;
	}

	int getPassword()
	{
		return m_nPassWord;
	}
	//tolua_end
public:
	//tolua_begin
	int m_nPassWord;
	//tolua_end
};//tolua_exports

//tolua_begin
enum
{
	ITEM_QUALITY_NONE = 0,
	ITEM_QUALITY_NORMAL,//普通
	ITEM_QUALITY_HIGH,//高级
	ITEM_QUALITY_RARE,//稀有
	ITEM_QUALITY_MYTH,//神话
};

struct GenerateItemDesc
{
	int itemid;
	int itemnum;
	int quality;
	GenerateItemDesc() :itemid(0), itemnum(0), quality(0) {};
	void reset() {
		itemid = 0;
		itemnum = 0;
		quality = 0;
	}

	void setItem(int itemid, int itemnum = 0, int quality = 0) {
		this->itemid = itemid;
		this->itemnum = itemnum;
		this->quality = quality;
	}
};
//tolua_end

/*class BaseWorldContainerMgr
{
public:
	BaseWorldContainerMgr() {};
	virtual ~BaseWorldContainerMgr() {};

	virtual void spawnContainer(WorldContainer *container, bool sync_spawn=true){};
	virtual WorldContainer* getContainer(const WCoord &pos){ return NULL; };
	virtual void destroyContainer(const WCoord &pos){};
	virtual void updateTick() {};
	virtual void updateDisplay(float dtime){};
	virtual void clearContainers() {};
	virtual WorldContainer *getContainerExt(const WCoord &pos) { return NULL;};
	virtual void resetContainerByFullyCustomModel(std::string skey) {};
}; */

struct ContainerStats {
	int totalContainers;
	std::unordered_map<std::string, int> containerTypeCount; // 按类型统计数量
	int activeContainers;  // 正在更新的容器数量
	int uiOpenContainers; // 正在被玩家打开的容器数量

	void reset() {
		totalContainers = 0;
		containerTypeCount.clear();
		activeContainers = 0;
		uiOpenContainers = 0;
	}
};

class EXPORT_SANDBOXENGINE WorldContainerMgr;

class ContainerStatistics {
public:
	ContainerStatistics();
	void updateStats(WorldContainerMgr* mgr);
	void printStats();
	const ContainerStats& getStats() const { return m_stats; }

public:
	ContainerStats m_stats;
	float m_lastPrintTime;
};

class EXPORT_SANDBOXENGINE WorldContainerMgr;
class WorldContainerMgr { //tolua_exports

public:
	//tolua_begin
	WorldContainerMgr(World* world);
	~WorldContainerMgr();

	static void generateChestItems(std::vector<GenerateItemDesc>& items, int chestid, ChunkRandGen* randgen, int selectmethod);

	virtual void updateTick();
	virtual void updateDisplay(float dtime);

	virtual void spawnContainer(WorldContainer* container, bool sync_spawn = true);
	virtual void destroyContainer(const WCoord& pos, bool needDropItem = true);
	virtual void clearContainers(bool delboard=true);
	virtual WorldContainer* getContainer(const WCoord& pos);
	virtual WorldContainer* getContainerExt(const WCoord& pos);
	void addContainerByServer(WorldContainer* container);
	virtual void resetContainerByFullyCustomModel(std::string skey);
	virtual void resetContainerByImportModel();
	void addContainerByChunk(WorldContainer* container);
	void removeContainerByChunk(WorldContainer* container);

	WorldStorageBox* spawnStorageBox(const WCoord& pos, int owneruin, bool sync_spawn);
	WorldStorageBox* addDungeonChest(const WCoord& pos, int chestid, ChunkRandGen* randgen);
	WorldContainer* spawnFurnace(const WCoord& pos, int owneruin, bool sync_spawn);
	WorldValueContainer* spawnComparator(const WCoord& pos, int owneruin, bool sync_spawn);
	WorldValueContainer* getComparator(const WCoord& pos);
	void addRandomItemToChest(const WCoord& pos, int chestid, ChunkRandGen* randgen);


	WorldContainer* addFurnace(int x, int y, int z);
	void removeFurnace(int x, int y, int z);
	WorldContainer* getFurnace(int x, int y, int z);

	WorldStorageBox* addStorageBox(int x, int y, int z);
	void removeStorageBox(int x, int y, int z);
	WorldStorageBox* getStorageBox(int x, int y, int z);

	WorldContainer* addContainer(int x, int y, int z, int iType, int iOwner);//只给开发者接口用
	WorldContainer* getContainer(int x, int y, int z);//只给开发者接口用
	void removeContainer(int x, int y, int z);//只给开发者接口用
	WorldStringContainer* getStringContainer(int x, int y, int z);
	WorldContainer* getSignsContainer(int x, int y, int z);
	void removeContainerFromAdd(WorldContainer* pContainer);
	WorldContainer* GetContainer(int x, int y, int z, int type);//获取一个类型的container
	//tolua_end

	virtual void registerUpdateTickContainer(WorldContainer* container);
	virtual void registerUpdateDisplayContainer(WorldContainer* container);
	virtual void unRegisterUpdateTickContainer(WorldContainer* container);
	virtual void unRegisterUpdateDisplayContainer(WorldContainer* container);
	void setContainerNeedClear(WorldContainer* container);

	bool onSearchName(jsonxx::Array& resultArray, const std::string& name);
	bool onSearchCoordinate(jsonxx::Array& resultArray, const int& x, const int& y, const int& z);
	void forEachWorldContainer(const std::function<bool(WorldContainer&)>& func);

    ContainerStatistics& getStatistics() { return m_statistics; }
    void updateStatistics();
    const std::set<WorldContainer*>& getUpdateTickContainers() const { return m_UpdateTickContainers; }
    const std::set<WorldContainer*>& getUpdateDisplayContainers() const { return m_UpdateDisplayContainers; }	

public:
	//tolua_begin
	World* m_World;
	//tolua_end
private:
	void doDeleteContainer(WorldContainer* container, bool delboard = true);
protected:
	mutable Rainbow::ReadWriteLock m_ContainerMgrLock;
private:
	typedef Rainbow::HashTable<WCoord, WorldContainer*, WCoordHashCoder> ContainHashTable;
	ContainHashTable m_Containers;

	std::vector<WorldContainer*>m_AddContainers;
	std::vector<WorldContainer*>m_DelContainers1;
	//std::set<WorldContainer*>m_DelContainers;
	mutable Rainbow::ReadWriteLock m_DelContainerLock;
	std::set<WorldContainer*> m_UpdateTickContainers;
	std::set<WorldContainer*> m_UpdateDisplayContainers;
	bool m_ScanningContainers;
	ContainerStatistics m_statistics;
};//tolua_exports


/**********************************************************************************************
类    名：WorldBookCabinet
功    能：书架 存放书籍
********************************************************************************************* */
class EXPORT_SANDBOXENGINE WorldBookCabinet : public WorldContainer//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	WorldBookCabinet();
	WorldBookCabinet(const WCoord& blockpos);
	virtual ~WorldBookCabinet() {};
	virtual int getObjType() const
	{
		return OBJ_TYPE_BOX;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerBookCabinet;
	}

	virtual void afterChangeGrid(int index);
	virtual bool load(const void* srcdata);
	virtual BackPackGrid* index2Grid(int index);
	virtual int getGridCount();
	virtual void dropItems();
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual void syncItemUserdata(IClientPlayer* player);
	virtual int getGridNum()
	{
		return BOOKCABINET_CAPACITY;
	}
	int getBookNum();
	//tolua_end
	const char* getContainerName() const override {
		return "WorldBookCabinet";
	}
public:
	//tolua_begin
	BackPackGrid m_Grids[BOOKCABINET_CAPACITY];
	//tolua_end

};//tolua_exports

/**********************************************************************************************
类    名：WorldBonFire
功    能：篝火 可以烤肉
********************************************************************************************* */
class EXPORT_SANDBOXENGINE WorldBonFire : public WorldContainer//tolua_exports
{//tolua_exports
public:
	enum
	{
		ROAST_EMPTY = 0,
		ROAST_RAW,
		ROAST_COOKED,
		ROAST_BURNT,
	};
	enum
	{
		FIRE_EXTINCT = 0,
		FIRE_SMALL,
		FIRE_MEDIUM,
		FIRE_BIG,
	};
	enum
	{
		ATTRACT_NONE = 0,
		ATTRACT_DANCING,
		ATTRACT_DANCE_PAUSE,
		ATTRACT_EATING,
		ATTRACT_EAT_PAUSE,
		ATTRACT_SLEEPING,
	};
public:
	//tolua_begin
	WorldBonFire();
	WorldBonFire(const WCoord& blockpos);
	virtual ~WorldBonFire();
	virtual int getObjType() const override
	{
		return OBJ_TYPE_BOX;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerBonFire;
	}

	virtual void afterChangeGrid(int index);
	virtual bool load(const void* srcdata);
	virtual BackPackGrid* index2Grid(int index) override;
	virtual int getGridCount();
	virtual void updateTick();
	virtual void dropItems();
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual void syncItemUserdata(IClientPlayer* player);
	virtual int getGridNum()
	{
		return BONFIRE_CAPACITY;
	}
	int getMeatNum();
	int addItemByCount(int itemid, int num);
	void removeItemByCount(int itemid, int num, int index = 0);
	int getGridItemId(int index = 0);
	void setGridItemId(int itemId, int index);
	void addHeat(int iValue);
	void setHeat(int iValue);
	int getHeat() { return m_HeatValue; }

	int getFireStatus();

	int getRoastSumTick();	// 一共需要的时间
	int getRestRoastTick();	// 剩余时间
	bool isCooked(int index = 0);	//熟了
	bool isBurnt();		//烤糊了
	void changeRoastState(int nstate, int index = 0);	//改变烤肉状态
	int getNextStateItemId(int index = 0);	//根据当前状态和id，获取下一状态的id
	int getHeatWeakRate(int fireStatus); //大中小火的减弱速率
	int getRoastCountDown(int state); //指定状态的烧烤tick
	int getRoastState(int index = 0);	//当前烤肉的状态
	void changeAttractState(int nstate);	//吸引状态流转
	int getAttractStatus();	//读取当前吸引状态 
	int getAttractCountDown(int nstate); //对应吸引状态的倒计时
	void bindActor(WORLD_ID modId);	//吸引野人
	void unbindActor(WORLD_ID modID);	//去掉吸引野人
	void unbindAllActors();
	bool hasArriveActor();	//被吸引的野人是否有靠近篝火
	bool hasDancingActor();	//被吸引的野人是否在跳舞
	bool hasEatingActor();	//被吸引的野人是否在吃肉
	bool checkIfAttractMobs();
	void checkBindModsState(); //updatetick里检查解绑不在对应状态的野人
	void modArrive();	//野人达到跳舞范围，判断状态流转
	void wakeMobAndEndAttract(); //唤醒野人并解绑所有已吸引的野人
	void setPlaySoundTime(int iTime) { m_iPlaySoundTime = iTime; }
	int getPlaySoundTime() { return m_iPlaySoundTime; }
	bool checkIfAttractFull();
	void leaveWorld();
	virtual void enterWorld(World* pworld) override;
	virtual void updateDisplay(float dtTime) override;
	int GetStateMeatNum(int state);	// 获取相同状态肉数量
	int GetAnyMeat(int state);			// 任意获取肉
	float GetCurCookedMeatProgress();		// 当前烤肉的进度
	//tolua_end
	int getLastFireStatus() { return m_LastFireStatus; }
	void setLastFireStatus(int lastFireStatus);
	int getLastRoastState() { return m_LastRoastState; }
	void setLastRoastState(int lastRoastState);
public:
	//tolua_begin
	BackPackGrid m_Grids[BONFIRE_CAPACITY];
	int m_HeatValue;		//火焰强度值
	int m_HeatCountdown;	//火焰减弱倒计时（tick）
	int m_RoastCountdown[BONFIRE_CAPACITY];	//当前状态倒计时（tick）
	int m_RoastState[BONFIRE_CAPACITY];		//当前状态 1生肉，2熟肉，3糊了
	int m_AttractCountdown;	//吸引野人倒计时（tick）
	int m_AttractState;		//当前吸引状态 
	int m_iPlaySoundTime;
	int m_iSoundEatRef;	    //声音被引用次数
	int m_iSoundDanceRef;	//声音被引用次数
	//bool m_bSoundEatPlayed;
	bool m_bSoundDancePlayed;
	std::set<WORLD_ID> m_BindMods;	//吸引到的野人
	int m_iPlayTick;
	std::vector<int> m_RoastSeq;	//烤肉顺序

	int m_LastFireStatus;	// 上次火焰状态
	int m_LastRoastState;	// 上次烤肉状态

	Rainbow::ProgressBarWithText3D* m_Progress;						// 进度条
	Rainbow::Image3D* m_FireIcon[BONFIRE_ICONNUM];	// 火焰图标
	//tolua_end
};//tolua_exports

/**********************************************************************************************
类    名：WorldBed
功    能：床 人或者村民躺上面，村名休息绑定点
********************************************************************************************* */
//tolua_begin
enum VillagerBedStatus {
	ENUM_BED_STATUS_NO_BIND_ACOTR = 0, //没有绑定关系
	ENUM_BED_STATUS_DIING,	//绑定的野人濒死
	ENUM_BED_STATUS_DIED,	//绑定的野人已死亡
	ENUM_BED_STATUS_ESCAPE,	//绑定的野人叛逃
	ENUM_BED_STATUS_NORMAL,	//绑定的野人正常

	ENUM_BED_STATUS_TRADER_IN_HOME, //游商在家
	ENUM_BED_STATUS_TRADER_OUT_HOME, //游商不在家
};
//tolua_end

class EXPORT_SANDBOXENGINE WorldBed : public WorldContainer//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	WorldBed();
	WorldBed(const WCoord& blockpos);
	virtual ~WorldBed() {};
	virtual int getObjType() const override
	{
		return OBJ_TYPE_BOX;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerBed;
	}

	virtual bool load(const void* srcdata);
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual void enterWorld(World* pworld) override;
	virtual void leaveWorld() override;
	virtual void updateTick() override;
	void setOwnerUin(unsigned int uin) { m_OwnerUin = uin; };
	WORLD_ID getBindActor() { return m_BindActor; }
	void setBindActor(WORLD_ID bindactor) { m_BindActor = bindactor; }
	short getBedStatus() { return m_ActorStatus; }
	void setBedStatus(short status); //设置的同时，播放特效；enterWorld的时候播一次，leaveWorld的时候清理
	void updateBedEffect();
	void stopBedEffect();
	void stopBedEffectByBlockdata(int blockdata); //考虑床敲掉的时候会先清了blockdata，所以多封装一个接口
	//tolua_end
	const char* getContainerName() const override {
		return "WorldBed";
	}
private:
	WCoord getEffectPos(int blockdata);

public:
	//tolua_begin
	WORLD_ID m_BindActor;
	short m_ActorStatus;
	//tolua_end
private:
	int m_desertSleepCount;   //缓存用不需要持久化（时间短）
};//tolua_exports


/**********************************************************************************************
类    名：WorldVillageTotem
功    能：村庄图腾 激活村庄
********************************************************************************************* */
class EXPORT_SANDBOXENGINE WorldVillageTotem : public WorldContainer//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	WorldVillageTotem();
	WorldVillageTotem(const WCoord& blockpos);
	virtual ~WorldVillageTotem() {};
	virtual int getObjType() const
	{
		return OBJ_TYPE_BOX;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerVillageTotem;
	}

	virtual bool load(const void* srcdata);
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	void setOwnerUin(unsigned int uin) { m_OwnerUin = uin; }
	unsigned int getOwnerUin() { return m_OwnerUin; }
	virtual void enterWorld(World* pworld);
	virtual void leaveWorld();
	//tolua_end
	const char* getContainerName() const override {
		return "WorldVillageTotem";
	}
private:
	WCoord getEffectPos();
};//tolua_exports

/**********************************************************************************************
类    名：WorldVillagerFlag
功    能：村民旗帜 分配工作点
********************************************************************************************* */
class EXPORT_SANDBOXENGINE WorldVillagerFlag : public WorldContainer//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	WorldVillagerFlag();
	WorldVillagerFlag(const WCoord& blockpos);
	virtual ~WorldVillagerFlag() {};
	virtual int getObjType() const override
	{
		return OBJ_TYPE_BOX;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerVillagerFlag;
	}

	virtual bool load(const void* srcdata);
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	void setOwnerUin(unsigned int uin) { m_OwnerUin = uin; }
	unsigned int getOwnerUin() { return m_OwnerUin; }
	virtual void enterWorld(World* pworld);
	virtual void leaveWorld();
	//tolua_end
	const char* getContainerName() const override {
		return "WorldVillagerFlag";
	}
private:
	WCoord getEffectPos();
};//tolua_exports

/**********************************************************************************************
类    名：WorldCanvas
功    能：帐篷 人或者村民躺上面，村名休息绑定点
********************************************************************************************* */
class EXPORT_SANDBOXENGINE WorldCanvas : public WorldContainer//tolua_exports   //not_auto_export_to_lua
{//tolua_exports
public:
	//tolua_begin
	WorldCanvas();
	WorldCanvas(const WCoord& blockpos);
	virtual ~WorldCanvas();
	virtual int getObjType() const
	{
		return OBJ_TYPE_BOX;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerCanvas;
	}

	virtual bool load(const void* srcdata);
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual void enterWorld(World* pworld) override;
	virtual void leaveWorld() override;
	virtual void updateTick() override;
	virtual bool remoteMarkBlockForUpdate() override;

	WORLD_ID getBindActor() { return m_BindActor; }
	void setBindActor(WORLD_ID bindactor) {
		m_BindActor = bindactor;
		refreshBlockMesh();
	}
	short getBedStatus() { return m_ActorStatus; }
	void setBedStatus(short status); //设置的同时，播放特效；enterWorld的时候播一次，leaveWorld的时候清理
	void updateBedEffect();
	void stopBedEffect();
	void stopBedEffectByBlockdata(int blockdata); //考虑床敲掉的时候会先清了blockdata，所以多封装一个接口

	void setStage(char stage)
	{
		m_stage = stage;
		refreshBlockMesh();
	}
	char getStage() { return m_stage; }
	char addStage();
	char subStage();
	void doRandomChangeStage();
	void needPlaySleepEffect(bool isNeed);
	static void whenPlayerWakeup(IClientPlayer* player);
	static bool repair(IClientPlayer* player, World* world, int x, int y, int z);
	//tolua_end
private:
	void refreshBlockMesh();
	WCoord getEffectPos(int blockdata);

public:
	//tolua_begin
	WORLD_ID m_BindActor;
	short m_ActorStatus;
	char m_stage;   //0完整,1有破损,2破得不能用
	//tolua_end
private:
	int m_inDesertWindCount;
	bool m_isPlayEffecting;
	WCoord m_effectPos;
};//tolua_exports

/**********************************************************************************************
类    名：WorldShellbed
功    能：贝壳床 人或者村民躺上面，村名休息绑定点
**********************************************************************************************/
class EXPORT_SANDBOXENGINE WorldShellbed : public WorldContainer//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	WorldShellbed();
	WorldShellbed(const WCoord& blockpos);
	virtual ~WorldShellbed() {};
	virtual int getObjType() const
	{
		return OBJ_TYPE_BOX;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerShellBed;
	}

	virtual bool load(const void* srcdata);
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual void enterWorld(World* pworld) override;
	virtual void leaveWorld() override;
	virtual void updateTick() override;
	WORLD_ID getBindActor() { return m_BindActor; }
	void setBindActor(WORLD_ID bindactor) { m_BindActor = bindactor; }
	short getBedStatus() { return m_ActorStatus; }
	void setBedStatus(short status); //设置的同时，播放特效；enterWorld的时候播一次，leaveWorld的时候清理
	void updateBedEffect();
	void stopBedEffect();
	void stopBedEffectByBlockdata(int blockdata); //考虑床敲掉的时候会先清了blockdata，所以多封装一个接口
	//tolua_end
	const char* getContainerName() const override {
		return "WorldShellbed";
	}
private:
	WCoord getEffectPos(int blockdata);

public:
	//tolua_begin
	WORLD_ID m_BindActor;
	short m_ActorStatus;
	//tolua_end
private:
	int m_ShellbeddesertSleepCount;
};//tolua_exports
extern int gCurveVertCuboidArray;

//风铃
class EXPORT_SANDBOXENGINE WorldBells : public WorldContainer
{
public:
	WorldBells();
	WorldBells(WCoord BlockPos);
	~WorldBells();
	virtual int getObjType() const override
	{
		return OBJ_TYPE_BOX;
	}
	virtual void enterWorld(World* pworld) override;
	virtual void leaveWorld() override;
	virtual void updateTick() override;
	virtual void updateDisplay(float dtime) override;

	void playAnim(int animId, int times = -1);
	void stopAnim();
	bool hasAnimPlaying(int animId);
	void playSound(World* pworld, const char* name);
	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerShells;
	}
	virtual bool load(const void* srcdata);
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);

private:
	Rainbow::Entity* m_pEntity;
	int m_curAnimId;
};

class EXPORT_SANDBOXENGINE WorldContainerEx : public WorldContainer
{ 
public:
	
	WorldContainerEx() :WorldContainer() {};
	WorldContainerEx(const WCoord& blockpos) :WorldContainer(blockpos, 0) {};
// 	virtual ~WorldContainerEx();

	virtual void updateTick() override final {};

};

class EXPORT_SANDBOXENGINE AirDropStorageBox : public WorldStorageBox
{ 
public:
	AirDropStorageBox() :WorldStorageBox(), m_chestId(0){}
	AirDropStorageBox(const WCoord& blockpos) :WorldStorageBox(blockpos), m_chestId(0) {}
	virtual ~AirDropStorageBox() {}

	virtual bool doOpenContainer() override;
	virtual int getObjType() const override;

	void setChestId(int chestId) { m_chestId = chestId; };
	int getChestId() { return m_chestId; };

private:

	int m_chestId{0};

};

#endif
