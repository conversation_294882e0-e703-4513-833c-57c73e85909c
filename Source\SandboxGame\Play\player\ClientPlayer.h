#ifndef __CLIENTPLAYER_H__
#define __CLIENTPLAYER_H__

#include "PlayerAnimation.h"
#include "MotionControl.h"
#include "PlayerStateTrigger.h"
#include "OgreTimer.h"
#include "ClientAccountInfo.h"
#include "Core/worldData/world_struct.h"
#include "Core/driver/scene/SandboxSceneObject.h"
#include "TaskData.h"

#include "WorldRole_generated.h"
#include "TaskData.h"
#include "IClientPlayer.h"
#include "actors/generalActor/ClientActorLiving.h"
#include "CoreCommonDef.h"
#include "proto_common.pb.h"
#include "Core/blocks/container_world.h"
#include "Core/worldData/ChunkViewer.h"
#include "Core/worldData/worldDef.h"
#include "SandboxCallback.h"
#include "SandboxGame.h"
 
class SleepState;
namespace miniw {
	class tech_tree;
	class tech_tree_node;
}

namespace Rainbow {
	namespace UILib {
		class ModelView;
	}
	class IActorBody;
}

namespace MNSandbox
{
	class Component;
}
class MpActorTrackerEntry;
class EffectDestroyBlock;
class ActorContainerMob;
class ActorHorse;
class BackPack;
class WorldContainer;
class ChunkIOMgr;
class PlayerTaskManager;
class ActorVehicleAssemble;
class ActorInPortal;
class PlayerAttrib;
class TotemComponent;
class AttackingTargetComponent;
class HPProgressComponent;
class TeamComponent;
class PlayerTeamComponent;
class ActorUpdateFrequency;
class OpenContainerComponent;
class ItemSkillComponent;
class TransformersSkinComponent;
class WeaponSkilledComponent;
class AvatarSummonComponent;
class ChargeJumpComponent;
class PlayerCheatData;
class PlayerStateController;
class PlayerState;
class CameraModel;
class ClientMob;
class MoveControl;
class GunUseComponent;
class CustomGunUseComponent;
class ItemUseComponent;
class ActionIdleStateGunAdvance;
class ModelItemMesh;
class InteractTamedMobComponent;
class PetFollowListComponent;
class SkillCDComponent;
class TransformersSkinComponent;
class VacantEffectComponent;
class TemperatureComponent;
class AccountHorseComponent;
class ChangeColorComponent;
class PetAccountComponent;
class PetSummonComponent;
class FishLineComponent;
class CraftingQueue;

#ifdef IWORLD_SERVER_BUILD
namespace miniw {
	class pb_pvp_activity_player;
}
class GridVisitor;
#endif
#define _UPDATE_BOUND_BY_SCALE_ //跟随玩家尺寸更新碰撞盒

namespace Rainbow
{
	class ISound;
	class Vector3f;
	class Transform;
}

////tolua_begin
//enum
//{
//	PLAYEROP_NULL = 0,
//	PLAYEROP_ATTACK_BOW,
//	PLAYEROP_EATFOOD,
//	PLAYEROP_DIG,
//	PLAYEROP_USE_ITEM_SKILL,
//	PLAYEROP_CATCH_BALL,
//	PLAYEROP_SHOOT,
//	PLAYEROP_PASS_BALL,
//	PLAYEROP_TACKLE,
//	PLAYEROP_TACKLE_END,
//	PLAYEROP_BALL_CHARGE_BEGIN,
//	PLAYEROP_THROW_GRAVITYACTOR,
//	PLAYEROP_CATCH_GRAVITYACTOR,
//	PLAYEROP_GRAVITY_CHARGE_BEGIN,
//	//篮球部分
//	PLAYEROP_BASKETBALL_BLOCK_SHOT,   //盖帽
//	PLAYEROP_BASKETBALL_BLOCK_SHOT_END,
//
//	PLAYEROP_BASKETBALL_OBSTRUCT,
//	PLAYEROP_BASKETBALL_OBSTRUCT_END,
//	PLAYEROP_BASKETBALL_GRAB,
//	PLAYEROP_BASKETBALL_GRAB_END,
//
//	PLAYEROP_BASKETBALL_DRIBBLERUN,
//	PLAYEROP_BASKETBALL_DRIBBLERUN_END,
//	PLAYEROP_BASKETBALL_PASS,
//	PLAYEROP_BASKETBALL_PASS_END,
//	PLAYEROP_BASKETBALL_SHOOT,
//	PLAYEROP_BASKETBALL_SHOOT_END,
//	PLAYEROP_BASKETBALL_CHARGE_BEGIN,
//	PLAYEROP_SHEILD_DEFENCE_BEGIN,
//
//	PLAYEROP_PUSHSNOWBALL_CHARGE_BEGIN,
//	PLAYEROP_PUSHSNOWBALL_SHOOT,
//	PLAYEROP_PUSHSNOWBALL_SHOOT_END,
//	PLAYEROP_PUSHSNOWBALL_MAKEBALL,
//	PLAYEROP_PUSHSNOWBALL_MAKEMAN,
//	PLAYEROP_PUSHSNOWBALL_JUMP,
//
//	PLAYEROP_STATUS_BEGIN = 0,
//	PLAYEROP_STATUS_END,
//	PLAYEROP_STATUS_CANCEL
//};
//
//enum
//{
//	FREEZING_STATE_CLEAN = 0,
//	FREEZING_STATE_CANMOVE,
//	FREEZING_STATE_NOMOVE,	//禁锢状态
//};
//
//enum MotionState
//{
//	MOTION_STATIC = 0,
//	MOTION_MOVE = 1,
//	MOTION_RUN = 2,
//	MOTION_JUMP = 4,
//	MOTION_TWOJUMP = 8,
//	MOTION_SNEAK = 16,
//	MOTION_FALLGROUND = 32,
//};
//
//enum BasketballFall
//{
//	NOT_HAS_TARGET = -1,
//	BEFORE_DESTION,
//	HIT_DESTION,
//	BEHIND_DESTION
//};
//tolua_end
//tolua_begin
struct SocAttackInfo {
	ATTACK_TYPE atktype;
	int buffId;
	int buffLevel;
	int toolid;
	int playerid;
	int length;
	int mobid;
	uint64_t survival_time;
	void clean() {
		atktype = (ATTACK_TYPE)-1;
		buffId = 0;
		buffLevel = 0;
		toolid = 0;
		playerid = 0;
		length = 0;
		mobid = 0;
		survival_time = 0;
	}

	std::string GetSurvivalStr()
	{
		//存活时间时间低于一小时，则显示XmYs;超过1小时，低于1天，则显示XhYm,超过1天，则显示XdYh；
		int64_t seconds = survival_time;
		int64_t minutes = seconds / 60;
		int64_t hours = minutes / 60;
		int64_t days = hours / 24;
		seconds %= 60;
		minutes %= 60;
		hours %= 24;
		if (days > 0)
		{
			return std::to_string(days) + "d" + std::to_string(hours) + "h";
		}
		else if (hours > 0)
		{
			return std::to_string(hours) + "h" + std::to_string(minutes) + "m";
		}
		else
		{
			return std::to_string(minutes) + "m" + std::to_string(seconds) + "s";
		}
	}
};
//tolua_end

//tolua_begin
struct TackleEffectData
{
	int ticks;
	EffectDestroyBlock* effect;
};

//struct SummonPetInfomation
//{
//	int monsterid;
//	int petid;
//	int stage;
//	int quality;
//	SummonPetInfomation() : monsterid(0), petid(0), stage(0), quality(0)
//	{
//	}
//};
//tolua_end

#ifdef DEDICATED_SERVER
struct PvpActivityConfig
{
	PvpActivityConfig()
	{
		activityId = 0;
		start_time = 0;
		end_time = 0;
		timeZone = 0;
	}
	uint32_t activityId;
	uint32_t start_time;
	uint32_t end_time;
	int32_t timeZone;
	std::string commParam;
	std::set<int> itemNeedRecord;
	std::set<int> mobNeedRecord;
};
#endif // DEDICATED_SERVER

enum SET_PLAYER_GAME_INFO_TYPE {
	SPGI_SCORE = 1,
	SPGI_RESULT = 2,
	SPGI_RANKING = 4,
	SPGI_ALL = 0xff
};

struct SnakeGodWingState
{
	float wingFlyTime;
	float wingForwardSpeed;
	int wingState;
	SnakeGodWingState() :wingFlyTime(0.0f), wingForwardSpeed(0.0f), wingState(0) {}
};

#define TACKLE_INITIAL_VELOCITY 25
extern WCoord GetBlockFaceCenter(const WCoord& blockpos, DirectionType dir);
extern bool CanActivateBlock(int blockid, int toolid);
class EXPORT_SANDBOXGAME ClientPlayer;
class ClientPlayer : public ActorLiving, public IClientPlayer //tolua_exports
{ //tolua_exports
	DECLARE_SCENEOBJECTCLASS(ClientPlayer)
public:
	//tolua_begin
	ClientPlayer();

	void createEvent();//事件注册
#pragma region IClientPlayer

	virtual IClientActor* CastToActor()
	{
		return dynamic_cast<IClientActor*>(this);
	}
	virtual IClientActor* GetCurAccountHorse() override;

	virtual MNSandbox::SandboxNode* CastToNode() override
	{
		return dynamic_cast<MNSandbox::SandboxNode*>(this);
	}
	virtual ClientPlayer* GetPlayer() override
	{
		return this;
	}
	virtual World* GetPlayerWorld() override { return getWorld(); }
	virtual long long iGetObjId() override
	{
		return getObjId();
	}
	virtual int iGetTeam()
	{
		return getTeam();
	}
	virtual std::set<int>& GetDirtyGridIndex() override
	{
		return mDirtyGridIndex;
	}
	virtual void SetPlayerTeam(int id) override
	{
		setTeam(id);
	}
	virtual WCoord& iGetPosition()
	{
		return getPosition();
	}
	virtual void SetPlayerPosition(const WCoord& position) override
	{
		setPosition(position);
	}
	virtual unsigned short iGetCurMapID()
	{
		return getCurMapID();
	}

	virtual Rainbow::IActorBody* iGetBody();
	virtual IActorLocoMotion* GetPlayerLocoMotion() override;
	virtual void GetPlayerFaceDir(float& x, float& y, float& z) override
	{
		getFaceDir(x,y,z);
	}
	virtual int GetPlayerCurPlaceDir() override
	{
		return getCurPlaceDir();
	}
	virtual bool BlockBookCabinetVehicleDir(Rainbow::Vector3f& viewDir, Rainbow::Vector3f& centerDir, World* pworld, const WCoord& blockpos) override;
	virtual void PlayerWakeUp(bool immediately, bool updateallflag, bool setrevive) override
	{
		wakeUp(immediately, updateallflag, setrevive);
	}
	virtual bool GetGunZoom() override;
	virtual void doWaterCanoonSkill() override;
	virtual int getEquipItem(EQUIP_SLOT_TYPE t) override;
	virtual BackPackGrid* getEquipGrid(EQUIP_SLOT_TYPE t) override;
	virtual BackPackGrid* getEquipGridWithType(EQUIP_SLOT_TYPE t) override;
	virtual int damageEquipItem(EQUIP_SLOT_TYPE t, int damage) override;
	virtual IClientActor* GetCatchBall() override;
	virtual IClientActor* GetPlatformIClientActor() override;

	virtual void SetMoveForward(float value) override
	{
		m_MoveForward = value;
	}
	virtual void SetMoveRight(float value) override
	{
		m_MoveRight = value;
	}
	
#pragma endregion

	virtual bool init(int uin, const char* nickname, int playerindex, const char* customjson);
	virtual bool initAvatar();	//模型加载完 进入游戏才能调用avatar 模型初始化
	virtual void initCusMotion();
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder) override;
	virtual bool load(const void* srcdata, int version) override;
	virtual bool needSaveInChunk() override
	{
		return false;
	}

	void UseSelectTechBlueprints();

	virtual void replacePlayer(WCoord pos = WCoord(0, -1, 0));
	virtual bool mountActor(ClientActor* actor, bool isforce = false, int seatIndex = -1, bool bcontrol = true);
	//tolua_end

	//tolua_begin
	virtual bool carryActor(ClientActor* actor, WCoord pos = WCoord(0, -1, 0));
	void ReqAllSingleBuildData();
	//virtual void setRidingActor(ClientActor *p, bool isleave = false) override;
	bool isFishNeedUp();
	virtual void enterWorld(World* pworld);
	virtual void leaveWorld(bool keep_inchunk);
	virtual void tick();
	virtual void update(float dtime)override;
	virtual void renderUI();
	virtual void onCollideWithPlayer(ClientActor* player)override;
	virtual void onCollideWithActor(ClientActor* pActor)override;
	virtual bool applyActorElasticCollision(ClientActor* actor)override;
	virtual bool applyActorObstructCollision(ClientActor* actor, ClientActor* player)override;

	virtual void OnPlayerCustomMessage(int type,const std::string& data);

	virtual int getObjType() const override;
	virtual void onSetCurShortcut(int i);
	virtual int getEyeHeight();
	virtual int getChestHeight();
	//virtual ActorInWater* getActorInWater()override;

	bool ProcessDie() override;
	virtual void onDie();
	virtual bool attackedFrom(OneAttackData& atkdata, ClientActor* inputattacker);
	void updataLastAttackDataInfo(OneAttackData& atkdata, ClientActor* inputattacker);
	void obstructActorCollision(ClientPlayer* player);
	void obstructActorsCollision(ClientPlayer* player);
	virtual bool isInvulnerable(ClientActor* attacker);

	//空间管理 update tick 时的顺序  actor->player->boss
	virtual int GetSpaceTickPriority() {
		return 1;
	}

	virtual void addAchievement(int flags, ACHIEVEMENT_TYPE achievetype, int target_id = 0, int num = 1);
	//flags = 1: 成就，  2: 总的统计,  3: 两者都有
	//type:活动类型，id：生物id或道具id等，value：进度值
	void addSFActivity(int type, int id, int value, bool needSync);
	void attrShapeShiftTick();// 属性变身系统的tick
	void attrShapeShiftAttackedFrom(OneAttackData& atkdata, ClientActor* inputattacker);
	//	更新任务系统进度
	virtual void updateTaskSysProcess(TASKSYS_TYPE type, int target1 = 0, int target2 = 0, int goalnum = 1);
	virtual void addOWScore(float score) override
	{
	}
	virtual void checkNewbieWorldProgress(int curprogress, const char* name)
	{
	}
	virtual void checkNewbieWorldProgress(int curLv, int curStep)
	{
	}
	virtual void checkNewbieWorldTask(int itemid)
	{
	}
	virtual bool hasUIControl()
	{
		return false;
	}
	virtual bool checkGameRule(ActorLiving* atkliving);
	virtual void setFaceYaw(float yaw, bool needSync = false) override;
	virtual void checkCollideOnTrigger(ClientActor* actor);
	bool attackedFromTypeByPlayer(ATTACK_TYPE atktype, float atkpoints, ClientActor* attacker = NULL);
	bool attackedVehicleFromTypeByPlayer(ATTACK_TYPE atktype, float atkpoints, CollideAABB& box, ClientActor* attacker = NULL);
	virtual void tryMoveTo(int x, int y, int z, float speed, bool canControl, bool needSync = false, bool showtip = false) override;

	bool isPCControl()
	{
		return m_UIControlMode == 1;
	}
	void setUIControlMode(int m)
	{
		m_UIControlMode = m;
	}
	int getThrowItemHeight()
	{
		return int(getEyeHeight() * 0.8f);
	}
	//	bool checkCanDigBlcok(const WCoord &targetblock, int status, DIG_METHOD_T digmethod);
		//virtual bool interactActor(ClientActor *target, bool forceattack = false);
		//interactType: 0-attack,1-check interact then attack 2-only interact
	virtual bool interactActor(ClientActor* target, int interactType = 1, bool interactplot = false);
	//参数 onlyCheckAtk: 只检测能否攻击
	virtual bool performInteractActor(ClientActor* target, int interactType, bool interactplot, bool onlyCheckAtk);
	bool canAttack() override;
	bool attackActor(ClientActor* target, int seq = 2, int targetIndex = 1)override;
	virtual bool interactBlock(const WCoord& targetblock, DirectionType targetface, const Rainbow::Vector3f& colpoint);
	virtual bool TryPlaceBlueprintBlock(int blueprintId, const WCoord& blockpos, DirectionType targetface, const Rainbow::Vector3f& colpoint);
	//	virtual bool digBlock(const WCoord &targetblock, DirectionType targetface, int status, DIG_METHOD_T digmethod=DIG_METHOD_NORMAL);
	// 添加在头文件的适当位置（public部分）
	virtual bool attackBlock(const WCoord& blockpos, DIG_METHOD_T dgmethod, ATTACK_TYPE attacktype = ATTACK_PUNCH);
	//避免与以前的代码发生冲突，单独为载具添加的挖方块接口 @tangjie
	void destroyBlockWithVehicle(const WCoord& blockpos, DIG_METHOD_T dgmethod, bool destroy_effect, bool gamerule_forbid, ActorVehicleAssemble* pVeh);

	virtual bool recoverBlock(const WCoord& targetblock, DirectionType targetface, int status);
	virtual bool farmOpenBlock(const WCoord& targetblock, DirectionType targetface, int status);

	//检测一定范伟内的商人npc,给距离最近的商人头上加上一个图标

	bool refreshBusinessmanHeadIcon();
	bool checkIsBusinessman(ClientActor* actor);
	void resetBusinessIcon();

	//开垦耕地 土坑 code-by:liwentao
	//virtual bool exploitBlock(const WCoord &targetblock, DirectionType targetface, int status, int pickType);
	virtual bool interactHorse(ActorHorse* horse, bool onshift = false);
	virtual bool useItemSkill(int itemid, int status, int skillid, Rainbow::Vector3f& currentEyePos, Rainbow::Vector3f& currentDir, std::vector<WCoord>& blockpos, std::vector<WORLD_ID>& obj, WCoord& centerPos, std::string clientParam = "");
	virtual bool doSpecialSkill();
	//void useItemSkillOper(int itemid, int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir, std::vector<WCoord> &blockpos, std::vector<WORLD_ID> &obj, WCoord &centerPos);
	bool canShowInteract(int type, int id, bool ignorecondition = false);

	//IClientPlayer
	virtual WCoord getCurOperatePos(int operatedistance = 5) override;

	virtual void showSpecailTryShapeAnim();
	virtual void hideSpecailTryShapeAnim();
	/*
		展示新的ShapeAnimEntity
	*/
	void showSpecailTryShapeAnimForActorBody();
	/*
		隐藏ShapeAnimEntity
	*/
	void hideSpecailTryShapeAnimForActorBody();
	void setSpecialShapeNameHeight(float height);
	//	virtual void doKickBall(int type, float charge, ClientActor *ball);
	//	virtual bool doCatchBall(ClientActor *ball);
	virtual ClientActor* getCatchBall();
	//	virtual void beginChargeKickBall();
	//	virtual bool doTackle();
	//	virtual void endTackle();

		//virtual bool basketBallOPStart(int type, ClientActor* ball);
		//virtual void basketBallOPEnd(int type, ClientActor* ball);
	//	virtual void doBasketBallGrabMove();
		//virtual bool doBlockShot();
	//	virtual bool doRunDribbleRunBasketBall(ClientActor* ball);
	//	virtual void beginChargeThrowBall();
	//	virtual void doKickBasketBall(int type,BasketballFall result, float charge, ClientActor* ball,const WCoord& pos,float cameraYaw, float cameraPitch,int selectedActorUin = -1);

	virtual void doThrowGravityActor(int type, float charge, ClientActor* actor);
	void throwGravityActor(int type, float charge, ClientActor* actor);
	virtual bool doCatchGravityActor(ClientActor* actor, int bindDistance = 0, int bindHeight = 0);
	virtual ClientActor* getCatchGravityActor();
	virtual void beginChargeThrowGravityActor();
	virtual void doPutGravityActor(ClientActor* actor);

	virtual bool attackRangedFree(int status);
	virtual bool attackCharge(int status);
	//	virtual bool eatFood(int itemid, int status);
	virtual bool SheildDefence(int itemId, int status);


	virtual bool openPlotDialogue(ClientMob* mob, int itemid = 0, WCoord pos = WCoord(0, 0, 0), int type = 0);
	virtual void closePlotDialogue();

	virtual void clearTrackerEntry();
	virtual void addTrackerEntry(MpActorTrackerEntry* entry);
	virtual std::vector<MpActorTrackerEntry*>& getTrackerEntrys();
	//配置直接生效对话系统
	bool interactDialogueDirectUse(int type, int interactID, WCoord blockpos = WCoord(0, -1, 0));

	void setCurInteractPlotType(int type)
	{
		m_curInteractPlotType = type;
	}
	int  getCurInteractPlotType()
	{
		return m_curInteractPlotType;
	}
	/*
	@brief	聊天框隐藏命令
	 */
	virtual bool execCmd(const char* cmdstr) override;

	virtual ActorBody* newActorBody();
	virtual void setVipInfo(const VipInfo& vipinfo);
	virtual const VipInfo& getVipInfo();
	virtual void setGVoiceInfo(int speakerswitch);

	virtual int getMass();
	int checkPlayerReviveStar();
	void setPlayerReviveStar();
	virtual void tickOperate();
	virtual int getCurOperate()
	{
		return m_CurOperate;
	}
	long long getOperrateData() const
	{
		return m_lOperrateData; //kick ball:objid
	}

	PlayerStateManager& getPlayStateManager()
	{
		return m_PlayerStateMgr;
	}
	int getOperateTicks()
	{
		return m_OperateTicks;
	}
	int getOperateTotalTicks()
	{
		return m_OperateTotalTicks;
	}
	int getOperateData()
	{
		return m_OperateData;
	}
	void endCurOperate(long long vehID = 0);
	virtual void onOperateEnded();

	virtual void onPickupItem(ClientActor* item);
	virtual ATTACK_TARGET_TYPE getAttackTargetType()
	{
		return ATTACK_TARGET_PLAYER;
	}
	virtual void calFallMotion(float motiony, bool onground);

	virtual int getViewDist() { return 6400; }
	virtual bool canControl();
	virtual void setCanControl(bool b);
	bool isSleepAndNeedHide();

	bool getCanSwimming()
	{
		return true;
	}

	// @param:sync2Client是否同步给客户端 默认不同步
	void gotoPos(World* pworld, const WCoord& pos, bool sync2Client = false);
	void gotoBlockPos(World* pworld, const WCoord& blockpos, bool random_offset);
	virtual void gotoTeleportPos(World* pworld, const WCoord& blockpos, WCoord& realpos);
	virtual void gotoTransferPos(World* pworld, const WCoord& blockpos);
	virtual void gotoSpawnPoint(World* pworld, bool bRandom = true);
	void notifyUseItem2Tracking(int itemid, int status, bool onshift = false);
	void gotoBlockPosAllWorld(int targetMapid, const WCoord& blockpos, bool random_offset);
	//void notifyUseItemSkill2Tracking(int itemid, int status, int skillid);

	//WCoord getRevivePoint();
	//WCoord getTriggerRevivePoint()
	//{
	//	return m_TriggerRevivePoint;
	//}

	// 20211103 同步位置到客户端 codeby:liusijia
	void syncPos2Client(bool sync_motion = false);
	void onClientRspSyncPos(unsigned long long tick);

	//sleep;
	virtual int sleepInVehicleBed(const WCoord& blockpos, World* pworld) override;
	int sleepInVehicleBed(const WCoord& blockpos, ActorVehicleAssemble* assemble);
	void playerTrySleep();
	bool isRestInBed();
	bool isSleeping();
	virtual int sleep(const WCoord& blockpos, bool refreshRevivePoint = false) override;
	//IClientPlayer
	virtual int sleepInBed(const WCoord& blockpos, bool refreshRevivePoint = true) override;
	void wakeUp(bool immediately, bool updateallflag, bool setrevive);


	int sitInChair(const WCoord& blockpos);
	// 此方法的坐标blockpos是*BLOCK_SIZE的, 目标targetpos没有*BLOCK_SIZE
	int sitInChairEX(const WCoord& blockpos, const WCoord& targetpos = WCoord(0, 0, 0));
	int sitInEmitter(const WCoord pos, const Rainbow::Vector3f& euler);
	void standUpFromChair();
	virtual void tryStandup() { standUpFromChair(); };
	bool isSittingInChair();
	bool isSittingInStarStationCabin();
	bool isStandOnVoidMushroom();
	bool isSittingInPianoChair();
	bool getUsingPianoPos(int& outx, int& outy, int& outz);


	// 使用灶台烹饪
	//int cookByHearth(const WCoord &blockPos);
	virtual void teleportMap(int targetmap);
	void transferMap(int targetmap, int destStarStationId, WCoord& pos);
	virtual void teleportRidingRocket(int targetmap) override;
	void teleportByRocket(int targetmap);
	virtual WCoord getRealLandingPoint(int targetmap, World* pworld);
	virtual bool managedByChunk()
	{
		return false;
	}
	virtual bool isDead();
	PLAYER_GENIUS_TYPE getGeniusType();
	float getGeniusValue(PLAYER_GENIUS_TYPE t, float* extvalue = NULL);

	ActorHorse* getFacedHorse(); //得到眼前的马
	GunUseComponent* getGunLogical();
	virtual void changeRoleData(game::common::PB_RoleData* pRoleData) override;
	void reStoreRoleData(const PB_RoleData& roleData);
	bool SetLevelMode(int nSumExp, int nCurExp, int nCurLevel);	//从fb加载等级经验
	void syncLevelMode();	//同步等级信息给客机
	//void syncRevivePoint(int uin);	//同步复活点给客机
	//void sendRevivePoint(int uin, int mapid, WCoord& spawnpoint, WCoord& revivepoint);	//发送复活点给客机

	void UpgradeForTrigger();//触发器事件: 玩家升级
	bool loadFromFile(long long owid, int uin, bool needload = true, int specialType = NORMAL_WORLD);
	bool loadFileByToggleGameMakerMode(long long owid, int uin, bool needload = true, int specialType = NORMAL_WORLD);
	bool loadFromDataServer(long long owid, int uin, void* buf, int buflen, bool needload = true, int specialType = NORMAL_WORLD);
	bool isHaveRoleFile(long long owid, int uin, int specialType = NORMAL_WORLD);
	/**
	@brief 保存本地地图
	*/
	virtual bool saveToFile(long long owid = 0, ChunkIOMgr* iomgr = NULL);

	virtual void applyEquips(EQUIP_SLOT_TYPE t = MAX_EQUIP_SLOTS);
	void applyEquipsOnShortcut(EQUIP_SLOT_TYPE t = MAX_EQUIP_SLOTS, bool takeoffAble = true);
	virtual void onDisApplyEquips(int fromid, int toindex, int itemid, int num);	//卸载装备栏装备回调

	//装备
	virtual void onDisApplyEquips(int itemid);	//卸载装备回调
	virtual void onApplyEquips(int itemid, int grid_index = 0);		//装载装备回调
	void updateEquips();//切换游戏模式时, 刷新身上的装备
	void discardEquip(int index, int num = 1);
	void doEquipModEntry(int itemid, bool enable);
	//显示、隐藏开关
	void SetHideEquipAvatar(bool bShow);
	bool GetHideEquipAvatarState();

	void onSendChat(const std::string& content);				//游戏内发送消息回调
	virtual void doActualRangeAttack(ClientActor* target) override;
	//virtual void doActualItemSkillRangeAttack(ClientActor *target, int itemid, float ChargeMove);
	//virtual void doActualItemSkillAttack(ClientActor *target, ATTACK_TYPE type, int demage);

	void rangeAttackWithPower(int toolId, float shotPower);
	//void itemSkillAttackWithPower(int skillId, float shotPower);
	virtual void doGunFire(int id);
	virtual void doReload(int bulletid, int num, bool isCustomGun = false, int checkShortcut = -1);
	virtual void doReloadWithoutCheck(int num, int checkShortcut = -1);
	bool canHurtActor(ClientActor* target); //target==NULL返回true
	//IClientPlayer
	virtual long long getOWID() override;

	void updateChunkView();
	//探索周围chunk区域
	void exploreChunkView(int range, int blockType);
	virtual ChunkViewer* getChunkViewer()
	{
		return &m_ChunkViewer;
	}
	void addMoveStats(const WCoord& dp);
	void kickBall(int type, float charge, ClientActor* ball);
	//	void kickBasketBall(int type,BasketballFall result, float charge, ClientActor* ball,const WCoord& pos, float cameraYaw, float cameraPitch,int selectedActorUin = -1);
	void kickPushSnowBall(int type, float charge, ClientActor* ball);

	void defeatActorOnTrigger(long long objid, int actorid = 0);
	void attackOnTrigger();
	void attackHitOnTrigger(long long objid, int actorid = 0);

	virtual void beHurtOnTrigger(float hp);
	virtual void moveOneBlockSizeOnTrigger();
	void damageActorToTrigger(long long objid, int actorid = 0, int hurtlv = 0, bool isAttackHead = false, bool ignoreTrigger = false); //受伤害id 攻击id 受到的伤害值
	void playActionOnTrigger(int act, int actTrigger = 0); // act是animact.csv表格，actTrigger是triggeract.csv表格
	void useItemOnTrigger(int itemid);
	void mountActorOnTrigger(long long objid, int actorid = 0);
	void dismountActorOnTrigger(long long objid, int actorid = 0);
	void pickUpItemOnTrigger(long long objid, int itemid, int itemnum);
	void gridChangeOnTrigger(int index, int itemid, int itemnum);
	void addItemOnTrigger(int itemid, int itemnum);
	void selectShortcutOnTrigger(int index);
	void motionStateChangeOnTrigger(MotionState state);
	void clickActorOnTrigger(long long objid, int actorid = 0);

	void clickBlockOnTrigger(int blockid, WCoord& pos);
	void DoMtlClickByActor(bool& bInterrupt);
	void DoMtlClickByActorNotify();
	//void setRevivePoint(const WCoord *pos, bool forced, bool bSetWorldMgr = true);
	/*
		对应的功能：任意玩家属性变化
		观察者事件接口
	*/
	void attriChangeOnTrigger(std::vector<int>& attri);
	void applyEquipsOnTrigger(int itemid, EQUIP_SLOT_TYPE t);							//观察者事件接口:对应功能:任意玩家穿上装备{param1 ,装备的具体id ,param2 装备栏的位置 如头部}
	//开发者用接口
	virtual bool playActForTrigger(int act);						//播放动作

	bool mountActorForTrigger(long long objid, int posindex, bool bcontrl);		//乘坐动物
	void discardItem(int index, int num);							//丢弃道具
	virtual void createPolaroidPhotoItem(string userdata); //创建拍立得相片道具
	void createPolaroidPhotoDropItem(std::string userdata); //创建相片掉落物
	void downloadPolaroidPhoto(std::string userdata);//去下载拍立得图片

	SocAttackInfo& getSocAttackInfo() { return m_socAttackInfo; };
	virtual bool revive(int reviveType = 0, int x = 0, int y = -1, int z = 0); //0回到出生地复活，1原地复活
	uint64_t getSurvivalTime() const;		//获取玩家当前存活时间 秒数
	void reviveEquipEffect(int reviveType = 0);
	void consumeItemOnTrigger(int itemid, int num);
	void gotoPos(World* pworld, int x, int y, int z)
	{
		gotoPos(pworld, WCoord(x, y, z));
	}
	void gotoPosEx(World* pworld, int x, int y, int z);

	void teleportPos(int x, int y, int z);

	WCoord findNearestVerticalReviveBlockPos(World* pworld, const WCoord& startblock, int radius); //指定半径向上查找复活点

	void setRevivePoint(int x, int y, int z);//有开放给lua 需保留
	void setTriggerRevivePoint(int x, int y, int z);//有开放给lua 需保留
	virtual void notifyGameInfo2Self(int infotype, int id, int num = 0, const char* name = NULL, const char* buff = NULL) override;
	virtual bool useItem(int itemid, int status, bool onshift = false, unsigned int useTick = Rainbow::Timer::getSystemTick());
	virtual bool usePackingFCMItem(int itemid, WCoord usepos);   //使用微缩组合道具
	void rotateCamera(float yaw, float pitch);
	void setCameraLerpSpeed(float speed);
	virtual bool checkActionAttrState(int actionattr) override;
	bool isEquipByResID(int resid);

	virtual bool getAimPos(int& outx, int& outy, int& outz, int distance = 20, bool calibration = false);
	Rainbow::Vector3f getBodyDir();
	virtual Rainbow::Vector3f getCameraLookDir();
	void changeViewMode(int mode, bool lock, bool clientMode = false);
	void GotoTeamPos(int id);
	void setBaseModel(int nTeamId = -1);	//基础设置--玩家属性设置--初始模型
	virtual void changeBaseModel(std::string& strModelId, float fScale);
	void parsePlayerBaseModelID(std::string& strWholeID, std::string& sType, std::string& sID);

	virtual MNSandbox::Component* getPlayerAttribComponent() override;

	PlayerAttrib* getPlayerAttrib()
	{
		return m_PlayerAttrib;
	}
	float getDistanceToPos(int posx, int posy, int posz)
	{
		return Distance(getPosition().toVector3(), WCoord(posx, posy, posz).toVector3());
	}
	virtual int getGameResults();
	int getPlayerGameResults(); //触发器踢出玩家改为设置玩家结果 不附带设置队伍结果
	bool setGameResults(int r);
	void setGameInfo2Client(SET_PLAYER_GAME_INFO_TYPE);
	virtual int getGameScore()
	{
		return m_GameScore;
	}
	void setGameScore(int s);
	void setGameRanking(int r);
	virtual int getGameRanking()
	{
		return m_GameRanking;
	}
	void playCurToolSound();
	virtual bool setJumping(bool b);
	virtual int gainItems(int itemid, int num, int prioritytype = 1, bool save = false);
	virtual int gainItemsByIndex(int gindex, int num, int priorityType = 1, bool ignoreGodMode = true);
	virtual int gainItemsUserdata(int itemid, int num, const char* userdata_str, int prioritytype = 1);
	virtual int gainItemsByGrid(const BackPackGrid& grid, int prioritytype = 1);

	void throwBackpackItem(int backpack_index, int num);
	void teleportHome();
	//20210826:交换背包道具 codeby：wangyu
	virtual int exchangeItems(int costitemid, int costnum, int gainitemid, int gainnum);
	virtual void tryMoveToActor(long long targetobj, float speed);
	virtual void tryMoveToPos(int x, int y, int z, float speed);

	virtual void setMoveForward(float speed);				// 向前移动，>0:向前移动，<0:向后移动
	virtual void setMoveStrafing(float speed);				// 横向移动，>0:向右移动，<0:向左移动
	virtual void onMotionCtrlOver(const MotionCtrlParam& param);	// 用于行动结束回调(这里使用虚函数的方式而非注册函数)
	virtual void walkForward(int param, int exid = 0);				// 向前移动多少格，<0:保持向后移动
	virtual void walkStrafing(int param, int exid = 0);				// 向右移动多少格，<0:保持向左移动
	virtual void jumpOnce(); //跳跃一次

	virtual void postInfoTips(int tip)
	{
	}
	/*
		添加一个获取IBackPack的接口
	*/
	IBackPack* getIBackPack();
	BackPack* getBackPack();
	CraftingQueue* getCraftingQueue();
	void addCurToolDuration(int num);
	void DoCurToolAtkDuration();
	void addCurDorsumDuration(int num);
	int getEquipItemDuration(EQUIP_SLOT_TYPE slot);
	//IClientPlayer
	virtual int getCurShortcut() override;
	virtual int getCurShortcutItemNum() override;
	//获取快捷栏索引
	int getShortcutStartIndex();
	//IClientPlayer
	virtual void shortcutItemUsed(bool ignoreDurable = false) override;
	void shortcutItemAllUsed(int maxUsedNum);
	int removeItemInNormalPack(int itemid, int num);
	virtual int getUin() const;//for minicode
	void setUin(long long uin);
	//IClientPlayer
	virtual const char* getNickname() override;
	bool isComboWeaponInHand(); //是否手持可连击的武器
	virtual int getCurToolID() override;
	int getCurDorsumID();

	bool isSkinning();
	void setSkinning(bool b);

	//	void attachUIModelView(ModelView *modelview, int index=0);
//	void detachUIModelView(int index=0);
	bool isFlying();
	void createEnderEye();
	void createFirework(int firetype, int firedata);
	void callAirDrop(int itemid,int eventId, int x, int y);
	void throwBall(int itemid);
	void throwItem(int itemid, int num);
	//为游商地形图购买(超过背包丢弃),同时带上对应的数据(捡起来的时候能继续使用)
	void throwItemUserData(int itemid, int num, const char* userData);
	//ClientActor *spawnItem(int itemid, int num, int offset=0);
	//IClientPlayer
	virtual int getPlaceDirToBlock(const WCoord& blockpos) override;
	int getPlaceDirToBlockWithWorld(World* pworld, const WCoord& blockpos);
	int getFreeStandPlaceDir(); //15 dirs
	int getSurviveDay(int mapid = -1);
	void setWorldTime(int mapid, int t);
	void sendWorldTimesUpdate();
	virtual bool isHost() override;  //是房主
	virtual bool isRemote() override; // 是不是客机

	virtual void setAccumulatorState(float progress);

	virtual void sortPack(int base_index);
	virtual void sortStorageBox();

	virtual int enchant(int tgtGridIdx, int frmGridIdx, int enchants[MAX_ITEM_ENCHANTS]);
	virtual int enchantRandom(int tgtGridIdx);

	virtual bool canEnchant(int tgtGrid, int frmGrid, int enchants[MAX_ITEM_ENCHANTS]);
	virtual int calcEnchantCost(int tgtGrid, int enchants[MAX_ITEM_ENCHANTS]);

	virtual void setUserDataStr(int tgtGridIdx, std::string txt);
	virtual bool writeLetters(int tgtGridIdx, std::string txt);
	virtual bool writeBlueprint(int tgtGridIdx, std::string txt);		//保存蓝图图纸道具信息
	virtual bool writeInstruction(int tgtGridIdx, std::string txt);
	virtual void writePolaroidAlbumInfo(int gridIndex, std::string jsonStr);

	int storeItem(int frmGrid, int num);

	virtual int repair(int tgtGridIdx);
	std::map<int, int> getRepairCost(BackPackGrid* grid);
	virtual bool canRepair(int tgtGridIdx, const std::map<int, int>& costitems);
	virtual int calcRepairCost(int tgtGridIdx, int materialId, int useNum);
	virtual int NewRepair(int tgtGridIdx, int repairDur, int mat1Id, int mat2Id, int repairCount, int starCount);

	void research();

	void ChangeNameObjHeight();
	void GMChangeSkin(int nType = 1, int nSkinId = 0, const char* file = NULL);
	virtual void changePlayerModel(int playerindex, int mutatemob = 0, const char* customskin = "", const char* custommodel = NULL, int itemid = 0, int blockid = 0, bool force = false);
	virtual void setCustomModelForActorBody(int playerindex, int mutatemob, const char* customskin, const char* custommodel, ActorBody* body = nullptr);
	void changePlayerModelTryOn(int playerindex, int mutatemob = 0, const char* customskin = "", const char* custommodel = NULL, int itemid = 0, int blockid = 0, bool force = false)
	{ 
		ClientPlayer::changePlayerModel(playerindex, mutatemob, customskin, custommodel, itemid, blockid, force); 
	}
	virtual std::string getFullyCustomModelKey();
	virtual void updateBodyByFullyCustomModel();
	virtual void updateBodyByImportModel();

	virtual bool useSpecialItem(int grid_index, int itemId, int num = 1);
	virtual void npcTrade(int op, int index, bool watch_ad = false, int ad_rewardnum = 1); //op==0刷新物品,  1: 兑换index的物品

	virtual void starConvert(int num);

	void setAchieveInfo(bool isUse, const char* texIcon, const char* texFrame);
	void setBPTitleIcon(bool isUse, const char* titleIcon);

	void removeBackpackItem(int itemid, int num);
	virtual void playHurtSound();
	virtual void playStepSound();
	virtual void playBlockPlaceSound(int blockid, int x, int y, int z);
	virtual void playBlockDigSound(int blockid, int x, int y, int z);
	virtual bool placeBlock(int blockid, int x, int y, int z, int face, float facept_x = 0, float facept_y = 0, float facept_z = 0, bool placeinto = false, bool isagainplace = false, int extendData = 0, World* world = NULL);
	virtual void summonAccountHorse(int horseid);
	ClientActor* summonShapeShiftHorse(int horseid);
	ClientActor* getCurAccountHorse();
	void setCurAccountHorse(long long objId);
	void setAccountHorseEquip(int horseid, int index, int itemid);
	int getAccountHorseLiveAge(int horseid);
	void clearAccountHorseLiveAge(int horseid);
	virtual void accountHorseEgg();
	bool isChargeWalkSlow(); //蓄力导致移动速度慢
	int getDieTimes();
	int getLeftLifeNum(); //得到剩余生命条数, 即复活次数
	void addUnlockItem(int itemid);
	virtual bool isUnlockItem(int itemid);
	int selectAllCacheMobsNum(int iMobDefID, int grouptype, int range);
	ClientMob* getCacheMob(int index);
	bool isRidingByHippocompus();
	void setCrabClamp(long long objId) { m_clampId = objId; };
	bool isCrabClamp();
	int addCrabClick();
	void resetClickCount() { m_crabClickCount = 0; };
	int composePlayerIndex(int modelid, int geniuslv = 0, int skinid = 0);
	//avatar summon
	virtual void avatarSummon(int summonid);

	int getSelectedColor() {
		return m_SelectedColor;
	}

	void setSelectedColor(int color);
	bool isPlayerControl();
	void moveItemInner(int fromindex, int toindex, int num);
	virtual PLAYER_SPECTATOR_MODE getSpectatorMode() { return m_nSpectatormode; }
	virtual void setSpectatorMode(PLAYER_SPECTATOR_MODE spectmod);

	virtual PLAYER_SPECTATOR_TYPE getSpectatorType() { return m_nSpectatortype; }
	virtual void setSpectatorType(PLAYER_SPECTATOR_TYPE specttype);

	virtual bool isInSpectatorMode() override { return m_nSpectatormode != SPECTATOR_MODE_NONE; } 
	virtual void setSpectatorUin(int uin) { m_nSpectatorUin = uin; }
	virtual int getSpectatorUin() { return m_nSpectatorUin; }
	virtual int getToSpectatorUin() { return m_nToSpectatorUin; }
	ClientPlayer* getToSpectatorPlayer();
	void setMainPlayerAttrib();

	bool canBePushed();

	virtual int getOPWay()
	{
		return m_OPWay;
	}
	virtual void setOPWay(int way);
	virtual void changeOPWay();
	void updateBallEffect();

	virtual int getCurWorldMapId();
	virtual void setScale(float s);
	virtual float getScale();
	void syncCustomScale();

	bool isRidingMutateFlyHorse();
	float getHorseEnergy();
	bool isHorseTired();

	virtual void newAdNpcAddExp(int op, int starNum); //新版广告商人添加/扣除星星

	// 尝试购买心愿商人道具，逻辑扣除星星或道具
// 如果购买的道具可以放入背包则直接发放，否则通知脚本
// 返回值：0 失败 1 成功 2 等待host处理
	virtual int tryBuyAdNpcGood(int tabId, int goodId);
	// pricetype为迷你币之类的，客户端已支付完成，通知逻辑添加道具
	virtual void onBuyAdNpcGood(int tabId, int goodId);

	// 领取成就任务奖励
	void getAchievementAward(int taskId);

	virtual void AddStar(int starNum);
	virtual void completeTask(int taskid);
	void removeTask(int taskid);
	void addTask(int taskid, int plotid);
	bool hasTask(int taskid);
	void updateTask(PLAYERTASK_TYPE type, int id, int num);
	int getTaskState(int taskid);

	int getTaskNum();
	TaskInfo* getTaskInfo(int taskid);
	TaskInfo* getTaskInfoByPlot(int plotid, int& taskid);
	TaskInfo* getTaskInfoByIndex(int index, int& taskid);
	virtual bool playAct(int act, bool isSwitchViewMode = true);
	virtual void stopAct();

	void onPlayActEvent(int act);

	bool useMagicWand();
	//工具模式
//	bool useTriggerAreaTool();				//使用区域工具
	void gotoBlockPos(WCoord& pos);			//移动到一个方块的位置
	NpcShopDef* getCurShopInfo() { return &m_stCurNpcShopInfo; }

	virtual void closeEditActorModel(int operatetype, std::string modelname = "");
	virtual void closeFullyCustomModelUI(int operatetype, std::string name = "", std::string desc = "");

	void playMusicByTrigger(const char* path, float volume = 1.0f, float pitch = 1.0f, bool isLoop = false);
	virtual void stopMusicByTrigger(const char* path = NULL) override;
	void openDevStoreByTrigger();

	virtual bool isShapeShift();
	virtual int getSkinID() override;

	bool checkCanOpenBackpack();							//开发者:检查是否可以打开背包

	bool isCloudRoomServerOwner();
	void onInputContent(const std::string& content, bool needSync = true);//玩家输入事件
	void setCheckBoxScale(int scale); //设置检测碰撞盒大小范围
	bool openBoxByPos(int x, int y, int z); // 打开某个位置上的方块
	bool forceOpenBoxUI(int itemid); //强制打开某个方块界面
	bool openDevGoodsBuyDialog(int itemid, const char* desc = NULL); //打开开发者商店商品购买弹框

	std::string getCustomModel();
	virtual void setNickName(char* nickname);

	void setNewPlayer(bool flag) { m_NewPlayerFlag = flag; }
	bool isNewPlayer() { return m_NewPlayerFlag; }
	void InteractMobPack(const std::string& name, const std::string& param, long long mobID);
	bool InteractMobItem(int fromIndex, int toIndex);
	void PickMobBackpack(int gridIndex, int moveType = 1, int toGridIndex = -1);
	void resetDeformation(int reason = 1);	//变形后还原模型
	void DeformationSkin(RoleSkinDef* skinDef);
	void resetActorBody();	//变形后还原模型
	bool InTransform();// { return m_bInTransform; }
	bool trySplitDisguise(short id); //准备分裂装扮
	virtual int GetAccountSkinID() { return m_AccoutSkinID; }
	void eraseSkinSubPlayer(long long objid);
	void insertSkinSubPlayer(long long objid);
	void transformSkinReason(int transformReason);

	virtual void summonPet(int monsterid, std::string serverid, int petid, int stage, int quality, std::string petName = "");
	virtual void triggerInputEvent(int vkey, const char* ktype); // For PC/Mobile

	virtual void syncUseItemByHomeLand(int itemid, int num);
	//IClientPlayer
	virtual void notifyOpenWindow2Self(int blockid, int x = -1, int y = -1, int z = -1) override;

	void ReviveCostExp();

	bool IsInteractSpBlockValid(); //有开放给lua 需保留
	//void AddInteractSpBlock(int nBlockID, WCoord point); //lua unused
	//void ClearInteractSpBlock();//lua unused
	void ResetRevivePoint();//有开放给lua 需保留
	//bool GetLastInteractSpBlockList(WCoord & pos);//lua unused

	//钢琴相关从ClientActor移动到角色这边
	void tryStopPianoSoundAndPaticle();//addby rice
	virtual void setPianoSoundName(std::string pSoundName) {
		pianoSoundName = pSoundName;
	}
	virtual void setPianoPaticleName(std::string pPaticleName) {
		pianoPaticleName = pPaticleName;
	}
	virtual void setPaticlePos(WCoord pos) {
		pParticlePos = pos;
	}
	virtual void setPianoSoundPos(WCoord pos) {
		pSoundPos = pos;
	}

	virtual bool isUseHearth()
	{
		return m_IsUseHearth;
	}

	void setUseHearth(bool b)
	{
		m_IsUseHearth = b;
	}

	WCoord& getUsingHearthPos()
	{
		return m_UsingHearthPos;
	}

	void setUsingHearthPos(const WCoord& pos)
	{
		m_UsingHearthPos = pos;
	}


	virtual void setApiid(int apiid)
	{
		m_nApiid = apiid;
	}
	virtual int getApiid()
	{
		return m_nApiid;
	}
	virtual void setLang(int lang)
	{
		m_nLang = lang;
	}
	virtual int getLang()
	{
		return m_nLang;
	}
	virtual void setEnterTs(int ts)
	{
		m_nEnterTs = ts;
	}
	virtual int getEnterTs()
	{
		return m_nEnterTs;
	}

	//修改手持模型贴图 目前花洒用到 code-by:liwentao
	void updateToolModelTexture(int textureIndex = 0, bool sync = true);
	void doSomeChangeAfterSprinklerOnUse();
	void doSomeChangeAfterEmptySprinklerOnUse(int newItem = 0);

	bool isPlayerMove();
	virtual bool isStarStationTeleporting() override;
	virtual void setIsStarStationTeleporting(bool isTeleporting) override;
	virtual bool isExploiting() override;
	void setIsExploiting(bool exploiting);

	virtual void UploadCheckInfo2Host(int infoType, const std::string& awardInfo) {}
	// 请求获取商店累计奖励
	virtual void GetAdShopExtraItemReward(int awardId, int itemId, int count);
	// 提取仓库道具
	virtual void ExtraStoreItem(int storeIndex, int itemId, int count);

	bool isCurrentActionState(const std::string& szState);
	PlayerState* getCurrentActionStatePtr();
	PlayerState* getCurrentActionBodyStatePtr();
	PlayerState* getCurrentMoveStatePtr();

	PlayerState* getActionStatePtr(const std::string& szStateId);
	PlayerState* getActionStatePtr(const char* szStateId);
	PlayerState* getActionBodyStatePtr(const std::string& szStateId);
	PlayerState* getMoveStatePtr(const std::string& szStateId);

	PlayerState* getLocoCurActionStatePtr(const std::string& szStateId);
	PlayerState* getLocoCurActionStatePtr(const char* szStateId);
	PlayerState* getLocoCurActionBodyStatePtr(const std::string& szStateId);

	bool setActionState(const std::string& szStateId);
	bool setMoveState(const std::string& szStateId);

	void toActionState(const std::string& szStateId);
	void toActionBodyState(const std::string& szStateId);

	virtual int getSkinActPlayerNum(); //20210915 codeby: chenwei 获取附近玩家数量
	virtual int getSkinActPlayerUinByIndex(int index); //20210915 codeby: chenwei 通过index索引玩家
	virtual void scanSkinActActorList(int range = 10); //20210915 codeby: chenwei 查找附近玩家
	virtual bool playSkinAct(int act, const int inviteUin, const int acceptUin); //2021-09-14 codeby:chenwe 播放装扮互动动画
	virtual void sendActorInvite(int invitetype, int targetuin, int actid, int inviterPosX = 0, int inviterPosZ = 0); //20210914装扮互动发起 cody-by: wangyu

	virtual void onAnimInterrupt(); //20210929 codeby:chenwei 联机装扮互动动作中断回调接口

	virtual bool checkCanPlaySkinAct(); //2021-09-17 codeby:chenwe 检查玩家状态是否能够播放交互动作
	virtual bool checkHasEnoughSpace2SkinAct(int act); //2021-09-17 codeby:chenwe 是否有足够空间播放装扮互动
	virtual bool checkNearEnough2SkinAct(int targetUin, int act = 0);//2021-09-17 codeby:chenwe 是否距离太远

	virtual bool checkIsDivByBlock(const WCoord& pos1, const WCoord& pos2); //******** codeby:chenwei 测试两个位置是否被方块阻隔
	virtual bool checkIsDivideByBlock(int targetUin); //******** codeby:chenwei 是否被block隔开
	virtual WCoord getSkinActTargetPos(int act, const WCoord& selfPos, bool isReverse = false); //******** codeby:chenwei 获取装扮动作接受玩家的位置 20

	//三角门碎片使用
	//void useSanJiaoMen(int tag);
	bool isMyAccountHorse(long long objid);

	//void findPlaceSplitDisguise(int &splitResult, bool ignoreOccupied = false);
	//IClientPlayer
	virtual void openEditActorModelUI(WorldContainer* container) override;
	void openFullyCustomModelUI(WorldContainer* container);
	//IClientPlayer
	virtual void syncOpenFCMUIToClient(const WCoord& blockpos, bool isedited, std::string url, int version = 0, int result = 0) override;

	void syncTaskByEnterWorld(const RepeatedPtrField<PB_TaskInfoData>* pdatas);

	int updateNpcShopItemChange(int shopid, int skuid, int buycount);//for npc shop update backpack items
	int updatePackGiftItemChange(int packindex, int costitemid, int costitemnum, const std::map<int, int>& addMap);//for pack gift update backpack items


	void updateAccountHorse(int horseid, float hp, int addlivetick, int shieldcoolingticks);
	void resetAccountHorseLiveTick(int horseid, int t);
	virtual int getNetSyncPeriod() override
	{
#ifdef IWORLD_SERVER_BUILD
		return 3;
#else
		return 2;
#endif // IWORLD_SERVER_BUILD
	}

	virtual void addDirtyIndex(int index);
	void setOperate(int op, int totalticks = 0, int opdata = 0, long long lopdata = 0, long long tick = 0);
	void playToolEffect(int stage, bool stoplast);
	void playToolSound(int stage, bool loop);
	//void playItemSkillLoopEffect(bool stop);
	//void playItemSkillLoopSound(bool stop);
	void setSpecSpeed(float spec_speed) { m_fSpecSpeed = spec_speed; }
	float getSpecSpeed() { return m_fSpecSpeed; }

	void setGunInfo(float spread, float jaw, float pitch, Rainbow::Vector3f pos);
	bool checkClientInputMotion(const WCoord& dp, bool client_onground);
	void resetCheckClientInputVariable();
	void setGuardSafeTick(int tick);
	void setLastTriggerBlock(const WCoord& blockpos);

	void summonAccountPet(int petid);
	//int  getCurPetMonsterId();
	//WORLD_ID  getCurAccountPetObjId();
	void hideAccountPet();
	void showAccountPet();
	ClientActor* getCurPet();
	bool canAttackByItemSkill(int skillId, ClientPlayer* player);
	int doPick(Rainbow::Vector3f& currentEyePos, Rainbow::Vector3f& currentDir);
	virtual std::vector<WORLD_ID> doPickActorByItemSkill(int skillid, WCoord& centerPos, Rainbow::Vector3f& currentEyePos, Rainbow::Vector3f& currentDir);
	virtual std::vector<WCoord> doPickBlockByItemSkill(int skillid, Rainbow::Vector3f& currentEyePos, Rainbow::Vector3f& currentDir);
	virtual std::vector<WORLD_ID> doPickPhysicsActorByItemSkill(int skillid, WCoord& centerPos, Rainbow::Vector3f& currentEyePos, Rainbow::Vector3f& currentDir);
	// 获取屏幕控件的坐标
	virtual bool GetScreenSpacePos(Rainbow::Vector3f pos, int& x, int& y);
	//tolua_end
	virtual void setMotionChange(const Rainbow::Vector3f& motion, bool addmotion = false, bool changepos = false, bool sync_pos=true) override;
	//tolua_begin
	//需要针对视角 处理武器特效
	bool needHandleWeaponMotionForView(int status, const char* motionName, bool stopMotion = false);
	int  getWeaponMotionStatus()
	{
		return m_WeaponMotionStatus;
	}
	const char* getWeaponMotionName()
	{
		return m_WeaponMotionName.c_str();
	}
	bool isVisible();
	bool canShowShotTip();

	void resetRound();


	virtual const char* getCustomjson() { return m_strCustomjson.c_str(); }
	virtual void setCustomJson(std::string&) override;

	void setCustomModel(std::string custommodel);
	void setCustomModelScale(float scale) {
		m_fCustomModelScale = scale;
	}
	float getCustomModelScale() {
		return m_fCustomModelScale;
	}

	void setFreezing(int freezingFlag);
	int getFreezing() { return m_nFreezingFlag; }

	virtual void setRocketTeleport(bool b) override
	{
		m_RocketTeleport = b;
	}

	virtual bool isRocketTeleport() override
	{
		return m_RocketTeleport;
	}
	WORLD_ID getRidingVehicle();
	//IClientPlayer
	virtual int getContainersPassword(const WCoord& pos) override;

	virtual void setHookObj(WORLD_ID obj, bool include_me = true);
	virtual WORLD_ID getHookObj() override{ return m_bHookObj; }

	void setLandingPoint(int mapid, WCoord pos);
	WCoord getLandingPoint(int mapid);

	bool isOwnCreateWorld();   //正在玩的是不是自己创建的地图
	virtual int getCurViewRange() override;
	virtual void setContainersPassword(const WCoord& pos, int password) override;
	virtual void refreshAvarta() override;

	void onHandlePlayerBodyColor2Client(const PB_PlayerBodyColorHC& playerBodyColorHC);
	ChangeColorComponent* sureChangeColorComponent();

	//void checkChangeSpawnPoint(const WCoord &blockpos, int mapid);
	//void setSpawnPoint(const WCoord &blockpos, bool bSetWorldMgr = true);
	//WCoord getSpawnPoint();
	//bool getAccountWorldPoint(int mapid, WCoord& spawnpoint, WCoord& revivepoint);
	//void setAccountWorldPoint(int mapid, WCoord spawnpoint = WCoord(0, -1, 0), WCoord revivepoint = WCoord(0, -1, 0));
	//void playClientReviveEffect(int mapid, WCoord revivepoint);
		//******** codeby: chenwei 设置伴舞uin
	void setSkinPartnerUin(int uin)
	{
		m_nSkinActPartnerUin = uin;
	}
	//******** codeby: chenwei 获取伴舞uin
	int getSkinPartnerUin()
	{
		return m_nSkinActPartnerUin;
	}

	int getKills() { return m_CWKills; }

	virtual void setSyncCustomModelTick(int tick)
	{
		m_SyncCustomModelTick = tick;
	}

	virtual void setSyncTransferTick(int tick)
	{
		m_CurSynTransferIndex = 0;
		m_SynTransferTick = tick;
	}

	void ReportSurviveDay();	//成就系统, 上报生存天数
	void ReportKillBoss(World* pworld, long uin, int bossId, WCoord spawnPoint);//成就系统, 击杀boss上报
	void generalTaskReportPlaceBlock();  // 建筑工升职记 使用方块上报

	// 状态
	virtual void setJumpState(int times);
	virtual void setStopState(bool b);
	virtual void setFallGround(bool b);
	virtual void setCurrentMoveState(bool b);
	virtual void setSneaking(bool b);
	virtual void updateRunState();

	void setFollowByVillager(bool state) {
		m_canFllowByVillager = state;
	}
	bool getFollowByVillager() {
		return m_canFllowByVillager;
	}
	bool syncMotionState(int state, bool b = true);
	void setMotionState(int state, bool b = true);

	void setActionAttrState(int actionattr, bool b);

	bool checkDeveloperHandleForActor(bool isLeft, ClientActor* target, bool openWnd = true);
	bool checkDeveloperHandleForBlock(bool isLeft, int blockId, int posX, int posY, int posZ);

	int getMaxLifeNum();
	void setLeftLiftNum(int num);

	virtual void syncAttr(int attrtype, float val);  //部分属性被设置后 要同步给客机

	void SetPickItemLastIndex(int index);
	int GetPickItemLastIndex();

	void onBuffAppend(int buffid, int bufflvl); // 效果附加
	void onBuffRemove(int buffid, int bufflvl); // 效果移除

	virtual void setSpeedUpTimes(unsigned char speedUpTimes) override;
	unsigned char getSpeedUpTimes();

	// 设置玩家的权限状态
//	void setActionStates(unsigned int states) { m_ActionAttrState = states; }

	virtual Rainbow::Vector3f getCarryingBindPos();
	void setLoadSpeedUp(bool loadspeedup) { m_bLoadSpeedUp = loadspeedup; }

	void moveMobItem(int gridIndex, int moveType = 1);
	void restoreSkin();

	void OnGainedExp(int nExp);

	//判断家园编辑模式
	bool isHomeLandGameMakerMode();

	/*
		获取player对应宠物信息
	*/
	virtual std::string getCurSummonPetID();
	virtual SummonPetInfomation getCurSummonPetInfo();

	void setCurSummonPetID(std::string& petId);
	void setCurSummonPetInfo(int monsterid, int petid, int stage, int quality);

	void createUIViewActorBody();

	bool addMobToTamedFollows(ClientMob* mob);//code-by dengpeng 驯养后的生物加到跟随列表
	void removeMobFromTamedFollows(ClientMob* mob);
	void initMobByEggData(long long objId, const jsonxx::Object& jsonObj); //code-by:lizb 通过生物蛋内存储的信息初始化生物
	bool isInTamedFollows(ClientMob* mob); //是否在跟随列表里
	/*  code-by:hanyunqiang
		地心人偷取体力标记
	*/
	WORLD_ID  getCurStealMonster() { return m_curStealWid; }
	void setCurStealMonster(WORLD_ID wid) { m_curStealWid = wid; }
	void setUsingPianoPos(const WCoord& pos) { m_usingPianoPos = pos; }
	void setUsingPianoSitPos(const WCoord& pos) { m_sitingPianoPos = pos; }
	void restoreSkinByReason(int transformReason);
	bool onInteractByActorSkinNpc(long long mainPlayer, int changeModel);
	void onHandlePlayerTransformSkinModel2Client(const PB_PlayerTransformSkinHC& changeModelHC);

	void breakHorseInvisible();		// 20210910：打断骑乘的坐骑隐身  codeby： keguanqiang
	bool isInvisible();				// 20210910：是否隐身  codeby： keguanqiang
	//void checFollowSituation();
	void callPierceCommand();//GM玩家穿墙
	void callPierceCloseCommand();//GM玩家穿墙关闭

	bool isOpenPierceViewBright() { return m_isOpenPierceViewBright; }
	void setOpenPierceViewBright(bool isOpen) { m_isOpenPierceViewBright = isOpen; }

	int getKillScorpionCount() { return m_KillScorpionCount; }
	void setKillScorpionCount(int val) {
		if (val == 0)
		{
			m_KillScorpionCount = 0;
		}
		else
		{
			m_KillScorpionCount += val;
		}
	}
	//获取护甲
	float getArmor();
	//获取毅力
	float getPerseverance();
	void removeLetter(int index);
	void removeWorldStringByGridIndex(int index, int destId, int type);
	void removeWorldStringByKey(const std::string& key, int type);

	bool isFishing();	// 是否正在钓鱼(不包括抛竿收竿阶段)
	bool isEndingFishing();		// 是否在钓鱼收竿阶段
	int getFishResultItemId();	// 当前钓鱼的结果(钓鱼阶段可以知道)
	int clearFishResult();	// 让当前钓鱼没有了结果
	long long getFishhookObjId();	// 获取鱼钩Actor的objid
	void battleHornVillager();//野人伙伴战斗号角
	virtual void SetTreeItemIndex(int itemid, int index);
	virtual int GetTreeItemIndex() override;
	void UseColorBrush();
	void ToggleDisplayNameVisible(bool visible);//切换title的可见性
	bool HaveExtBackPack(); //是否有扩展背包
	int GetExtBackPackGridCount();//当前扩展背包格子数目

	//显示、隐藏新鱼竿的鱼线
	void SetFishLineEndPoint(int x, int y, int z);
	bool ShowFishLine(bool bShow = true);

	//tolua_end

	PetFollowListComponent* surePetFollowListComponent();
	virtual InteractTamedMobComponent* SureInteractTamedMobComponent();
	void onClientUploadCheckInfo(int infoType, const std::string& detail);
	void tryGetAdShopExtraAward(int awardId, int itemId, int count);
	void tryExtractStoreItem(int storeIndex, int itemId, int count);
	PlayerCheatData* GetCheatHandler() { return m_CheatData; }
	virtual bool isPlayer() { return true; } // 返回actor是否是玩家 2021.12.24 by huanglin
	virtual void SetRunSandboxPlayer(bool value) override { m_isRunSandboxPlayer = value; }
	virtual bool IsRunSandboxPlayer() { return m_isRunSandboxPlayer; }

	bool checkCanInteractPoseidonStatue(); //检测玩家是否可交互海神像
	void setPoseidonStatueStates(bool value);

	virtual AccountHorseComponent* sureAccountHorseComponent();
	PetAccountComponent* surePetAccountComponent();
	virtual PetSummonComponent* surePetSummonComponent();

	//刺球
	void doThornBall(ClientActor* actor);
	bool isAttrShapeShift();// 是否属性变身了
	void doAttrShapeShiftLeftClick();
	void doAttrShapeShiftRightClick();
	//彼岸菠萝 突发恶疾buff 主动发起攻击
	void suddenIllnessBuffAttack();
	void  AttackFromSharkBite(ClientMob* actor);//受到鲨鱼攻击

	bool checkIsSnowMan(ClientActor* actor);//检测雪人

	bool IsInPierceMode() { return m_isInPierceMode; } // 是否在审查模式

	virtual void setFlying(bool b);
	/*
	* 播放武器特效
	* @param resetPlay 是否重置播放，重置会打断前一次播放
	* @param motionClass 标记，停止的时候需要用到
	* @param motionScale 缩放
	* @param isSync 是否同步
	* @param playType 播放类型（第三视角使用） 0-右手 1-左手 2-全部
	* @param tpsPlay 第三视角是否播
	*/
	void playWeaponMotion(std::string motion, bool resetPlay = true, int motionClass = 0, float motionScale = 1.0f, bool isSync = true, int playType = 0, bool tpsPlay = true);

	bool onLoadUserData(const void* data, int len);
	virtual bool saveUserData(long long owid);
	/*
	* 给player做模拟射线的
	*/
	int doSimulativePick(const Rainbow::WorldPos& originPos, const Rainbow::Vector3f& dir, int range, IntersectResult& result, bool ignorecarried = false);

	void updatePhysCollisionBetweenPlayers();
	virtual void resetAllFlags(unsigned int flags) override;
	
	//科技树
	bool onLoadTechTree(const void* data, int len);
	virtual bool saveTechTree(long long owid);
	miniw::tech_tree* getTechTree(int page_level);
	miniw::tech_tree_node* getTechTreeNode(int level, int node_id);
	std::vector<miniw::tech_tree*> getTechTrees(int level, int root_tree_id = 0);
	void unlockTechNode( int level, int tree_id, int node_id);

protected:
	virtual void randomSelect(std::vector<const EnchantDef*>& echantSet, int num);
	virtual void preMoveTick() override;
	virtual void afterMoveTick() override;
public:
	//tolua_begin

	std::set<int> mDirtyGridIndex;
	long long m_clampId;
	//--//WCoord m_OpenContainer;
	//--//WORLD_ID m_OpenContainerID;
	//--//int m_OpenContainerBase;
	//--//long long m_vehicleObjid;
	static int m_ViewRangeSetting;
	static int m_ViewInnerRangeOffset;	//inner range
	int m_GameScore;
	int m_GameRanking; //名次
	int m_GameResults;
	WCoord m_CurDigBlockPos;
	int m_OperateTicks;
	int m_OperateTotalTicks;
	unsigned long long m_OperateStartTick;
	int m_CurDigBlockID;
	float m_RangeAttackPower;
	//载具挖方块使用的数据
	long long m_CurDigVehicleID;
	WCoord m_CurDigVehBlockPos;

	//float m_JetpackCosumeAccum;
	//int m_OxypackCosumeAccum;
	//int m_SnakeGodWingCosumeAccum;
	//int m_BlueprintTicks;
	int m_PickItemLastIndex;	//鼠标拾取的道具之前的格子索引

	WCoord m_LastTriggerBlock; //上一次触发的方块,  y<0表示不存在

	//std::map<int, float> m_SkillCD;
	std::vector<int> m_UnlockItems;
	int m_WorldTimes[MAX_MAP];
	WCoord m_LandingPoints[MAX_MAP];
	//std::map<int, TaskInfo> m_Tasks;
	PlayerTaskManager* m_pTaskMgr;
	WORLD_ID m_OpenDialogueMobID;
	int m_OPWay;
	bool m_UsePhysics;

	//short m_TransformReason;
	//bool m_bInTransform;
	////long long m_MainSkinPlayerID;	//副装扮玩家
	////std::vector<long long> m_vecSkinSubActors;		//副装扮npc
	////std::vector<long long> m_vecSkinSubPlayers;	//子装扮玩家
	////std::vector<WCoord> m_SplitPos; //子装扮生成位置
	////int m_iCheckDistanceTick;   //检测自己与生成子装扮的距离tick触发计数（距离过远将收回装扮），10个tick触发一次
	//std::map<int, std::map<int, NpcShopInfo> > m_NpcShopInfo;//[shopid, [skuid, NpcShopInfo]] 该地图里所有的商店信息，只存在主机里
	NpcShopDef m_stCurNpcShopInfo;//当前商店的信息
	std::string m_strOriginCustomJson;
	int m_originSkinId;
	int m_AccoutSkinID;
	int m_curInteractPlotType;//当前触发的对话所属类型
	//WCoord m_RevivePoint;
	//WCoord m_TriggerRevivePoint;
	//WCoord m_SpawnPoint; // 个人重生点
	int m_crabClickCount;
	bool m_SnakeGodWingFlying;	//蛇神之翼飞行

	IntersectResult m_PickResult;
	//tolua_end
	PlayerStateController* m_StateController = nullptr;
	CameraModel* m_CameraModel = nullptr;
	BLOCK_MINE_TYPE m_MineType;
	EffectDestroyBlock* m_DigEffect;
	std::map<int, bool> m_biomeBeenTo;
	// 星站信息
	int m_CurSynStarStationIndex;
	int m_SynStarStationTick;

	//防外挂数据
	int m_SuspicionValue; //有使用外挂等的嫌疑, 随时间下降
	int m_LastClientInputMotionY;

	bool m_ChangeViewMode;
	GunHoldState m_GunHoleState = GunHoldState::NOGUN;//持枪状态
public:
	//tolua_begin
	int GetGunHoldState() { return (int)m_GunHoleState; }
	virtual float getOxygenUseRate() override; //0-1.0f,  0完全不消耗氧气

	//PlayerAttackingTargetComponent
	void setAttackAnim(ATTACK_TYPE attacktype);
	void setAttackAnim(ATTACK_TYPE attacktype, int animTicks);

	//OpenContainerComponent
	virtual bool checkIsOpenContainer(const WCoord& pos, int index) override;
	bool checkIsOpenContainer(WORLD_ID objid, int index);
	//IClientPlayer
	virtual WorldContainer* getCurOpenedContainer() override;
	virtual WCoord getCurOpenedContainerPos() override;
	void onCloseFullyCustomModelUI(const PB_CloseFullyCustomModelUICH& closeFullyCustomModelUICH);
	void onCloseEditActorModel(const PB_CloseEditActorModelCH& closeEditActorModelCH);
	virtual void cleanupOpenedContainer() override;
	virtual int getOpenContainerBaseIndex();
	//IClientPlayer
	virtual bool openContainer(WorldContainer* container) override;
	virtual bool openContainer(ActorContainerMob* container);
	virtual void closeContainer() override;

	//SkillCDComponent begin
	float getSkillCD(int itemid);
	float getTotalSkillCD(int itemid);
	void setSkillCD(int itemid, float cd);
	void syncSkillCD(int itemid, float cd);
	virtual void saveSkillCDCompToPB(game::common::PB_SkillCDData* skillCDData) override;
	void setItemSkillCD(int itemid, float cd);//疑似废弃  无地方调用
	//SkillCDComponent end
	//新的技能SkillCompont提供的CD接口
	void getSkillCDNew(int itemid, float& cd, float& maxCD);
	void saveSkillExtendCDCompToPB(game::common::PB_SkillExpandCDDataGather* gather);
	void loadSkillExtendCDCompFromPB(const game::common::PB_SkillExpandCDDataGather* gather);
	//
	virtual bool CanExposePosToOther();
	void SetExposePosToOther(bool b);
	void addWeaponSkilledPoint(int pointType, int itemId = 0); //新增武器熟练度系统 2022.06.28 code-by:lizhibao
	int openPackGift(int iItemID, int iShortCutIdx, int iPackID, std::string& addlist);
	//tolua_end
	virtual bool isSimPlayer() { return false; }
	int getCurItemSkillID();
	void setCurItemSkillID(int skillid);
	const ItemSkillDef* getCurItemSkillDef();
	bool getBodyInfoInUse(int* model, int* genius_lv, int* skin_id);
	unsigned long long getOperateStartTick() { return m_OperateStartTick; }

	void playDigAnim(DIG_METHOD_T digmethod);
	virtual void destroyBlock(const WCoord& blockpos, DIG_METHOD_T dgmethod, bool destroy_effect, bool gamerule_forbid);
	EffectDestroyBlock* getDigEffect() { return m_DigEffect; }
	void setDigEffect(EffectDestroyBlock* effect) { m_DigEffect = effect; }
	void notifyPunchBlock2Tracking(const WCoord& blockpos, DirectionType face, int status, DIG_METHOD_T digmethod, long long vehID = 0);
	bool isSkillCD();
	void updateBound(int height = -1, int width = -1);
	int getTackleRange();
	int getGrabRange();
	int getDribbleRange();
	//椰子跳过夜晚
	void setCoconutHit(bool is);
	bool isCoconutHit();
	bool isCoconutSkipNight();
	void updateCoconutSkipNight();//
	void setCoconutSkipNight(bool is);
	int	 getCoconutTimer();
	void notifyInteractActor2Tracking(ClientActor* target, int itype, bool interactplot = false);
	virtual ~ClientPlayer();
	SleepState* GetSleepState();

	//tolua_begin
	bool openEmitterContainer();
	bool _mountAndOpenEmitterContainer(const WCoord& blockpos);
	inline void setUsingEmitter(bool b)
	{
		m_useEmitter = b;
	}
	virtual bool getUsingEmitter() override
	{
		return m_useEmitter;
	}
	inline void setEmitterBlockPos(const WCoord& pos)
	{
		m_emitterPos = pos;
	}
	inline WCoord getUsingEmitterBlockPos()
	{
		return m_emitterPos;
	}
	ContainerManualEmitter* getEmitterContainer();
	virtual WorldContainer* getEmitterWorldContainer();
	virtual bool disMountEmitter(const WCoord& blockpos);
	virtual bool disMountEmitterClient(const WCoord& blockpos);
	virtual bool mountEmitter(const WCoord& blockpos);
	virtual bool operateManualEmitter(const WCoord& blockpos);
	virtual bool operateManualEmitterClient(const WCoord& blockpos);
	//tolua_end
	void OnDieForTrigger();
	void OnReviveForTrigger(int revivetype);
private:
	bool _disMountEmitter(const WCoord& blockpos);
	bool _mountEmitter(const WCoord& blockpos);
	bool _operateManualEmitter(const WCoord& blockpos);
private:
	bool m_useEmitter = false;
	WCoord m_emitterPos;

protected:
	virtual void fall(float fall_dist);
	virtual float getFallHurtSubtract();
	virtual int getOxygenUseInterval() override;


	//void tickStoreRoleData();

	void notifyInteractBlock2Tracking(const WCoord& blockpos, DirectionType face);

	//	void notifyExploitBlock2Tracking(const WCoord &blockpos, DirectionType face, int status, int picktype);


	void throwItem(const BackPackGrid& grid);
	//WCoord verifyRespawnCoordinates(World *pworld, const WCoord &pt, bool spawnforced);
	void checkDungeonPos();
	void calUnmountPos(ClientActor* actor);
	void fillBlocks(const WCoord& minpos, const WCoord& maxpos, int fillid, int filldata);
	void applyDisplayName();



	int killedByActor(WORLD_ID* pobjid = NULL); //0: 其它原因，  1: 被玩家杀死， 2: 被怪物杀死
	virtual void doActualAttack(ClientActor* target, int targetIndex = 1) override;
	void doActualChargeAttack();
	void getFacedActors(std::vector<ClientActor*>& actors, const Rainbow::Vector3f& dir, int range, int width);
	bool getPunchAtkData(OneAttackData& atkdata, ClientActor* target);
	// 获取近战攻击数据（新伤害计算系统）
	bool getPunchAtkDataNew(OneAttackData& atkdata, ClientActor* target, int targetIndex = 1);
	void updateAttackBound(int height = -1, int width = -1, int thickness = -1);
	/**
	@brief 加载本地地图
	*/
	bool loadFromFileReal(long long owid, int uin, bool needload, int specialType = NORMAL_WORLD);
	bool loadFromDataServerReal(long long owid, int uin, void* buf, int buflen, bool needload, int specialType = NORMAL_WORLD);

	virtual bool canPlayStepSound();
	virtual bool canPlayHurtSound();

public:
	virtual void SetPlayerAttrViewRange(int range) override;
public:
	static void SetViewRangeSetting(int range);
	static int GetCurViewRange(ClientPlayer* player);
	virtual ActorBindVehicle* getActorBindVehicle()override;
protected:
	virtual HPProgressComponent* getHPProgressComponent() override;
	//virtual TeamComponent* getTeamComponent() override;
	virtual OpenContainerComponent* getOpenContainerCom();
	virtual ItemSkillComponent* getItemSkillComponent();
	virtual ActorInPortal* sureActorInPortal() override;
	virtual ActorUpdateFrequency* getUpdateFrequencyCom() override;
	PlayerAttrib* m_PlayerAttrib;
	//ItemSkillComponent* m_pItemSkillComp;
	std::string m_Nickname;
	VipInfo m_VipInfo;
	unsigned int m_LastStoreTick;
	WCoord m_RegionStartPos;  //区域操作方块的起点

	SocAttackInfo m_socAttackInfo;

	//int m_SleepTimer;
	//WCoord m_VillagePoint; // 村庄点
	//std::vector<WCoord> m_VillageTotems;	//村庄图腾列表
	//std::map<int, std::vector<WCoord>> m_VillagerFlags;	//村民工作点列表，分类
	//std::set<WORLD_ID> m_BindVillagers; //驯服的野人(村民)
	//bool m_SpawnForced;
	int m_LoginNum;
	int m_MutateMobID; //变身的id
	int m_CurOperate;
	int m_OperateData; //useItem:itemid
	long long m_lOperrateData; //kick ball:objid

	std::vector<TackleEffectData> m_TackleEffects;
	Rainbow::ISound* m_CurPlaySnd;
	float m_checkTime;
	Rainbow::ISound* m_TriggerSounder;
	std::map<std::string, Rainbow::ISound*> m_TriggerSounderMap;

	ChunkViewer m_ChunkViewer;
	bool m_isRunSandboxPlayer{ false }; //跑沙盒环境的时候，利用此变量过滤一些不必要的老的tick设置属性的逻辑
	//struct AccountHorseInfo
	//{
	//	int horseid;
	//	float hp;
	//	int liveticks; //生存时间
	//	int shieldcoolingticks; //护盾冷却时间
	//	int equips[3];
	//};
	//std::vector<AccountHorseInfo>m_AccountHorses;

	//std::vector<AccountWorldPointInfo>m_AccountWorldPoint;						//玩家放置,复活点列表

	//struct InteractSpBlockInfo
	//{
	//	int nBlockID;
	//	WCoord interactpoint;

	//	InteractSpBlockInfo()
	//	{
	//		nBlockID = -1;
	//		interactpoint = WCoord(0, -1, 0);
	//	}
	//};
	//InteractSpBlockInfo	m_LastInteractSpBlock;			   //最后交互方块
	//std::vector<InteractSpBlockInfo>m_InteractSpBlockList; //交互特殊方块列表(历史复活点)

	//std::vector<int64_t> m_TamedMobFollowObjidList; //宠物跟随列表

	//WORLD_ID m_CurAccountHorse;
	//WORLD_ID m_CurAccountPet;
	//int		 m_PetMonsterId;
	int m_TestItemIconIndex;
	int m_UIControlMode; //mobile: 0,  pc: 1
	//int	m_sleepTickInDay;	
	int m_surviveDays;
	int m_suriveDaysByWorld;
	int m_CWKills; //连杀数
	int m_Kills;
	int m_DieTimes; //死亡次数
	uint64_t m_PlayerStartTimes; //角色开始存活的时间戳
	std::vector<ClientMob*> actorsfind;

	int m_CurPlaySndToolID;
	int m_CurPlayEffectToolID;
	float m_fSpecSpeed;

	int m_SelectedColor;

	PLAYER_SPECTATOR_MODE m_nSpectatormode;
	PLAYER_SPECTATOR_TYPE m_nSpectatortype;
	int m_nSpectatorUin; //观察他的玩家uin
	int m_nToSpectatorUin; //观战模式下,被观察的玩家uin

	//道具技能相关参数
	//int m_nCurItemSkillID;
	//int m_CurSndItemSkillID;
	//int m_CurEffectItemSkillID;
	//Rainbow::ISound *m_CurItemSkillSnd;


	float m_nPosYPos;
	float m_nPosYNeg;
	float m_nPosXZ;
	int  m_nGuardTick;
	bool m_bGuardError;
	bool m_bPreOnGround;
	bool m_bOffGrounding;
	int  m_nGuardSafeTick;
	int  m_nQuickSpeedCount;
	int  m_nReciveCount;
	std::string m_strCustomjson;
	std::string m_strCustomModel;


	float m_fCustomModelScale;
	enum
	{
		UpdateSurvive,
		UpdateTrigger,
		UpdateGun,
		UpdateSkillCD,
		UpdateSkin,
	};
	std::map<int, std::function<void(float)>> mNeedUpdateChunk;
	//被怪物限制行动标志
	int m_nFreezingFlag;

	bool m_RocketTeleport;
	WORLD_ID m_bHookObj;

	int m_ActiveDays;

	int m_helloTick;//点击打招呼，100tick内统计，沙漠村民npc跟随玩家
	bool m_canFllowByVillager;//是否可以被沙漠村民跟随
	int m_clicktimes;//第几次点击打招呼按钮
	//typedef Rainbow::HashTable<WCoord, int, WCoordHashCoder> ContainPasswordHashTable;
	//ContainPasswordHashTable m_ContainersPassword;

	//unsigned int m_DestSkinColor;
	//unsigned int m_CurSkinColor;
	//float m_ColorIncrements;
	//int m_CheckSkinColorTicks;
	int m_CurSyncCustomModelIndex;
	int m_SyncCustomModelTick;
	// 传送点
	int m_CurSynTransferIndex;
	int m_SynTransferTick;

	int m_WeaponMotionStatus;
	std::string m_WeaponMotionName;
	MotionControl m_MotionCtrl; // 行为控制
	int m_iInVehicleWorkShop;
	//有关联的载具
	//WORLD_ID m_OnVehicle;
	//WCoord   m_onVehicleCoord;

	unsigned int m_MotionState;

	bool m_CanControl;
	bool m_SyncCtrl; // 是否同步控制操作
	int m_MaxLifeNum;
	int m_nLang;
	int m_nApiid;
	int m_nEnterTs;
	//int m_nBuffCheckTime;

	int m_BoundHeight;	// 范围
	int m_BoundWidth;
	int m_AttackBoundHeight;
	int m_AttackBoundWidth;
	int m_AttackBoundThickness;
	//bool m_RestInBed;	//床上休息标志位
	bool m_IsUseHearth; // 是否在使用灶台 code-by: liya
	WCoord m_UsingHearthPos;	// 使用中灶台的位置 code-by: liya
	unsigned int m_SpeedUpTimes; //加速倍数
	PlayerStateManager m_PlayerStateMgr;
	bool isSpringMove;//这个是载具上的弹簧对角色作用 标记，用来跳过物理引擎进行的角色与载具的碰撞处理
	int m_checkboxscale;//设置检测碰撞盒范围
	bool m_NewPlayerFlag; // 是否是新玩家标志
	bool m_bLoadSpeedUp;//传输微缩数据加速
	long long curShowDialogId;//当前显示头上对话框的商人objid
	//long long m_InteractTamedMobID;
	//std::string      m_CurSummonPetID; //当前召唤的宠物ID
	/*
		当前召唤的宠物monsterid
	*/
	//SummonPetInfomation		m_CurSummonPetInfo;

	friend class SetTestItemIconIndexCommand;
	friend class ExportSTLCommand;
	bool m_isStarStationTeleporting;
	bool m_isExploiting;	//是否正在开垦中
	//bool m_bFirstPlayReviveEffect;

	bool m_isInMusicClubArea;//20210910：是否在音乐方块中
	bool m_isSkinning; //是否在剥皮

	WCoord m_usingPianoPos;
	WCoord m_sitingPianoPos;
	int m_KillScorpionCount;//小蝎子被玩家杀死次数

	//	TotemComponent*  m_totemComp;
	friend class PlayerTeamComponent;
	friend class ItemSkillComponent;
	friend class TransformersSkinComponent;

	bool m_InteractPoseidonStatue; //检测玩家是否可交互海神像
	int m_InteractPoseidonStatueTime; //交互海神像间隔

	std::set<int>	m_JumpTime;
	int m_treeItemIndex;

	int m_suddenIllnessBuffTraceTick;
	bool m_isBuffChangeViewMode;
	int m_oldBuffViewMode;
	int m_oldFreezing;
	MNSandbox::Callback m_buffPlayerCanCtrCallBack;

	PlayerCheatData* m_CheatData;   // 作弊相关数据
	
	MoveControl *m_MoveControl;
	bool m_MotionChangeSyncPos;
	WCoord m_BlockedPosition;
	std::map<int, int> m_TipTime;// 记录上次文本提示ID
	float m_OldJumpCharge; //弹跳蓄力
	WCoord m_TeleportPosition;  // 传送目标位置
	ItemSkillComponent* m_pItemSkillComponent = nullptr;
	TransformersSkinComponent* m_TransformerSkinComponent;
public:
	//tolua_begin
	void setSpringMove(bool value) { isSpringMove = value; }
	bool getSpringMove() {
		return isSpringMove;
	}
	void exportSTLForMiniCode(WCoord& start, WCoord& end, const std::string& stlName);  // minicode使用
	void setInMusicClubArea(bool inArea);
	bool getInMusicClubArea();	// 20211015是否在音乐方块中  codeby： huangxin
	//2021-12-20 codeby: wangyang 会员聊天气泡
	void tickNewChat(const char* text, int bubble = 0);				//新的消息来重设时间和聊天内容
	void setTopBrand(const char* text);//设置顶部名牌
	void setAttribDirty(bool value) { m_attribDirty = value; };
	bool getAttribDirty() { return m_attribDirty; };
	void addMotion(float x, float y, float z);

	// 自定义数据
	std::string getUserData(const std::string& key);
	bool hasUserData(const std::string& key);
	bool removeUserData(const std::string& key);
	void setUserData(const std::string& key, const std::string& value);
	void removeAllUserData();
	std::map<std::string, std::string> getAllUserData();
	virtual void changeMoveFlag(unsigned flag_id, bool on);
	bool isNewMoveSyncSwitchOn();
	void setFlyingAndSync(bool b)
	{
		setFlying(b);
		m_FlagBitChanged |= (1 << ACTORFLAG_FLY);
	}

	virtual std::string getBPTitle() { return m_BPTitle; }
	void setMoveControlPitch(float pitch);
	void setMoveControlYaw(float yaw);

	void COpenWorkbench(int Uin, int Level);
	void CUnlockTechNode(int Level, int RootTreeId, int NodeId);

	//tolua_end
	void OnKillMonster(int monster_id);
	int getTotalKill() { return m_Kills; }
	void updatePlayer();
	bool preCheckMoveControl(int &result, const WCoord& dest_pos);
	void checkMoveResult();
	void _updateJump();
	void addMoveStatus(unsigned key);
	void setNeedSyncPosition();
	void removeMoveStatus(unsigned key);
	void setMoveControl(const std::set<unsigned>& operators, float yaw, float pitch, unsigned long long tick);
	void setMoveControl(unsigned operators, float yaw, float pitch, unsigned long long tick);
	void setCheckMoveResult(unsigned long long id, const WCoord& pos, unsigned long long tick);
	void setLastSyncPosition(const WCoord& pos);
	void updateSnakeGodWing(bool jump, int move_forward);
	void updateFireRocket(bool jump);
	void updateChargeJump(bool jump);
	void updateJetFly(bool jet_flying);
	bool isMoveControlActive() const;
	void updateClientMoveSyncInterval(bool);
	//2021-12-20 codeby: wangyang 会员聊天气泡
	int m_chatBubble;								//聊天气泡背景
	std::string m_BPTitle;		// 悦享赛事称号 20230606 by wuyuwang
	void setGunUseUpdate(std::function<void(float)> func)
	{
		clearGunUseUpdate();
		mNeedUpdateChunk[UpdateGun] = func;
	}
	void clearGunUseUpdate()
	{
		auto item = mNeedUpdateChunk.find(UpdateGun);
		if (item != mNeedUpdateChunk.end())
		{
			mNeedUpdateChunk.erase(item);
		}
	}
#ifdef IWORLD_SERVER_BUILD
	int RecoverBagPack(const void* buf, int buf_len, GridVisitor* recorder);
#endif
	bool	m_IsSkipNight;
	int		m_SkipNightTime;
	int		m_TickInDay;
	int		m_TickInHit;
	bool	m_IsCoconutHit;

	//角色对象相关
	virtual bool IsOnPlatform() { return m_pBindPlatform != nullptr; }
	ClientActor* GetPlatform() { return m_pBindPlatform; }
	// 主机可以直接设置。  客机一般情况下不能自己设置， 除非是主机通知过来的（bForceInRemote 为 true）
	void SetOnPlatform(ClientActor* pActor, bool bForceInRemote = false);
	virtual Rainbow::Transform* GetTransform() override;
	virtual Rainbow::GameObject* GetGameObject() override;

	//角色对象相关
	ClientActor* m_pBindPlatform;
	float m_MoveForward; //-1: move back
	float m_MoveRight;
	int m_MoveUp;

	SnakeGodWingState m_SnakeGodWingState;

private:
	/*视野范围*/
	int m_attrViewRange;
	//地心人偷取体力标记 code-by:hanyunqiang
	WORLD_ID m_curStealWid;
	std::vector<MpActorTrackerEntry*> m_pTrackerEntrys;
	int m_nSkinActPartnerUin; //******** codeby：chenwei 舞伴uin

	float m_haveShowTime;							//聊天气泡已经显示的时间
	float m_showLimitTime;							//聊天气泡限定显示的时间
	std::string m_chatContent;						//聊天文字内容
	bool m_needReport;								//是否上报
	int m_SaveSizeRecorded;							//保存存档超限的次数

	bool m_needSaveUserData;							// 玩家自定义数据是否需要存储
	std::map<std::string, std::string> m_mapUserDatas;	// 玩家自定义数据

	bool isInShowBubbleDistane();					//是否在限定显示气泡距离内

	bool m_needSaveTechTree;
	std::map<int, miniw::tech_tree*> m_mapTechWorkbench;	// 玩家科技树数据

public:
	virtual void checkShowMusicClubChatBubble(float dtime); //每帧检测是否到了限定时间，到了就将聊天内容设置为空
	//2021-12-20 codeby: wangyang 会员聊天气泡
	void showMusicClubChatBubble(const char* text, int bubble = 0, float tickTime = 0); //做相关判断并传递给body是否显示气泡内容
	void onSetCurShortcutWithFB(const FBSave::ActorPlayer* wrole);
	// 拷贝高级创造老地图的快捷栏数据到新快捷栏
	void CopyShortcutForUGC(BackPack* backpack);
	void gotoTeleportPos();
	virtual void setTeleportPos(const WCoord &pos) override;

#ifdef IWORLD_SERVER_BUILD
	struct PvpActivityConfig m_pvpActivityConfig;
	std::map<int, unsigned> m_pvpRecordMobKill;  // 记录击杀怪物
	bool   init_failed;   //标记是否初始化失败，初始化失败就不再保存回去，避免覆盖原数据
	std::string m_LastSavePackString;
public:
	void SetPvpActivityConfig(const game::ch::PB_PvpActivityConfigCH& msg);
	bool IsInPvpActivity();
	void PackPvpActivityData(miniw::pb_pvp_activity_player* pb_player);
	uint32_t GetPvpActivityId();
	int32_t GetPvpTimezone();
	const std::string& GetPvpCommonParam();
	void setRecordItem(int item_id);
	void setRecordMonsterKill(int mid);
	const std::map<int, unsigned>& getKillMonsterRecords() { return m_pvpRecordMobKill; };
#endif	
	bool m_exposePos; //是否让其他玩家看到自己的位置
	int m_AttrRightClickTick;// 属性变身的右键操作cd
	int m_AttrShapeShiftTick;// 属性变身tick
	int m_AttrStopRecoverTick;// 属性变身停止恢复tick
	bool m_battleHornVillager;//村民 -战斗号角-状态
	bool m_attribDirty;
	SleepState* m_SleepState;

	bool m_isInPierceMode; //是否在审查模式
	bool m_isOpenPierceViewBright;	// 是否开启审查夜视功能
	unsigned m_FlagBitChanged;  // ClientActor的m_Flags变化标

	std::string pianoSoundName; //钢琴播放文件名  codeby:rice
	std::string pianoPaticleName; //钢琴播放粒子文件名   codeby:rice
	WCoord pSoundPos; //钢琴播放声音位置   codeby:rice
	WCoord pParticlePos;//钢琴播放粒子位置   codeby:rice

	WCoord m_gotoPosChunkXZ;//玩家传送时的chunk

#ifdef BUILD_MINI_EDITOR_APP
	int m_nCurToolId = 0;
#endif
#pragma region ActorBodyer
public:
	/*
		0: 隐藏所有，  1: 显示saddle01, 2: 显示saddle02 马鞍
	*/
	void showSaddle(int id);
	/*
		0: 隐藏所有，1：显示necklace01
	*/
	void showNecklace(int id);
	/*
		0: 隐藏所有，1：显示rake01  铁耙、钛耙
	*/
	void showRake(int id);
	/*
		设置脸部表情
	*/
	void setFaceExpression(int i);
	void setFaceExpression(std::string face);
	/*
		交换脸部部件
	*///已经下沉到ActorBody
	//bool exchangePartFace(int avatarmodel, int index, bool isShow, const char* path = NULL);

	/*
		播放触发器动作(开发者)
	*/
	bool	playActForTriggerForActorbody(int actid);
	//技能编辑器播放动画 支持第一人称动画播放
	virtual bool    skillPlayAnim(int tpsanimid, int fpsanimid, int isLoop, int playLayer = -1) override;
	virtual bool skillStopAnim(int tpsanimid, int fpsanimid, bool isReset = false)override;
	virtual bool skillPlayToolAnim(int animid);
	virtual bool skillStopToolAnim(int animid);
	void skillSetChargeMove(float chargemove);
	bool isMotionChangeSyncPos() {
		return m_MotionChangeSyncPos;
	}

	//tolua_begin
	void	playWeaponAnim(int aniID);
	void	stopWeaponAnim(int aniID);
	bool	hasWeaponAnimPlaying(int aniID);
	//尝试取出所有得条目到快捷栏或者背包中
	void tryTakeoutAllItem(int baseIndex, int blockId, int blockX, int blockY, int blockZ);
	bool UseWater(int status, int itemId, int drinkType);//0为喝水方块，1为喝水道具，2为喝水桶, 3为给水袋装水
	void drinkWaterWithBlock(const WCoord& blockPos, int count, int drinkType, int itemId);
	void drinkWaterWithBlock(int x, int y, int z, int count, int drinkType, int itemId);
	//tolua_end
	void ConsumptionWaterBlock(const WCoord& blockPos, int& count);

private:
	//添加用来定位野指针问题的成员变量 TODO:TEST
	static std::map<ClientPlayer*, int> gDumpCatch;

	ItemUseComponent* m_pItemComponent;
	GunUseComponent* m_pGunComponent;
	CustomGunUseComponent* m_pCustomGunComponent = NULL;
	ActionIdleStateGunAdvance* m_pActionIdleStateGunAdvance;
	SkillCDComponent* m_SkillCDComponent;
	
	VacantEffectComponent* m_VacantEffectComponent;
	TemperatureComponent* m_TemperatureComponent;
	RadiationComponent* m_RadiationComponent;
	FishLineComponent* m_pFishLineComponent;
public:

	std::string				m_CustomSkins;				//自定义皮肤特效路径
#ifdef BUILD_MINI_EDITOR_APP
	virtual void SetBindChunk(bool toggle) override;
	virtual void setCurToolID(int nToolId);
#endif //BUILD_MINI_EDITOR_APP
#pragma endregion
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	virtual bool NeedSupportExSandboxsInfo() const override;
#endif//SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX

public:
	virtual bool ComboAttackStateRunning() override;

	ItemUseComponent* getItemUseComponent() { return m_pItemComponent; }
	GunUseComponent* getGunComponent() { return m_pGunComponent; }
	CustomGunUseComponent* getCustomGunComponent() { return NULL;/*return m_pCustomGunComponent;*/ }
	CustomGunUseComponent* sureCustomGunComponent();
	//tolua_begin
	const CustomGunDef* getCustomGunDef();
	//tolua_end
	GunGridDataComponent* getGunGridData();
	void AssembleGunPartModel(ModelItemMesh* weapon); //组装枪械的部件模型

	void BindItemComponent(MNSandbox::SceneComponent* pComponent);
	void BindGunComponent(MNSandbox::SceneComponent* pComponent);
	void BindCustomGunComponent(MNSandbox::SceneComponent* pComponent);
	ActionIdleStateGunAdvance* GetIdleStateGunAdvance();

	//鱼线组件
	void BindFishLineComponent(MNSandbox::SceneComponent* pComponent);
	void OnChangeTool(bool bHold);
	FishLineComponent* GetFishLineComponent();

	float m_GmFlySpeed;

	virtual void resetChunkViewer();

// ... existing methods ...
	// Add a method to set the offline status.
	void SetOffline(bool offline);

	// Add a method to check for offline status
	virtual bool IsOffline();

	// 用于调试枪械 的lua逻辑进行热重载 --- by charlesxie
	void ResetAllStates();
}; //tolua_exports

WCoord EXPORT_SANDBOXGAME  GetNearMobSpawnPos(ClientPlayer* player);
#endif
