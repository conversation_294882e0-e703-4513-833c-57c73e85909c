﻿#include "BlockWaterStorage.h"
#include "BlockMaterialMgr.h"
#include "Collision.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "Environment.h"
#include "ClientActorManager.h"
#include "IClientPlayer.h"
#include "ChunkGenerator.h"
//#include "GameEvent.h"
#include "DefManagerProxy.h"
#include "VehicleWorld.h"
#include "ActorVehicleAssemble.h"
#include "ActorVillager.h"
#include "SandboxIdDef.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
using namespace MINIW;


WCoord changeWithDirection(const WCoord& blockpos, const int& direction)
{
	if (direction == DIR_NEG_X)
	{
		return WCoord(-blockpos.z, blockpos.y, blockpos.x);
	}
	else if (direction == DIR_POS_X)
	{
		return WCoord(blockpos.z, blockpos.y, -blockpos.x);
	}
	else if (direction == DIR_NEG_Z)
	{
		return WCoord(-blockpos.x, blockpos.y, -blockpos.z);
	}
	else if (direction == DIR_POS_Z)
	{
		return WCoord(blockpos.x, blockpos.y, blockpos.z);
	}
	// else if (direction == DIR_NEG_Y)
	// {
	// 	return blockpos;
	// }
	// else if (direction == DIR_POS_Y)
	// {
	// 	return WCoord(blockpos.x, -blockpos.y, blockpos.z);
	// }
}


const int SmallDewCollectorID = 2414;
const int MiddleDewCollectorID = 111112415;//示例

const static WCoord SmallDewCollectorExtendDir(0, 1, 0);

const static WCoord MiddleDewCollectorExtendDir(2, 2, 2);//示例


//const static int sShellbedExtendPos[4][9][2] = {
//		{
//			{0, 0}, {1, 0},{2,0}, {0, -1}, {1, -1},{2, -1},{0, -2},{1, -2},{2, -2},
//		},
//		{
//			{0, 0}, {-1, 0},{-2, 0}, {0, 1}, {-1, 1},{-2, 1},{0, 2},{-1, 2},{-2, 2},
//		},
//		{
//			{0, 0}, {1, 0},{2, 0}, {0, 1}, {1, 1},{2, 1},{0, 2},{1, 2},{2, 2},
//		},
//		{
//			{0, 0}, {-1, 0},{-2, 0}, {0, -1}, {-1, -1},{-2, -1},{0,-2},{-1, -2},{-2, -2},
//		},
//
//};

//相对核心方块的位置偏移，，从门口向其他位置搜索
const static int sShellbedStandPos[4][12][2] = {
		{
			{-1, 0},{-1, -1},{-1, 1}, {-1, -2},{0, 1}, {0, -2}, {1, 1}, {1, -2},{2, 1},{2, -2},{2, 0}, {2, -1}
		},
		{
			{1, 0},{1, 1},{1, -1}, {1, 2},{0, -1}, {0, 2}, {1, -1}, {1, 2},{2, -1},{2, 0},{2, 1}, {2, 2}
		},
		{
			{0, -1},{1, -1},{-1, -1}, {2, -1},{-1, 0}, {2, 0}, {-1, 1}, {2, 1},{-1, 2},{0, 2},{1, 2}, {2, 2}
		},
		{
			{0, 1},{-1, 1},{1, 1}, {-2, 1},{1, 0}, {-2, 0}, {1, -1}, {-2, -1},{1, -2},{0, -2},{-1, -2}, {-2, -2}
		},
};

const static int sSleepPosOffset[4][2] = {
		{
			50,0
		},
		{
			50,100
		},
		{
			100,50
		},
		{
			0,50
		},
};
IMPLEMENT_BLOCKMATERIAL(BlockWaterStorage)
BlockWaterStorage::BlockWaterStorage()
{
}

int BlockWaterStorage::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* psection, const WCoord& blockpos, World* pworld)
{
	if (psection == NULL || idbuf == NULL || dirbuf == NULL)
	{
		return 0;
	}
	if (pworld == NULL || pworld->getContainerMgr() == NULL)
	{
		return 0;
	}
	WorldWaterStorage* container = dynamic_cast<WorldWaterStorage*>(pworld->getContainerMgr()->getContainer(psection->getOrigin() + blockpos));
	if (container == NULL) 
	{
		return 0;
	}
	int blockdata = psection->getBlock(blockpos).getData();
	idbuf[0] = 0;
	dirbuf[0] = blockdata & 3;
	return 1;
}

void BlockWaterStorage::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}

bool BlockWaterStorage::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (!player || !pworld)
	{
		return false;
	}

	if (pworld->isRemoteMode())
	{
		return true;
	}

	int blockData = pworld->getBlockData(blockpos);
	if (!isCoreBlock(pworld, blockpos))
	{
		WCoord corePos = getCoreBlockPos(pworld, blockpos, blockData);
		if (corePos.y < 0)
		{
			return false;
		}

		return onTrigger(pworld, corePos, face, player, colpoint);
	}

	WorldWaterStorage* container = sureContainer(pworld, blockpos);
	if (container == NULL)
	{
		return false;
	}
	return true;
}

void  BlockWaterStorage::init(int resid)
{
	ModelBlockMaterial::init(resid);
	SetToggle(BlockToggle_HasContainer, true);
	if (BlockMaterial::m_LoadOnlyLogic) return;
	getDefaultMtl()->setItemMtlOpaque(true);
	if (m_nWaterVolunmeMax == -1)
	{
		auto pitemdef = GetDefManagerProxy()->getItemDef(m_Def->ID);
		m_nWaterVolunmeMax = atoi(pitemdef->para.c_str());
	}
}

void BlockWaterStorage::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	WCoord origin = blockpos * BLOCK_SIZE;
	coldetect->addObstacle(origin, origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

WorldWaterStorage* BlockWaterStorage::createContainer(World* pworld, const WCoord& blockpos)
{
	if (pworld->isRemoteMode())
	{
		return nullptr;
	}
	if (isCoreBlock(pworld, blockpos))
	{
		return SANDBOX_NEW(WorldWaterStorage, blockpos);
	}
	else
	{
		return nullptr;
	}
}

void BlockWaterStorage::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{

	if (data.m_SharedSectionData->getBlock(blockpos).getData() & 4)
		return;
	auto corePos = blockpos;
	if (!isCoreBlock(data.m_World, corePos)) corePos = getCoreBlockPos(data.m_World, blockpos);
	Super::createBlockMesh(data, corePos, poutmesh);
}

void BlockWaterStorage::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (pworld == NULL || player == NULL)
	{
		return;
	}
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	ModelBlockMaterial::onBlockPlacedBy(pworld, blockpos, player);
	int placeDir = playerTmp->getCurPlaceDir();
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y)
	{
		placeDir = DIR_NEG_X;
	}
	pworld->setBlockData(blockpos, placeDir);
}

int BlockWaterStorage::getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player)
{
	ClientPlayer* playerTmp = player->GetPlayer();
	if (playerTmp) 
		return playerTmp->getCurPlaceDir();

	return 0;
}

int BlockWaterStorage::getPlaceBlockData(World* pworld, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	int placeDir = face;
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y)
	{
		placeDir = DIR_NEG_X;
	}
	return placeDir;
}

void BlockWaterStorage::onBlockAdded(World* pworld, const WCoord& blockpos)
{
	if (pworld == NULL)
	{
		return;
	}

	// 只有核心方块才进行处理
	int blockData = pworld->getBlockData(blockpos);
	if (isCoreBlock(pworld, blockpos))
	{
		ModelBlockMaterial::onBlockAdded(pworld, blockpos);

		int placeDir = blockData & 3;
		std::vector<WCoord> poslist;
		getBlockRange(poslist, placeDir, blockpos);
		for (auto& pos : poslist)
		{
			pworld->setBlockAll(pos + blockpos, m_BlockResID, 4 | placeDir);
		}
	}
}

void BlockWaterStorage::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	if (pworld == NULL)
	{
		return;
	}

	int placeDir = blockdata & 3;
	if (blockdata & 4)  //普通的方块
	{
		WCoord basePos = getCoreBlockPos(pworld, blockpos, blockdata);
		if (basePos.y >= 0)
		{
			pworld->setBlockAir(basePos);
		}
	}
	else
	{
		ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);

		//WorldWaterStorage* canvasContainer = dynamic_cast<WorldWaterStorage*>(pworld->getContainerMgr()->getContainer(blockpos));
		//if (canvasContainer)
		//{
		//	
		//}

		pworld->getContainerMgr()->destroyContainer(blockpos);
		clearNormalBlock(pworld, blockpos, blockdata);
	}

}

void BlockWaterStorage::clearNormalBlock(World* pworld, const WCoord& blockpos, int placeDir)
{
	std::vector<WCoord> poslist;
	getBlockRange(poslist, placeDir, blockpos);
	for (auto& pos : poslist)
	{
		WCoord curPos = pos + blockpos;
		int blockid = pworld->getBlockID(curPos);
		if (blockid == m_BlockResID && !isCoreBlock(pworld, curPos))
		{
			pworld->setBlockData(curPos, 12, 0);
			pworld->setBlockAir(curPos);
		}
	}
}

WorldContainer* BlockWaterStorage::getCoreContainer(World* pworld, const WCoord& blockpos)
{
	int blockid = pworld->getBlockID(blockpos);
	if (blockid != m_Def->ID)
	{
		return NULL;
	}
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	//WorldWaterStorage* container = static_cast<WorldWaterStorage*>(pworld->getContainerMgr()->getContainer(corePos));
	//return container;
	return sureContainer(pworld, corePos);
}

WCoord BlockWaterStorage::getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata/* =-1 */)
{
	if (blockdata == -1 && pworld)
	{
		blockdata = pworld->getBlockData(blockpos);
	}
	
	// 如果是核心方块，直接返回当前位置
	if (!(blockdata & 4))
	{
		return blockpos;
	}
	
	int direction = blockdata & 3;
	// 获取扩展方向
	WCoord extendDir = WCoord::infinity;
	if (m_Def->ID == SmallDewCollectorID)
	{
		extendDir = changeWithDirection(SmallDewCollectorExtendDir, direction);
	}
	else if (m_Def->ID == MiddleDewCollectorID)
	{
		extendDir = changeWithDirection(MiddleDewCollectorExtendDir, direction);
	}
	
	// 如果没有有效的扩展方向，返回无效位置
	if (extendDir == WCoord::infinity)
	{
		return WCoord(0, -1, 0);
	}

	int xMaxValue = 0;
	int yMaxValue = 0;
	int zMaxValue = 0;

	int xStart = 0;
	int yStart = 0;
	int zStart = 0;

	int xExtend = abs(extendDir.x);
	int xDir = 1;
	if (extendDir.x > 0) xDir = -1;
	for (int i = 1; i <= xExtend; i++)
	{
		WCoord curPos = blockpos + WCoord(i * xDir, 0, 0);
		int blockid = pworld->getBlockID(curPos);
		if (blockid != m_Def->ID) 
		{
			xExtend = i;
			break;
		}
		if (!(pworld->getBlockData(curPos) & 4))
		{
			return curPos;
		}
	}

	int zExtend = abs(extendDir.z);
	int zDir = 1;
	if (extendDir.z > 0) zDir = -1;
	for (int i = 1; i <= zExtend; i++)
	{
		WCoord curPos = blockpos + WCoord(0, 0, i * zDir);
		int blockid = pworld->getBlockID(curPos);
		if (blockid != m_Def->ID) 
		{
			zExtend = i;
			break;
		}
		if (!(pworld->getBlockData(curPos) & 4))
		{
			return curPos;
		}
	}

	int yExtend = abs(extendDir.y);
	int yDir = 1;
	if (extendDir.y > 0) yDir = -1;
	for (int i = 1; i <= yExtend; i++)
	{
		WCoord curPos = blockpos + WCoord(0, i * yDir, 0);
		int blockid = pworld->getBlockID(curPos);
		if (blockid != m_Def->ID) 
		{
			yExtend = i;
			break;
		}
		if (!(pworld->getBlockData(curPos) & 4))
		{
			return curPos;
		}
	}	

	for (int x = 1; x < xExtend; x++)
	{
		for (int y = 1; y < yExtend; y++)
		{
			for (int z = 1; z < zExtend; z++)
			{
				WCoord curPos = blockpos + WCoord(x * xDir, y * yDir, z * zDir);
				int blockid = pworld->getBlockID(curPos);
				if (blockid != m_Def->ID) break;
				if (!(pworld->getBlockData(curPos) & 4))
				{
					return curPos;
				}
			}
		}
	}

	// if (extendDir.x < 0) xStart = extendDir.x;
	// else xMaxValue = extendDir.x;
	
	// if (extendDir.z < 0) zStart = extendDir.z;
	// else zMaxValue = extendDir.z;
	
	// if (extendDir.y < 0) yStart = extendDir.y;
	// else yMaxValue = extendDir.y;
	
	// for (int x = xStart; x <= xMaxValue; x++)
	// {
	// 	for (int y = yStart; y <= yMaxValue; y++)
	// 	{
	// 		for (int z = zStart; z <= zMaxValue; z++)
	// 		{
	// 			WCoord curPos = blockpos + WCoord(x, y, z);
	// 			int blockid = pworld->getBlockID(curPos);
	// 			if (blockid != m_Def->ID) break;
	// 			if (!(pworld->getBlockData(curPos) & 4))
	// 			{
	// 				return curPos;
	// 			}
	// 		}
	// 	}
	// }
	
	// 如果上述方法都找不到核心方块，返回无效位置
	return WCoord(0, -1, 0);
}

void BlockWaterStorage::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */)
{
	//int ishead = blockdata & 4;
	//if (!ishead)
		ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance);
}

bool BlockWaterStorage::isCoreBlock(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
		return false;

	int blockData = pworld->getBlockData(blockpos);
	return (blockData & 4) == 0;
}

int BlockWaterStorage::getCoreBlockData(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
		return 0;
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	return pworld->getBlockData(corePos);
}

WorldWaterStorage* BlockWaterStorage::sureContainer(World* pworld, const WCoord& blockpos)
{
	WorldWaterStorage* container = dynamic_cast<WorldWaterStorage*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (!container)
	{
		container = SANDBOX_NEW(WorldWaterStorage, blockpos);
		pworld->getContainerMgr()->spawnContainer(container);
	}
	return container;
}

void BlockWaterStorage::getBlockRange(std::vector<WCoord>& rangelist, int placeDir, const WCoord& blockpos, bool isIncludeSelf)
{
	int xstart = 0;
	int xend = 0;
	int ystart = 0;
	int yend = 0;
	int zstart = 0;
	int zend = 0;

	WCoord extendDir = WCoord::infinity;
	if (m_Def->ID == SmallDewCollectorID)
	{
		extendDir = changeWithDirection(SmallDewCollectorExtendDir, placeDir);
	}
	else if (m_Def->ID == MiddleDewCollectorID)
	{
		extendDir = changeWithDirection(MiddleDewCollectorExtendDir, placeDir);
	}
	//其他模式
	if (extendDir.x >= 0) xstart = 0, xend = extendDir.x;
	else xstart = extendDir.x, xend = 0;
	if (extendDir.y >= 0) ystart = 0, yend = extendDir.y;
	else ystart = extendDir.y, yend = 0;
	if (extendDir.z >= 0) zstart = 0, zend = extendDir.z;
	else zstart = extendDir.z, zend = 0;

	for (int x = xstart; x <= xend; x++)
	{
		for (int y = ystart; y <= yend; y++)
		{
			for (int z = zstart; z <= zend; z++)
			{
				if ((x == 0 && y == 0 && z == 0) && !isIncludeSelf)
					continue;
				rangelist.push_back(WCoord(x, y, z));
			}
		}
	}
}

int BlockWaterStorage::getCurWaterVolume(World* pworld, const WCoord& blockpos)
{
	auto pcontainer = getCoreContainer(pworld, blockpos);
	if (pcontainer)
	{
		WorldWaterStorage* container = dynamic_cast<WorldWaterStorage*>(pcontainer);
		if(container) return container->getCurWaterVolume();
	}
	return 0;
}

void BlockWaterStorage::addWaterVolume(World* pworld, const WCoord& blockpos, int value)
{
	auto pcontainer = getCoreContainer(pworld, blockpos);
	if (pcontainer)
	{
		WorldWaterStorage* container = static_cast<WorldWaterStorage*>(pcontainer);
		if (container) container->addWater(value);
	}
}

int BlockWaterStorage::getCurWaterVolumeForLua(const WCoord& blockpos)
{
	return getCurWaterVolume(g_pPlayerCtrl->getWorld(), blockpos);
}

void BlockWaterStorage::addWaterVolumeForLua(const WCoord& blockpos, int value)
{
	addWaterVolume(g_pPlayerCtrl->getWorld(), blockpos, value);
}