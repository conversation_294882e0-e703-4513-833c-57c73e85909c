#include "GameAnalytics.h"

#include <iostream>
#include <type_traits>

using namespace thinkingdata;

// 全局变量定义
GameAnalytics* g_pGameAnalytics = nullptr;

GameAnalytics::CommonProperties GameAnalytics::s_commonProps;
bool GameAnalytics::m_initialized = false;

// 构造函数和析构函数实现
GameAnalytics::GameAnalytics() {
  // 构造函数实现
}

GameAnalytics::~GameAnalytics() {
  // 析构函数实现
}

// 初始化方法实现
bool GameAnalytics::Init(const std::string& device_id, int env) {
  // 如果全局变量已经初始化，直接返回true
  if (g_pGameAnalytics != nullptr) {
    return true;
  }
  
  // 创建全局对象
  g_pGameAnalytics = new GameAnalytics();
  
  // 初始化埋点SDK
  std::string appid = "a12c62532cf54941ba8cb3cb63784b07"; // 数据文件路径
  std::string server_url = "https://tga.mini1.cn";
  bool is_login_id = false; // device_id 不是登录ID
  int max_staging_record_count = 10000; // 最大暂存记录数

  // 配置ThinkingData SDK
  TDConfig td_config;
  td_config.appid = appid;
  td_config.server_url = server_url;
  td_config.enableAutoCalibrated = true; // 自动时间校准
  td_config.mode = TDMode::TD_NORMAL;
  td_config.databaseLimit = max_staging_record_count;
  td_config.dataExpression = 15;

  bool success = ThinkingAnalyticsAPI::Init(td_config);
  
  if (success) {
    m_initialized = true;
    s_commonProps.env = env;                // 设置环境
    s_commonProps.device_id = device_id;    // 设置设备ID
    s_commonProps.log_id = genLogid();
    // 可以在这里设置其他默认属性
  }

  ThinkingAnalyticsAPI::EnableLog(true);
  
  return success;
}
 

 

void GameAnalytics::Login(const std::string& login_id) {
    ThinkingAnalyticsAPI::Login(login_id);
    s_commonProps.uin = login_id;
}

void GameAnalytics::Logout() {
    m_initialized = false;
    // 调用SDK登出
    ThinkingAnalyticsAPI::LogOut();
}

void GameAnalytics::TrackEvent(const std::string& event_name,
                             const std::string& param1,
                             const std::string& param2,
                             const std::string& param3,
                             int num_param1,
                             int num_param2,
                             bool bool_param1) {
    if (!m_initialized) {
        std::cout << "[GameAnalytics] Not initialized" << std::endl;
        return;
    }

    if (event_name.empty()) {
        return;
    }
    
    thinkingdata::TDJSONObject properties = createCommonProperties();
    
    if (!param1.empty()) {
        properties.SetString("param1", param1);
    }
    
    if (!param2.empty()) {
        properties.SetString("param2", param2);
    }
    
    if (!param3.empty()) {
        properties.SetString("param3", param3);
    }
    
    if (num_param1 != 0) {  // 假设0表示无效值
        properties.SetNumber("num_param1", num_param1);
    }
    
    if (num_param2 != 0) {  // 假设0表示无效值
        properties.SetNumber("num_param2", num_param2);
    }
    
    if (bool_param1) {
        properties.SetBool("bool_param1", bool_param1);
    }
    
    ThinkingAnalyticsAPI::Track(event_name, properties);
    ThinkingAnalyticsAPI::Flush();
}

void GameAnalytics::TrackPlayerCreated(const std::string& character_id, const std::string& character_class) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("character_id", character_id);
    properties.SetString("character_class", character_class);
    ThinkingAnalyticsAPI::Track("PlayerCreated", properties);
}

void GameAnalytics::TrackPlayerLoaded(const std::string& character_id) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("character_id", character_id);
    ThinkingAnalyticsAPI::Track("PlayerLoaded", properties);
}

thinkingdata::TDJSONObject GameAnalytics::createCommonProperties() {
    thinkingdata::TDJSONObject properties;

    properties.SetString("session_id", s_commonProps.session_id);
    properties.SetNumber("session_start_time", s_commonProps.session_start_time);
    
    properties.SetNumber("env", s_commonProps.env);
    properties.SetString("ip_address", s_commonProps.ip_address);
    properties.SetString("app_version", s_commonProps.app_version);
    properties.SetNumber("apiid", s_commonProps.apiid);
    properties.SetString("os_type", s_commonProps.os_type);
    properties.SetString("country", s_commonProps.country);
    properties.SetString("province", s_commonProps.province);
    
    properties.SetString("device_id", s_commonProps.device_id);
    properties.SetString("uin", s_commonProps.uin);
    properties.SetString("log_id", s_commonProps.log_id);
    
    properties.SetString("game_session_id", s_commonProps.game_session_id);
    properties.SetNumber("game_session_start_time", s_commonProps.game_session_start_time);
 
    return properties;
}


// 模板函数实现
template<typename T>
void GameAnalytics::SetUserProfile(const std::string& property_name, const T& value) {
    TDJSONObject userProps;
    if constexpr (std::is_same_v<T, std::string>) {
        userProps.SetString(property_name, value);
    } else if constexpr (std::is_same_v<T, int>) {
        userProps.SetNumber(property_name, value);
    } else if constexpr (std::is_same_v<T, bool>) {
        userProps.SetBool(property_name, value);
    } else if constexpr (std::is_same_v<T, double> || std::is_same_v<T, float>) {
        userProps.SetNumber(property_name, static_cast<double>(value));
    }
    ThinkingAnalyticsAPI::UserSet(userProps);
}

// 显式实例化常用类型
template void GameAnalytics::SetUserProfile<std::string>(const std::string&, const std::string&);
template void GameAnalytics::SetUserProfile<int>(const std::string&, const int&);
template void GameAnalytics::SetUserProfile<bool>(const std::string&, const bool&);
template void GameAnalytics::SetUserProfile<double>(const std::string&, const double&);
template void GameAnalytics::SetUserProfile<float>(const std::string&, const float&);


std::string GameAnalytics::genLogid() {
    return "";
}