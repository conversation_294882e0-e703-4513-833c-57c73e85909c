--[[
    冒险模式ui分布加载管理
    code_by:liya
]]

--声明
local AdventureUIRequire = Class("AdventureUIRequire")

--初始化
function AdventureUIRequire:Init()
    
end

--开始加载阶段（LuaPreLoadMgr里调用）
function AdventureUIRequire:GetStartLoadingRequires()
    local list = {

    }
    return list
end

--进入主页阶段（MiniLobbyFrame_OnShow和ShowMiniLobby里调用）
function AdventureUIRequire:GetEnterLobbyRequires()
    local list = 
    {
        "miniui/module/adventure/TaskSystem/TaskSystemManager.lua",
        "miniui/module/adventure/RoleAttrFrame/RoleAttrManager.lua",
        "miniui/module/adventure/forceguide/ForceGuideUIMgr.lua",
        "miniui/module/adventure/forceguide/ForceGuideStepControl.lua",
        "miniui/module/adventure/TaskSystem/TaskTrack/TaskTrackCtrl.lua",
        "miniui/module/adventure/TaskSystem/TaskTrack/TaskTrackModel.lua",
        "miniui/module/adventure/TaskSystem/TaskTrack/TaskTrackView.lua",
        "miniui/module/adventure/TaskSystem/TaskFinish/TaskFinishCtrl.lua",
        "miniui/module/adventure/compose/mainrune/MiniUIRuneMain.lua",
        "miniui/module/adventure/compose/mainrune/MiniUIRuneMainView.lua",
        "miniui/module/adventure/compose/mainrune/MiniUIMaterialView.lua",
        "miniui/module/adventure/MusicAdventurePlayer.lua",
        "miniui/module/adventure/StarStation/cargo/StarStationCargoManager.lua",
        "miniui/module/adventure/compose/helper/MiniUIComposeInterface.lua",
        "miniui/module/adventure/NpcTradeStore/NpcTradeStoreMgr.lua",
        "miniui/module/adventure/HpBarFrame/HpBarFrameCtrl.lua",
        "miniui/module/adventure/HpBarFrame/HpBarFrameModel.lua",
        "miniui/module/adventure/HpBarFrame/HpBarFrameView.lua",
        "miniui/module/adventure/HpBarFrame/HpBarFrameAutoGen.lua",
        "miniui/module/adventure/newItemTipsFrame/newItemTipsFrameCtrl.lua",
        "miniui/module/adventure/newItemTipsFrame/newItemTipsFrameModel.lua",
        "miniui/module/adventure/newItemTipsFrame/newItemTipsFrameView.lua",
        "miniui/module/adventure/newItemTipsFrame/newItemTipsFrameAutoGen.lua",
    }
    return list
end

--地图加载阶段（PlayMainFrame_OnShow里调用）
function AdventureUIRequire:GetMapLoadingRequires()
    local list = 
    {
        "miniui/module/adventure/compose/main/MiniUIComposeMain.lua",
        "miniui/module/adventure/compose/main/MiniUIComposeMainView.lua",
        "miniui/module/adventure/compose/craft/MiniUICraftMain.lua",
        "miniui/module/adventure/compose/craft/MiniUICraftMainView.lua",
        "miniui/module/adventure/compose/repair/MiniUIItemRepairMain.lua",
        "miniui/module/adventure/compose/runeidentify/MiniUIRuneIdentifyMain.lua",
        "miniui/module/adventure/compose/runeinlay/MiniUIRuneInlayMain.lua",
        "miniui/module/adventure/compose/runecompound/MiniUIRuneCompoundMain.lua",
        "miniui/module/adventure/compose/test/test_text.lua",
        "miniui/module/adventure/furnace/MiniUIFurnaceMain.lua",
        "miniui/module/adventure/furnace/MiniUIFurnaceModel.lua",
        "miniui/module/adventure/furnace/MiniUIFurnaceView.lua",
        "miniui/module/adventure/TaskSystem/TaskMain/TaskMainCtrl.lua",
        "miniui/module/adventure/TaskSystem/TaskMain/TaskMainModel.lua",
        "miniui/module/adventure/TaskSystem/TaskMain/TaskMainView.lua",
        "miniui/module/adventure/TaskSystem/TaskSelect/TaskSelectCtrl.lua",
        "miniui/module/adventure/TaskSystem/TaskSelect/TaskSelectModel.lua",
        "miniui/module/adventure/TaskSystem/TaskSelect/TaskSelectView.lua",
        "miniui/module/adventure/TaskSystem/TaskFinish/TaskObjectiveFinishCtrl.lua",

        "miniui/module/adventure/TaskSystem/TaskMutilReward/task_mutil_rewardCtrl.lua",
        "miniui/module/adventure/TaskSystem/TaskMutilReward/task_mutil_rewardModel.lua",
        "miniui/module/adventure/TaskSystem/TaskMutilReward/task_mutil_rewardView.lua",
        "miniui/module/adventure/TaskSystem/TaskMutilReward/task_mutil_rewardAutoGen.lua",
  
        "miniui/module/adventure/StarStation/info/StarStationInfoFrameAutoGen.lua",
        "miniui/module/adventure/StarStation/info/StarStationInfoFrameCtrl.lua",
        "miniui/module/adventure/StarStation/info/StarStationInfoFrameView.lua",
        "miniui/module/adventure/StarStation/info/StarStationInfoFrameModel.lua",
    }
    return list 
end

--初始化UI模块阶段
function AdventureUIRequire:GetUIModuleRequires()
    local list = 
    {
        AltarAwardsFrame = "miniui/module/adventure/AltarAwardsFrame",
        GuidenMain = "miniui/module/adventure/BeginGuidence/GuidenMain",
        BuffIconFramePage = "miniui/module/adventure/BuffFramePage",
        BuffFramePage = "miniui/module/adventure/BuffFramePage",
        ComboAtkTimer = "miniui/module/adventure/ComboAtkTimer",
        Telescope = "miniui/module/adventure/Telescope",
        -- 食谱界面
        Cookbook = "miniui/module/adventure/Cookbook",
        CreateMonsterFramePage = "miniui/module/adventure/CreateMonsterFramePage",
        curtainSignFrame = "miniui/module/adventure/curtainSignFrame",
        digproFrame = "miniui/module/adventure/digproFrame",
        EditBookFrame = "miniui/module/adventure/EditBookFrame",
        furnaceOxygenFrame = "miniui/module/adventure/furnaceOxygenFrame",
        inductionFrame = "miniui/module/adventure/inductionFrame",
        InstructionParserFramePage = "miniui/module/adventure/InstructionParserFramePage",
        ItemProcessingFrame = "miniui/module/adventure/ItemProcessingFrame",
        KeyDescriptionFramePage = "miniui/module/adventure/KeyDescriptionFramePage",
        lettersFramePage = "miniui/module/adventure/lettersFramePage",
        lightSensorFrame = "miniui/module/adventure/lightSensorFrame",
        ManualEmitterMain = "miniui/module/adventure/ManualEmitterMain",
        newSleepNoticeFrame = "miniui/module/adventure/newSleepNoticeFrame",
        NpcTradeFrame = "miniui/module/adventure/NpcTradeFrame",
        NpcTradeStore = "miniui/module/adventure/NpcTradeStore",
        plotFrame = "miniui/module/adventure/plotFrame",
        PotFrame = "miniui/module/adventure/PotFrame",
        RoleAttrFrame = "miniui/module/adventure/RoleAttrFrame",
        RoleFrame = "miniui/module/adventure/RoleFrame",
        signalParserFrame = "miniui/module/adventure/signalParserFrame",
        -- StarStationInfoFrame = "miniui/module/adventure/StarStationInfoFrame",
        StarStationStarTravelFrame = "miniui/module/adventure/StarStation/travel",
        StarStationTransferFrame = "miniui/module/adventure/StarStation/transfer",
        StarStationCargoFrame = "miniui/module/adventure/StarStation/cargo",
        StarStationLoadingFrame = "miniui/module/adventure/StarStation/loading",
        storageBoxFrame = "miniui/module/adventure/storageBoxFrame",
        StoveFrame = "miniui/module/adventure/StoveFrame",
        TerrainMap = "miniui/module/adventure/TerrainMap",
        thermometerFrame = "miniui/module/adventure/thermometerFrame",
        TombStoneFramePage = "miniui/module/adventure/TombStoneFramePage",
        ToolBoxFrame = "miniui/module/adventure/ToolBoxFrame",
        TreasureMainMap = "miniui/module/adventure/TreasureMainMap",
        --扩展背包界面
        ExtBackPackFrame = "miniui/module/adventure/ExtBackPackFrame",
		--@soc2024
		BG = "miniui/module/adventure/TaskSystem/BG",
        itemrepair = "miniui/module/itemrepair",
        furancemain = "miniui/module/furancemain",
        topdragui = "miniui/module/topdragui",
        decomposition = "miniui/module/decomposition",
        researchpage = "miniui/module/researchpage",
        territory = "miniui/module/territory",
		socchatmain = "miniui/module/socchatmain",
		waterstorage = "miniui/module/waterstorage",
    }
    return list 
end

--初始化UI模块额外阶段
function AdventureUIRequire:GetUIModuleExtraRequires()
    local list = {
    }

    return list
end