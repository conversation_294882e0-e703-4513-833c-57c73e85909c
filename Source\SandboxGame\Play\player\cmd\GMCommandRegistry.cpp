#include "GMCommandRegistry.h"

GMCommandRegistry* GMCommandRegistry::s_instance = nullptr;

void GMCommandRegistry::initializeCommands() {
    //m_commandTrie m_commandTrie;
    
    // 物品相关命令
    m_commandTrie.insertCommand("ITEM", {"<itemId>", "[count]"});
    m_commandTrie.insertCommand("UNLOCKITEM", {"<itemId>"});
    m_commandTrie.insertCommand("UNLOCKTYPE", {"<typeId>"});
    
    // 传送命令
    m_commandTrie.insertCommand("TP", {"<x>", "<y>", "<z>"});
    
    // MOD相关命令
    m_commandTrie.insertCommand("MOD", {"<modName>"});
    m_commandTrie.insertCommand("MODMOB", {"<modMobId>"});
    m_commandTrie.insertCommand("ENMOD", {"<modName>"});
    m_commandTrie.insertCommand("EXPMOD", {"<modName>"});
    
    // 蓝图相关命令
    m_commandTrie.insertCommand("LOADBP", {"<blueprintFile>"});
    
    // 生物相关命令
    m_commandTrie.insertCommand("MOB", {"<mobId>", "[count]"});
    m_commandTrie.insertCommand("MOBGEN", {"<params>"});
    m_commandTrie.insertCommand("CLEARMOB");
    m_commandTrie.insertCommand("SETTIMESCALEMOBS", {"<scale>"});
    
    // 宠物相关命令
    m_commandTrie.insertCommand("PET", {"<petId>"});
    m_commandTrie.insertCommand("PET_NEW", {"<petId>"});
    m_commandTrie.insertCommand("ACCHORSE");
    m_commandTrie.insertCommand("HORSEEGG");
    
    // 天气与环境相关命令
    m_commandTrie.insertCommand("RAIN");
    m_commandTrie.insertCommand("THUNDER");
    m_commandTrie.insertCommand("LIGHTNING", {"<x>", "<y>", "<z>"});  
    m_commandTrie.insertCommand("SNOW");
    m_commandTrie.insertCommand("WEATHER", {"<type>"});
    m_commandTrie.insertCommand("DUSTSTORM", {"<enable>"});  
    m_commandTrie.insertCommand("BLIZZARD", {"<power>", "[idleTick]"});
    m_commandTrie.insertCommand("AURORA");
    
    // 时间相关命令
    m_commandTrie.insertCommand("TIME", {"<hour>"});
    m_commandTrie.insertCommand("ADDWTIME", {"<time>"});
    m_commandTrie.insertCommand("TIMESPEED", {"<speed>"});
    
    // 玩家属性相关命令
    m_commandTrie.insertCommand("HP", {"<value>", "[overflowable]"});
    m_commandTrie.insertCommand("HPOVER", {"<value>"});
    m_commandTrie.insertCommand("ST", {"<value>"});
    m_commandTrie.insertCommand("STOVER", {"<value>"});
    m_commandTrie.insertCommand("TOGGLESTRENGTH");
    m_commandTrie.insertCommand("EXP", {"<value>", "[playerUin]"});
    m_commandTrie.insertCommand("ADDFOOD", {"<value>"});
    m_commandTrie.insertCommand("ADDFOODSAT", {"<value>"});
    m_commandTrie.insertCommand("MOVESPEED", {"<speed>"});
    m_commandTrie.insertCommand("FLYSPEED", {"<speed>"});
    m_commandTrie.insertCommand("VISION", { "<value>" });
    
    // 效果相关命令
    m_commandTrie.insertCommand("FX", {"<effectId>", "<x>", "<y>", "<z>"});  
    m_commandTrie.insertCommand("BUFF", {"<buffId>", "[level]"});
    m_commandTrie.insertCommand("RMBUFF", {"<buffId>"});
    m_commandTrie.insertCommand("ENCH", {"<slot>", "<enchantId>", "[level]"});
    m_commandTrie.insertCommand("RMENCH", {"<enchantId>"});
    m_commandTrie.insertCommand("ENCHS");
    
    // 方块相关命令
    m_commandTrie.insertCommand("DISPORE");
    m_commandTrie.insertCommand("CLRBLOCK");
    m_commandTrie.insertCommand("BTICK", {"<tick>"});
    m_commandTrie.insertCommand("TRACKBLOCK", {"<enable>"});
    m_commandTrie.insertCommand("TEST_BLOCK", {"<blockId>"});
    m_commandTrie.insertCommand("CLEARBLOCK");
    
    // 视觉相关命令
    m_commandTrie.insertCommand("VIEWRADIUS", {"<radius>"});
    m_commandTrie.insertCommand("SCR", {"<brightness>"});
    m_commandTrie.insertCommand("FOG");
    m_commandTrie.insertCommand("FOGR", {"<range>"});
    m_commandTrie.insertCommand("FOGT", {"<time>"});
    m_commandTrie.insertCommand("TORCH", {"<r>", "<g>", "<b>"});
    m_commandTrie.insertCommand("SCN", {"<r>", "<g>", "<b>"});
    m_commandTrie.insertCommand("SCD", {"<r>", "<g>", "<b>"});
    
    // 游戏模式与规则
    m_commandTrie.insertCommand("GAMEMODE", {"<mode>"});
    m_commandTrie.insertCommand("GOD");
    m_commandTrie.insertCommand("RULE", {"<rule>", "<value>"});
    m_commandTrie.insertCommand("PERMITS", {"<permission>"});
    m_commandTrie.insertCommand("GAMEVAR", {"<variable>", "<value>"});
    
    // 调试相关命令
    m_commandTrie.insertCommand("STATISTIC");
    m_commandTrie.insertCommand("PROFILE");
    m_commandTrie.insertCommand("PROFILER");
    m_commandTrie.insertCommand("PHY");
    m_commandTrie.insertCommand("AIRWALL");
    m_commandTrie.insertCommand("DBFX");
    m_commandTrie.insertCommand("ENABLEBOXDEBUG");
    m_commandTrie.insertCommand("ENABLETLBOX");
    m_commandTrie.insertCommand("COMBOLOG");
    
    // 模型与动画相关
    m_commandTrie.insertCommand("MODEL", {"<modelId>"});
    m_commandTrie.insertCommand("CAPMODEL");
    m_commandTrie.insertCommand("CAPMODEL2");
    m_commandTrie.insertCommand("PINDEX", {"<index>"});
    m_commandTrie.insertCommand("PLAYMOTION", {"<motionId>"});
    m_commandTrie.insertCommand("ANIM", {"<animId>", "[loop]"});
    m_commandTrie.insertCommand("DANCE", {"<danceId>"});
    
    // 脚本相关命令
    m_commandTrie.insertCommand("GM", {"<funcName>", "[param]"});
    m_commandTrie.insertCommand("LOADLUA", {"<fileName>"});
    m_commandTrie.insertCommand("CALL", {"<funcName>", "[params...]"});
    m_commandTrie.insertCommand("DEVLUA", {"<script>"});
    m_commandTrie.insertCommand("LITECALL", {"<funcName>"});
    m_commandTrie.insertCommand("SSCMD", {"<funcName>", "[params...]"});
    
    // 服务器相关命令
    m_commandTrie.insertCommand("SSTAT");
    m_commandTrie.insertCommand("SCLOSE");
    m_commandTrie.insertCommand("SLOG");
    m_commandTrie.insertCommand("SPERF");
    m_commandTrie.insertCommand("SYNC", {"<type>"});
    
    // 其他功能命令
    m_commandTrie.insertCommand("FIREWORK", {"<type>"});
    m_commandTrie.insertCommand("PORTAL");
    m_commandTrie.insertCommand("CHEST");
    m_commandTrie.insertCommand("BLOCKNUM");
    m_commandTrie.insertCommand("ADDMINICOIN", {"<amount>"});
    m_commandTrie.insertCommand("MUTATE");
    m_commandTrie.insertCommand("SHAPE");
    m_commandTrie.insertCommand("RUNE", {"<runeId>"});
    m_commandTrie.insertCommand("TASK", {"<taskId>"});
    m_commandTrie.insertCommand("CLEARSOUNDCACHE");
    
    // 图形质量相关
    m_commandTrie.insertCommand("SWITCH_QUALITY");
    m_commandTrie.insertCommand("SWITCH_EFFECT_QUALITY");
    m_commandTrie.insertCommand("SWITCH_SKYBOX");
    m_commandTrie.insertCommand("GODRAY");
    m_commandTrie.insertCommand("SHADOW", {"<enable>"});
    m_commandTrie.insertCommand("REFLECT", {"<enable>"});
    m_commandTrie.insertCommand("RELOAD");
    
    // 武器相关命令
    m_commandTrie.insertCommand("ADS");
    m_commandTrie.insertCommand("ADST", {"<time>"});
    m_commandTrie.insertCommand("LW");
    m_commandTrie.insertCommand("AFA");
    
    // 实验性功能
    m_commandTrie.insertCommand("SANDBOX");
    m_commandTrie.insertCommand("GLTF", {"<command>", "[params...]"});
    m_commandTrie.insertCommand("VEH", {"<itemId>"});
    m_commandTrie.insertCommand("AIRDROP", {"<damageHP>", "[radius]"});
    // LLM
    m_commandTrie.insertCommand("llm", { "<type>", "[params...]" });
    
    // 建筑保护区开关
    m_commandTrie.insertCommand("CLOSEBUILDPROTECT");
    m_commandTrie.insertCommand("OPENBUILDPROTECT");

    // 调试命令
    m_commandTrie.insertCommand("MAGIC", {
        "fs_pos_offset <offset>",
        "shake [power] [duration]",
        "camera_world_pos <x> <y> <z>",
        "camera_rotation <yaw> <pitch>",
        "near <value>",
        "far <value>",
        "fov <value>"
    });
    m_commandTrie.insertCommand("CREATEAIRDROPCHEST");
}
