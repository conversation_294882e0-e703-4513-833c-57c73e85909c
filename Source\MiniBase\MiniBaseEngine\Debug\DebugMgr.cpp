#include "DebugMgr.h"

#include "GameStatic.h"
#include "Input/InputManager.h"
#include "Profiler/MemoryProfiler.h"
#include "Core/CallBacks/GlobalCallbacks.h"
#include "Modules/RenderDoc/RenderDoc.h"
#include "Utilities/Word.h"
#include "Profiler/ProfilerManager.h"
#include "File/FileUtilities.h"
#include "Serialize/TransferUtilities.h"
#include "libpdebug.h"
#include "Profiler/lua_profiler.h"
#include "ScriptVM/OgreScriptLuaVM.h"
#include "ScriptVM/LuaCall.h"
#include "Utilities/Logs/LogSystem.h"
#include "Misc/GameSetting.h"
#include "Misc/SystemInfo.h"

#include "Console/Console.h"
#include "AssetPipeline/AssetManager.h"
#include "Core/Engine.h"
#include "Animation/Skeletons/BoneNode.h"
#include "Allocator/MemoryManager.h"
#include <File/FileManager.h>
#include <AssetPipeline/Prefab.h>
#include <Core/GameObject.h>
#include <Components/Camera.h>
#include "Entity/ModelAnimationPlayer.h"

#if PLATFORM_ANDROID
#   include "Platforms/Android/AndroidSystemInfo.h"
#   include "IClientInfo.h"
#elif PLATFORM_OHOS
#   include <Platforms/OHOS/napi/file/FileUtils.h>
#   include <Platforms/OHOS/OHOSSystemInfo.h>
#   include "IClientInfo.h"
#elif PLATFORM_IOS
#   include "Platforms/IPhone/EngineInterface.h"
#endif
#include <AssetPipeline/AssetProjectSetting.h>
#include <Diagnostics/Stacktrace.h>

using namespace MINIW;
namespace Rainbow
{

	void DoParseProfilerCmd(const char* userCmdStr)
	{
		GetConsole()->ExecCommand(userCmdStr);
	}

	DebugConfig::DebugConfig()
	{
		LoadConfig();
		GetLogSystem().SetLogType((LogType)m_LogLevel); //日志是否开启
	}

	DebugConfig::~DebugConfig()
	{

	}

	template<class TransferFunction>
	void DebugConfig::Transfer(TransferFunction& transfer) {
		TRANSFER_ENUM(m_LuaDebugModel);
		TRANSFER(m_LuaDebugIp);
		TRANSFER(m_LuaDebugPort);
		TRANSFER(m_ProfilerAutoStart);
		TRANSFER(m_ProfilerToolIp);
		TRANSFER(m_ProfilerPort);
		TRANSFER(m_LogLevel);
		TRANSFER(m_PkgServer);
		TRANSFER(m_HotfixMagic);
		TRANSFER(m_pocoTestPort);
		TRANSFER(m_usePocoTest);
		TRANSFER(m_TPSCameraDistanceOffset);
		TRANSFER(m_FPSCameraOffset);
		TRANSFER(m_LogTimestamp);
	}

    void DebugConfig::LoadConfig()
    {
#if PLATFORM_WIN
        dynamic_array<UInt8> data(kMemTempAlloc);
        if (LoadFileAsBinary("DebugConfig.json", data))
        {
            DebugConfig& self = *this;
            FromJSONDataStream(data, self);
        }
        else {
#   if DEBUG_MODE || PROFILE_MODE
            //没有就自动创建一个
            core::string outputJson;
            DebugConfig& self = *this;
            ToJSONDataStream(outputJson, self);
            SaveToFile("DebugConfig.json", outputJson.c_str(), outputJson.size());
#   endif
        }

#elif PLATFORM_ANDROID || PLATFORM_OHOS

#   if PLATFORM_ANDROID
        const char* filesDir = android::systeminfo::FilesDir();
#   elif PLATFORM_OHOS
        // 安卓参考: /storage/emulated/0/Android/data/com.minitech.miniworld/files/
        const char* filesDir = FileUtils::GetWritableFilesPath().c_str();
#   endif

        //int appId = GetIClientInfo().GetAppId();
        //core::string filePath = AppendPathName(filesDir, std::to_string(appId));
        core::string filePath = AppendPathName(filesDir, "DebugConfig.json");
        dynamic_array<UInt8> data(kMemTempAlloc);
        if (LoadFileAsBinary(filePath.c_str(), data))
        {
            DebugConfig& self = *this;
            FromJSONDataStream(data, self);
        }

#elif PLATFORM_IOS
        core::string filePath = Format("%s/%s", EngineApplicationDir(), "DebugConfig.json");

        dynamic_array<UInt8> data(kMemTempAlloc);
        if (LoadFileAsBinary(filePath.c_str(), data))
        {
            DebugConfig& self = *this;
            FromJSONDataStream(data, self);
        }

#endif

    }

	void DebugMgr::OnCmd(const char* cmd, ConsoleOutputDevice* output)
	{

#if USE_PROFILER_DEBUG_CMD

		core::string cmdline = cmd;
		cmdline = ToLower(cmdline);
		std::vector<core::string> params;
		Split(cmdline, " ", params);

		if (params.size() < 1)return;

		{	
			if (params[0] == "profiler")
			{
				if (params.size() >= 2)
				{
					if (params[1] == "start")
					{
						GetProfileManager().Start();
						return;
					}

					if (params[1] == "stop")
					{
						GetProfileManager().Stop();
#if ENABLE_RAINBOW_PROFILER
						GetProfileManager().LogProfiler();
#endif
					}
				}
			}


#if ENABLE_RENDERDOC   //先放这，后面放引擎
			if (params[0] == "renderdoc")
			{
				int frame = 1;
				if (params.size() >= 2)
				{
					frame = atoi(params[1].c_str());
				}
				m_Capture = frame;  //抓多帧
			}
#endif


		}
#endif
	}

	void DebugMgr::EndGame()
	{
		
#if ENABLE_RAINBOW_PROFILER
		if (m_DebugConfig.m_ProfilerAutoStart)
		{
			GetProfileManager().LogProfiler();
			GetProfileManager().FreeAllSampleNode();
		}
#endif

	}

	bool DebugMgr::LuaShowError()
	{
		return true;
	}

	void DebugMgr::loadDebugCfg()
	{
		m_DebugConfig.LoadConfig();
	}

	float DebugMgr::GetTPSCameraDistanceOffset() {
		return m_DebugConfig.m_TPSCameraDistanceOffset;
	}

	void DebugMgr::SetTPSCameraDistanceOffset(float offset) {
		 m_DebugConfig.m_TPSCameraDistanceOffset = offset;
		 SaveDebugCfg();
	}
	void DebugMgr::SetFPSCameraOffset(float offset)
	{
		m_DebugConfig.m_FPSCameraOffset = offset;
		SaveDebugCfg();
	}

	void DebugMgr::SaveDebugCfg()
	{
#if DEBUG_MODE || PROFILE_MODE
		//没有就自动创建一个
		core::string outputJson;
		ToJSONDataStream(outputJson, m_DebugConfig);
		SaveToFile("DebugConfig.json", outputJson.c_str(), outputJson.size());
#endif
	}

	float DebugMgr::GetFPSCameraOffset()
	{
		return m_DebugConfig.m_FPSCameraOffset;
	}
	
	static void AddCreateObjecInstanceStackInfo(PPtr<Object> obj, FixedString resPath);

	DebugMgr::DebugMgr()
	{
		m_Capture = 0;
		GlobalCallbacks::Get().frameTick.Register<DebugMgr>(&DebugMgr::OnTick,this);
#if USE_CAPTURE_FRAME 
		GlobalCallbacks::Get().m_PostRenderCallbacks.Register<DebugMgr>(&DebugMgr::OnPostRender, this);
		GlobalCallbacks::Get().m_PreRenderCallbacks.Register<DebugMgr>(&DebugMgr::OnPreRender, this);
#endif
		CONSOLE_REGIST_CMD_HANDLER(DebugMgr, OnCmd, this);

#if USE_MEMORY_PROFILER
		GlobalCallbacks::Get().m_PrefabInstanceCreateCallbacks.Register(&AddCreateObjecInstanceStackInfo);
#endif
	}

	DebugMgr::~DebugMgr()
	{
		CONSOLE_UNREGIST_CMD_HANDLER(DebugMgr, OnCmd, this);

		GlobalCallbacks::Get().frameTick.Unregister<DebugMgr>(&DebugMgr::OnTick, this);
#if USE_CAPTURE_FRAME
		GlobalCallbacks::Get().m_PostRenderCallbacks.Unregister<DebugMgr>(&DebugMgr::OnPostRender, this);
		GlobalCallbacks::Get().m_PreRenderCallbacks.Unregister<DebugMgr>(&DebugMgr::OnPreRender, this);
#endif
	}
	
	static void PrintGameObject(GameObject* go,int depth,bool printComponentDetails, core::string& output, core::hash_set<InstanceID>& printObjects)
	{
		if (go)
		{
			if (printObjects.find(go->GetInstanceID()) != printObjects.end())
			{
				return;
			}

			PPtr<Object> rootObject(go->GetInstanceID());
			if (!rootObject)
			{
				return;
			}

			printObjects.insert(go->GetInstanceID());
			core::string preFix = "";
			for (size_t i = 0; i < depth; i++)
			{
				preFix = preFix + "-";
			}

			auto components = go->GetComponents();
			core::string componentDes = "";
			componentDes += "[";
			for (auto comp : components)
			{
				componentDes += Format("%s%s(id:%u);", preFix.c_str(), comp->GetTypeName(), comp->GetInstanceID());
			}
			componentDes +="]";

			output += Format("%sGameObject:%s(id:%u) Components(count:%d): %s \n", preFix.c_str(), go->GetName(), go->GetInstanceID(), components.size(), componentDes.c_str());

			if (printComponentDetails)
			{
				for (auto comp : components)
				{
					dynamic_array<UInt8> buffer;
					JSONWrite jsonWriter(kNoTransferInstructionFlags);
					comp->VirtualRedirectTransfer(jsonWriter);
					core::string jsonStr;
					jsonWriter.OutputToString(jsonStr, true);
					output += Format("%s%s(id:%u) \n %s \n", preFix.c_str(), comp->GetTypeName(), comp->GetInstanceID(), jsonStr.c_str());
				}
			}

			depth++;
			auto trans = go->GetTransform();
			for (auto child : trans->GetChildren())
			{
				if (child->IsTransform())
				{
					Transform* childTrans = dynamic_cast<Transform*>(child);
					if (childTrans)
					{
						PrintGameObject(childTrans->GetGameObject(), depth, printComponentDetails, output, printObjects);
					}
				}
			}
		}
	}

#if USE_MEMORY_PROFILER
	const int kMaxStackLogLine = 10;

	struct CreateObjectStackInfo
	{
		InstanceID InstanceId;
		FixedString resPath;
		void* stack[kMaxStackLogLine];
		UInt32 stackHash;
	};

	static std::vector<CreateObjectStackInfo> m_objectInstanceVector;

	// 打印当前还在内存中的有效实例化对象
	static void PrintCollectedObjectInstance()
	{
		core::string outputStr = "";
		const int kBufferSize = kMaxStackLogLine * 1024;
		char buffer[kBufferSize];

		InitStackTrace();

		for (auto iter = m_objectInstanceVector.begin(); iter != m_objectInstanceVector.end(); iter++)
		{
			PPtr<Object> rootObject(iter->InstanceId);
			if (rootObject.IsValid())
			{
				int sceneId = -1;
				int typeId = rootObject->GetTypeId();

				// 只有gameobject类型的对象才去查找场景ID
				if (rootObject->IsKindOf<GameObject>())
				{
					PPtr<GameObject> go = rootObject.CastTo<GameObject>();
					if (go)
					{
						if (go->GetScene() != nullptr)
						{
							sceneId = go->GetScene()->GetGameSceneId();
						}
					}
				}

				memset(buffer, 0, kBufferSize);
				GetReadableStackTrace(buffer, kBufferSize, (void**)(iter->stack), kMaxStackLogLine);
				outputStr += FormatString(" Prefab:%s, InstanceId:%u, type: %s SceneId:%d \nStack:%s\n",  iter->resPath, iter->InstanceId, rootObject->GetTypeName(), sceneId, buffer);
			}
		}

		UninitStackTrace();

		SaveToFile("ReportCurentObjectInstance.log", outputStr.c_str(), outputStr.size());
	}

	// 清理已经销毁的实例化对象
	static void CleanObjectInstanceVectors()
	{
		auto iter = m_objectInstanceVector.begin();
		while (iter != m_objectInstanceVector.end())
		{
			PPtr<Object> rootObject(iter->InstanceId);
			if (!rootObject.IsValid())
			{
				iter = m_objectInstanceVector.erase(iter);
			}
			else
			{
				iter++;
			}
		}
	}

	static void AddCreateObjecInstanceStackInfo(PPtr<Object> obj,FixedString resPath)
	{
#if USE_MEMORY_PROFILER
		CreateObjectStackInfo site;
		site.InstanceId = obj.GetInstanceID();
		site.resPath = resPath;
		site.stackHash = GetStacktrace(site.stack, kMaxStackLogLine, 3);
		m_objectInstanceVector.push_back(site);
#endif
	}

	void DebugMgr::CleanObjectInstacneDestroyed()
	{
		CleanObjectInstanceVectors();
	}

#endif

	void DebugMgr::OnTick(float delta)
	{
	
#if PLATFORM_WIN 
		InputManager& inputMgr = GetInputManager();
#if USE_MEMORY_PROFILER
		//按标签打印内存分配报告
		if (inputMgr.IsControlHold()
			//&& inputMgr.GetKeyDown(SDLK_LSHIFT)
			&& inputMgr.GetKeyUp(SDLK_1)) {

			GetMemoryProfiler()->ReportLabelMemory();

		}
		//打印内存分配堆栈按分配从大到小
		if (inputMgr.IsControlHold()
			//&& inputMgr.GetKeyDown(SDLK_LSHIFT)
			&& inputMgr.GetKeyUp(SDLK_2)) {
			GetMemoryProfiler()->ShrinkMemoryUsage();
			GetMemoryProfiler()->ReportBigSortStackMemoryLeak();

		}
#endif
#if USE_METRIC_STATICS
        if (inputMgr.IsControlHold() && inputMgr.GetKeyUp(SDLK_KP_MULTIPLY)) {
            ToggleMetricProfile();
        }
#endif

#if USE_MEMORY_PROFILER
		if (inputMgr.IsControlHold()
			//&& inputMgr.GetKeyDown(SDLK_LSHIFT)
			&& inputMgr.GetKeyUp(SDLK_5)) {

			//REPORT_PROCESS_MEMORY()

		}
#endif

#if ENABLE_RAINBOW_PROFILER
		if (inputMgr.IsControlHold()
			&& inputMgr.GetKeyUp(SDLK_8)) {
			GetProfileManager().Start();
		}
		#
		if (inputMgr.IsControlHold()
			&& inputMgr.GetKeyUp(SDLK_9)) {
			GetProfileManager().Stop();
			GetProfileManager().LogProfiler();
		}
#endif


#if USE_CAPTURE_FRAME
		if (inputMgr.IsControlHold()
			&& inputMgr.IsAltHold()
			&& inputMgr.GetKeyUp(SDLK_0)) {
			CaptureFrame();
		}
#endif

#endif

#if PLATFORM_WIN 
		if (IsAssetDevelopMode())
		{
			if (inputMgr.IsControlHold()
				//&& inputMgr.GetKeyDown(SDLK_LSHIFT)
				&& inputMgr.GetKeyUp(SDLK_t)) {
				// test effects

				core::string inPath = "prefab/test/";
				core::string fullPath = GetFileManager().ToFullPath(inPath.c_str());

				FileEntryInfoList fileList;
				UInt64 totalSize = 0;
				dynamic_array<core::string> filterExtensions;
				filterExtensions.push_back("prefab");
				GetFileSystem().Enumerate(fullPath.c_str(), &fileList, true, Rainbow::kEnumerateAll, filterExtensions);

				Rainbow::GameScene* scene = GetWorld()->GetCurrentGameScene();
				ModelAnimationPlayer* actorPlayer = nullptr;
				for (GameScene::GameObjectList::iterator iterator = scene->begin(); iterator != scene->end(); iterator++)
				{
					actorPlayer = (*iterator)->GetComponentInChildren<ModelAnimationPlayer>();

					if (actorPlayer != nullptr && actorPlayer->GetIsPlayer())
					{
						break;
					}
				}

				for (int i = 0; i < fileList.size(); i++) {
					SharePtr<Rainbow::Prefab> prefabfile = GetAssetManager().LoadAsset<Rainbow::Prefab>(fileList[i].m_path);
					if (prefabfile.IsValid() && prefabfile->IsLoaded())
					{
						Rainbow::Object* cloneObject = prefabfile->CreatObjectInstance();
						Rainbow::GameObject* go = static_cast<Rainbow::GameObject*>(cloneObject);
						auto trans = go->GetComponent<Transform>();
						trans->SetLocalPosition(actorPlayer->GetTransform()->GetWorldPosition() + Vector3f(100 * (i + 1), 0, 0));
						go->SetActive(false);
						scene->AddGameObject(go);
						go->SetActive(true);
					}
					else
					{
						WarningStringMsg("LoadPrefab asset %s is not loaded!!!", fileList[i].m_path);
					}
				}
			}


			if (inputMgr.IsControlHold()
				&& inputMgr.GetKeyUp(SDLK_p)) {
				// test prefab

				core::string fullPath = AppendPathName(GetAssetProjectSetting().GetEngineResourcePath(), "Assets/Resources/minigame");

				FileEntryInfoList fileList;
				UInt64 totalSize = 0;
				//dynamic_array<core::string> filterExtensions;
				//filterExtensions.push_back("prefab");
				//filterExtensions.push_back("mat");
				//filterExtensions.push_back("controller");
				//filterExtensions.push_back("templatemat");
				//filterExtensions.push_back("scene");
				//filterExtensions.push_back("overrideController");
				//filterExtensions.push_back("timeline");

				GetFileSystem().Enumerate(fullPath.c_str(), &fileList, true, Rainbow::kEnumerateAll, { "prefab" });

				for (int i = 0; i < fileList.size(); i++) {
					core::string path = fileList[i].m_path;
					path = path.substr(path.find("minigame/"));
					if (!path.ends_with(".prefab")) continue;

					WarningStringMsg("Try LoadPrefab asset : %s.", path.c_str());

					SharePtr<Rainbow::Prefab> prefabfile = GetAssetManager().LoadAsset<Rainbow::Prefab>(path.c_str());
					if (prefabfile.IsValid() && prefabfile->IsLoaded())
					{
						WarningStringMsg("LoadPrefab asset %s success!!!,try to create instance", path.c_str());
						Rainbow::Object* cloneObject = prefabfile->CreatObjectInstance();
					}
					else
					{
						WarningStringMsg("LoadPrefab asset %s is not loaded!!!", path.c_str());
					}
				}
			}

			// 打印当前场景的gameobject调试信息
			if (inputMgr.IsControlHold()
				//&& inputMgr.GetKeyDown(SDLK_LSHIFT)
				&& inputMgr.GetKeyUp(SDLK_d)) {

				//auto allscenes = GetWorld()->GetGameScenes();
				auto curScene = GetWorld()->GetCurrentGameScene();
				auto scene = curScene;
				int index = 0;
				//for (auto scene : allscenes)
				{
					if (scene)
					{
						core::hash_set<InstanceID> printObjectSet;
						core::string outputStr;
						outputStr += Format("==Start Print Scene Summary(%d) path:%s isCurScene:%d\n", scene->GetGameSceneId(), scene->GetPath().c_str(), curScene == scene ? 1 : 0);
						for (GameScene::GameObjectList::iterator iterator = scene->begin(); iterator != scene->end(); iterator++)
						{
							PrintGameObject(iterator->GetData(), 0,false, outputStr, printObjectSet);
						}
						outputStr += Format("==End Print Scene Summary(%d) path:%s isCurScene:%d\n", scene->GetGameSceneId(), scene->GetPath().c_str(), curScene == scene ? 1 : 0);
						SaveToFile("ReportCurentSceneSummary.log", outputStr.c_str(), outputStr.size());

						WarningStringMsg(outputStr.c_str());


						printObjectSet.clear();
						outputStr = "";
						outputStr += Format("==Start Print Scene Details(%d) path:%s isCurScene:%d\n", scene->GetGameSceneId(), scene->GetPath().c_str(), curScene == scene ? 1 : 0);
						for (GameScene::GameObjectList::iterator iterator = scene->begin(); iterator != scene->end(); iterator++)
						{
							PrintGameObject(iterator->GetData(), 0, true, outputStr, printObjectSet);
						}
						outputStr += Format("==End Print Scene Details(%d) path:%s isCurScene:%d\n", scene->GetGameSceneId(), scene->GetPath().c_str(), curScene == scene ? 1 : 0);
						SaveToFile("ReportCurentSceneDetails.log", outputStr.c_str(), outputStr.size());
					}
				}
			}

			if (inputMgr.IsControlHold()
				&& inputMgr.GetKeyUp(SDLK_0)) {
#if USE_MEMORY_PROFILER
				PrintCollectedObjectInstance();
#endif
			}
		}
#endif

	}


	void DebugMgr::CaptureFrame()
	{
		m_Capture = 1;  //需要打开framework下RenderDocAPI.h 的头文件宏   ENABLE_RENDERDOC 为 1
	}

    void DebugMgr::RentServerSetShowLog(bool b)
	{
#ifdef DEDICATED_SERVER		
		if (b)
		{
			m_DebugConfig.m_LogLevel = 5;
			m_DebugConfig.m_LuaDebugModel = DebugConfig::DebugLuaType::kDebugLuaMsgShow;
		}
		else
		{
			m_DebugConfig.m_LogLevel = 2;
			m_DebugConfig.m_LuaDebugModel = DebugConfig::DebugLuaType::kDebugLuaNone;	
		}
		GetLogSystem().SetLogType((LogType)m_DebugConfig.m_LogLevel);
#endif			
	}

	void DebugMgr::RegisterLuaProfiler(lua_State* state)
	{
		int LuaModel = m_DebugConfig.m_LuaDebugModel;
		if ((LuaModel & DebugConfig::kDebugLuaPandaDebug) != DebugConfig::kDebugLuaPandaDebug
			&& (LuaModel & DebugConfig::kDebugLuaProfile) == DebugConfig::kDebugLuaProfile)
		{
#if	USE_OPTICK
			c_lua_profiler_start(state);
#endif
		}
	}

	void DebugMgr::UnregisterLuaProfiler(lua_State* state)
	{
#if USE_OPTICK
		c_lua_profiler_stop(state);
#endif // USE_OPTICK

	}


    void DebugMgr::StartGame()
	{
		//m_DebugConfig.LoadConfig();
		//GetLogSystem().SetLogType((LogType)m_DebugConfig.m_LogLevel); //日志是否开启
		WarningStringMsg("log level:%d", (int)GetLogSystem().GetLogType());
		GetLogSystem().SetLogTimestamp(m_DebugConfig.m_LogTimestamp);
#if PLATFORM_IOS
		WarningStringMsg("[Memory] Total: %dM, Used: %dM",
			systeminfo::GetPhysicalMemoryMB(),
			systeminfo::GetUsedPhysicalMemoryMB());
#elif PLATFORM_WIN
		WarningStringMsg("[Memory] Total: %dM, Used VM: %dM, Free: %dM",
			systeminfo::GetPhysicalMemoryMB(),
			systeminfo::GetUsedVirtualMemoryMB(),
			systeminfo::GetFreeMemoryMB());
#else
		WarningStringMsg("[Memory] Free: %dM, Available: %dM",
			systeminfo::GetFreeMemoryMB(),
			systeminfo::GetAvailableMemoryMB());
#endif

		lua_State* L=ScriptVM::game()->getLuaState();
		m_DebugConfig.m_LuaDebugModel = DebugConfig::kDebugLuaPandaDebug;
		int LuaModel = m_DebugConfig.m_LuaDebugModel;

		if ((LuaModel & DebugConfig::kDebugLuaPandaDebug) == DebugConfig::kDebugLuaPandaDebug)
		{
#ifndef IWORLD_SERVER_BUILD
			pdebug_init(L);
			ScriptVM::game()->callFile("luascript/debugscript_init.lua");
			MiniLua::LuaGlobalCall(L, nullptr, "C_LuaPanda_Debug_Start", m_DebugConfig.m_LuaDebugIp, m_DebugConfig.m_LuaDebugPort);
#endif
		}
		else {
			//lua的debug和lua的Profile是互斥的
			if ((LuaModel & DebugConfig::kDebugLuaProfile) == DebugConfig::kDebugLuaProfile)
			{
				RegisterLuaProfiler(L);
			}
		}

		if ((LuaModel & DebugConfig::kDebugLuaMsgShow) == DebugConfig::kDebugLuaMsgShow)
		{
			m_ShowLuaError = true;
		}

#if		ENABLE_RAINBOW_PROFILER

		GetProfileManager().doCommandFunc = DoParseProfilerCmd;
		GetProfileManager().Connect(m_DebugConfig.m_ProfilerToolIp.c_str(), m_DebugConfig.m_ProfilerPort);

		if (m_DebugConfig.m_ProfilerAutoStart)
		{
			GetProfileManager().Start();
		}

#endif
	}

#if USE_PROFILER_DEBUG_CMD


	void DebugMgr::OnParseProfilerCmd(const char* userCmdStr)
	{
		
	}
#endif

#if USE_CAPTURE_FRAME
	void DebugMgr::OnPreRender()
	{
		if (m_Capture > 0)
		{
			RenderDoc::BeginFrameCapture();
		}
	}

	void DebugMgr::OnPostRender()
	{
		if (m_Capture > 0)
		{
			RenderDoc::EndFrameCapture();
			--m_Capture;
		}
	}
#endif

	void DebugMgr::PrintMemoryInfo()
	{
		/*WarningStringMsg("[Memory]Assets:%u,GameObject:%u,BoneNode:%u,Transform:%u,TransHandleUsed:%u,TransHandleEnd:%u",
			GetAssetManager().GetAssetNum(),GetWorld().GetGameObjectCount(), BoneNode::GetBoneNodeCounter(),
			Object::GetObjectCount(Transform::StaticRTTI().GetTypeId()),
			Transform::GetUsedTransformHandleCount(), Transform::GetValidTransformHandlesEnd()
			);*/
#if PLATFORM_WIN
		//WarningStringMsg("[Memory] Free: %dM, Available: %dM",
		//				 systeminfo::GetFreeMemoryMB(),
		//				 systeminfo::GetAvailableMemoryMB());
#endif

#if PLATFORM_IOS
		WarningStringMsg("[Memory] Total: %dM, Used: %dM",
			systeminfo::GetPhysicalMemoryMB(),
			systeminfo::GetUsedPhysicalMemoryMB());
#elif PLATFORM_WIN
		WarningStringMsg("[Memory] Free: %dM, Used VM: %dM",
			systeminfo::GetFreeMemoryMB(),
			systeminfo::GetUsedVirtualMemoryMB());
#else
		WarningStringMsg("[Memory] Free: %dM, Available: %dM",
						 systeminfo::GetFreeMemoryMB(),
						 systeminfo::GetAvailableMemoryMB());
#endif

#if ENABLE_MEMORY_MANAGER
		{
			core::string str(kMemProfiler);
			// Total memory registered in all allocators
			str = FormatString("[Total Memory] : %0.2fMB (  %0.1f ) Reserved:%0.2fMB , LowLevel: %0.3f MB \n\n",
				(float)(GetMemoryManager().GetTotalAllocatedMemory()) / (1024.f * 1024.f), (float)GetMemoryManager().GetTotalAllocationCount(),
				(float)(GetMemoryManager().GetTotalReservedMemorySize()) / 1024.f / 1024.f, (float)(MemoryManager::GetLowLevelAllocatedSize()) / 1024.f / 1024.f
			);
			WarningString(str.c_str());
		}
#endif
	}

    void DebugMgr::ToggleMetricProfile() {
        m_bMetricProfiling = !m_bMetricProfiling;
        if (m_bMetricProfiling) {
            METRIC_GAUGE_INCREMENT(app_game_state, { "type", "Profiling" });
        }
        else {
            METRIC_GAUGE_RESET(app_game_state, { "type", "Profiling" });
        }
    }

	MINIW::GameStatic<DebugMgr> s_DebugMgr(MINIW::kInitStatic,10000);
	DebugMgr& GetDebugMgr()
	{
		return *s_DebugMgr;
	}

	DebugMgr* GetDebugMgrPtr()
	{
		return s_DebugMgr;
	}
}
