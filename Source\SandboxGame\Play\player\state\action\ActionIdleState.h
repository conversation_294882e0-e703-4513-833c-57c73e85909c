#pragma once
#include "PlayerState.h"
#include "world_types.h"
#include "SandboxGame.h"
#include "ActionIdleStateGunAdvance.h"

//tolua_begin
enum StateLogic_Type
{
	Logic_UseGun = 1,
	Logic_CheckItem,
	Logic_ItemCanUse,
	Logic_BlockCanBreak,
	Logic_BlockCanPlace,
	Logic_RiderSpecialSkill,
	Logic_TriggerToolMode,
	Logic_UseMusicItem,
};
//tolua_end

//tolua_begin
enum HomeLand_OpType
{
	HomeLand_ClickBlock = 1,
	HomeLand_ClickChest = 2,
	HomeLand_ClickActor = 4,
	HomeLand_CheckIsCrops = 8,
	HomeLand_CheckIsAnimal = 16,
	HomeLand_LeftClickBlock = 32,
	HomeLand_CheckIsFramOpen = 64,

};
//tolua_end

struct ItemDef;
struct ToolDef;
struct ItemSkillDef;
class WCoord;
class ClientActor;
class EXPORT_SANDBOXGAME ActionIdleState;
class ActionIdleState : public PlayerState, public ActionIdleStateGunAdvance //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	ActionIdleState(PlayerControl* host);
	virtual ~ActionIdleState();
	virtual void doBeforeEntering() override;
	virtual std::string update(float dtime) override;
	virtual void doBeforeLeaving() override;
public:
	int m_ActionStartMark;
	float m_BuildCoolDown;
	float m_UseBlock;
	bool LogicToState(int logicType);
	
	void InfoTips(int id);
	
	virtual bool IsRightClickDown() override;
	virtual bool IsUseActionTrigger() override;
	virtual bool IsRightClickUp() override;
	//tolua_end
	bool IsKeyEDown();
protected:
	void updateForCurrentTool(); //代码整合一下

	const char* updateOnMobile(float dtime);
	const char* updateOnMobileForLongPress(float dtime);
	const char* updateOnMobileForTab(float dtime);
	const char* updateOnMobileForActionTrigger(float dtime);
	const char* updateOnMobileForAction(float dtime);

	const char* updateOnPC(float dtime);
	const char* updateOnPCForRightClickDown(float dtime, bool& bBreak);
	const char* updateOnPCForLongPress(float dtime, bool& bBreak);
	const char* updateOnPCForRightClickUp(float dtime, bool& bBreak);
	const char* updateOnPCForRightClick(float dtime, bool& bBreak);
	const char* updateOnPCForLeftClickDown(float dtime, bool& bBreak);
	const char* updateOnPCForLeftClick(float dtime, bool& bBreak);
	const char* updateOnPCForEDownClick(float dtime, bool& bBreak);
	const char* doPickActorForRightClickDown(bool& bBreak);
	const char* doPickBlockForRightClickDown(bool& bBreak);
	const char* doPickBlockForLeftClickDown(bool& bBreak);
	const char* doExploitForRightClickDown(bool& bBreak);
	const char* doUseForRightClickDown(const ItemDef*def, bool& bBreak);
	bool doInteractForLeftClickDown();

	const char* itemSkillUse(bool &handle);
	bool canSkipSkillUse(const ItemSkillDef * skilldef);
	bool canActivateBlock(bool& handle);
	bool skillCostEnough(const ItemSkillDef * skilldef);
	bool doSkillUse(int skillId, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir,std::vector<WCoord> &wCoordVec, std::vector<WORLD_ID> &idvec, WCoord &centerPos);

//	const char* footballerState(float dtime);
	const char* gravityGunState(float dtime);
//	const char* basketballerState(float dtime);
	const char* carringState(float dtime);
	
	bool logicToUseMusicItem(); //使用乐器逻辑
	bool logicToUseGun(); //使用枪支逻辑
	bool logicToCheckItem(); //道具解锁逻辑
	bool logicToItemCanUse();//道具可用判断
	bool logicToBlockCanBreak(bool showTip = true); //方块可破坏
	bool logicToBlockCanPlace(bool showTip = true); //方块可放置
	bool logicToRiderSpecialSkill(); //特殊技能释放逻辑
	bool logicToTriggerToolMode();	 //工具模式逻辑

	bool logicToUseTarget(const ItemDef* def, int useType);

	int doHostToPick();

	std::string& getNextState() { return m_nextState; }
	bool CheckEnterComboState(int pickType, ClientActor* target = nullptr, int interactType = 0, bool interactplot = false);

	bool KeyEPressDealOnPC(const char** strResult);

protected:
	void DoMtlClickByActor(bool& bInterrupt);
	void DoMtlClickByActorNotify();
private:
	bool skillAllowDigAndAttack();
	char* ChangeToDig2AttackBlock();//这个函数是方便在运行模式下强制转成攻击方块
	bool IsSupportedBlockType(const std::string& blockType);

private:
	bool m_RMouseUpd;
	std::string m_nextState;
	bool triggerRightClickDownOrUseActionOnce{ false };

	int m_curItemId = -1;
	ItemDef* m_curItemdef;

	int m_curToolId = -1;
	ToolDef* m_curToolDef;
}; //tolua_exports