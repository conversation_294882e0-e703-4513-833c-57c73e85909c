/********************************************************************************
 * ChestMgr
 * author: cloud
 * date: 2025-01-21
 ********************************************************************************/
 
#include "ChestMgr.h"
#include "world.h"
#include "WorldManager.h"
#include "Core/blocks/container_world.h"
#include <sstream>
#include <iomanip>
#include <fstream>
#include "MiniShared/utils/json.hpp"

#define CHESTMGR_DEBUG_LOG 1
#if CHESTMGR_DEBUG_LOG
#define CHESTMGR_LOG(...) \
    do { \
        WarningStringMsg("[ChestMgr] " __VA_ARGS__); \
    } while(0)
#else
#define CHESTMGR_LOG(...)
#endif

// refresh check interval, seconds
#define REFRESH_CHECK_INTERVAL 600  // 10分钟完全刷新一次
#define TIME_CHECK_INTERVAL 10      // 10秒检查一次时间

ChestManager::ChestManager(World* world, int mapSize)
    : m_world(world)
    , m_mapSize(mapSize)
{
    m_containerMgr = dynamic_cast<WorldContainerMgr*>(world->getContainerMgr());
    m_randGen.seed(std::random_device()());
    m_lastRefreshTime = std::chrono::steady_clock::now();
    m_lastTimeCheckTime = std::chrono::steady_clock::now();
    m_lastGameTimeInMinutes = m_world->getTimeInDay();
    m_needFullRefresh = true;
    m_lastGameDay = m_world->GetWorldMgr()->getWorldTimeDay(); // 记录当前游戏天数

    char path[256];
    std::string rootDir = Rainbow::GetFileManager().GetWritePathRoot();
    sprintf(path, "%s/data/w%lld/soc_building_chests.json", rootDir.c_str(), m_world->getOWID());
    m_buildingChestConfigPath = path;
    CHESTMGR_LOG("buildingChestConfigPath: %s", m_buildingChestConfigPath.c_str());
    
    loadBuildingChestsFromJson();
}

ChestManager::~ChestManager()
{

}

void ChestManager::tick()
{
    bool bUGC = m_world->GetWorldMgr()->isUGCMode();
    if (bUGC) {
        return;
    }
    
    // 检查游戏天数是否变化
    int currentGameDay = m_world->GetWorldMgr()->getWorldTimeDay();
    if (currentGameDay > m_lastGameDay) {
        CHESTMGR_LOG("New game day detected (Day %d), refreshing building chests", currentGameDay);
        // 清除并刷新建筑箱子
        clearBuildingChests();
        refreshBuildingChests();
        m_lastGameDay = currentGameDay;
    }
    
    auto currentTime = std::chrono::steady_clock::now();
    
    // 检查是否需要进行完全刷新（包括随机宝箱）
    if (currentTime - m_lastRefreshTime > std::chrono::milliseconds(REFRESH_CHECK_INTERVAL * 1000))
    {
        CHESTMGR_LOG("Full refresh interval reached, refreshing all chests");
        m_needFullRefresh = true;
        refreshChests();
        m_lastRefreshTime = currentTime;
        m_lastTimeCheckTime = currentTime;
        return;
    }
    
    // 更频繁地检查游戏时间变化，只处理定时宝箱
    if (currentTime - m_lastTimeCheckTime > std::chrono::milliseconds(TIME_CHECK_INTERVAL * 1000))
    {
        // 获取当前游戏时间
        int currentGameTimeInMinutes = m_world->getTimeInDay();
        
        // 检查游戏时间是否有显著变化（超过5分钟或跨天）
        int timeDiff = std::abs(currentGameTimeInMinutes - m_lastGameTimeInMinutes);
        if (timeDiff >= 5 || timeDiff >= 1435) // 5分钟或跨天
        {
            CHESTMGR_LOG("Game time changed significantly, checking timed chests");
            checkTimedChests();
            m_lastGameTimeInMinutes = currentGameTimeInMinutes;
        }
        
        m_lastTimeCheckTime = currentTime;
    }
}

void ChestManager::refreshChests() 
{
    // 确保在每次完全刷新时清理现有宝箱
    if (m_needFullRefresh) {
        clearExistingChests();
        m_needFullRefresh = false;
    }
    
    // 获取宝箱配置表
    auto& chestSpawnTable = GetDefManagerProxy()->getChestSpawnTable();
    
    // 获取游戏内时间（单位：分钟，范围0-1440，对应一天24小时）
    int gameTimeInMinutes = m_world->getTimeInDay();
    int gameHour = gameTimeInMinutes / 60;
    int gameMinute = gameTimeInMinutes % 60;
    
    CHESTMGR_LOG("Current game time: %02d:%02d, refreshing chests", gameHour, gameMinute);
    
    // 遍历所有宝箱配置
    for (auto iter = chestSpawnTable.m_Records.begin(); iter != chestSpawnTable.m_Records.end(); ++iter) {
        const ChestSpawnDef& def = iter->second;
        
        CHESTMGR_LOG("Processing chest def id %d, spawnType %d", def.id, def.spawnType);
        
        switch (def.spawnType) {
            case 0: // 固定时间刷新
                refreshFixedTimeChest(def, gameTimeInMinutes);
                break;
                
            case 1: // 随机位置刷新
                // 只有在完全刷新时才处理随机宝箱
                if (m_needFullRefresh) {
                    refreshRandomChest(def);
                }
                break;
                
            default:
                CHESTMGR_LOG("Unknown spawn type %d for chest %d", def.spawnType, def.id);
                break;
        }
    }
    
    // 在刷新常规宝箱后，刷新建筑宝箱
    refreshBuildingChests();
    
    // 更新最后游戏时间
    m_lastGameTimeInMinutes = gameTimeInMinutes;
}

void ChestManager::checkTimedChests()
{
    // 获取宝箱配置表
    auto& chestSpawnTable = GetDefManagerProxy()->getChestSpawnTable();
    
    // 获取游戏内时间
    int gameTimeInMinutes = m_world->getTimeInDay();
    int gameHour = gameTimeInMinutes / 60;
    int gameMinute = gameTimeInMinutes % 60;
    
    CHESTMGR_LOG("Current game time: %02d:%02d, checking timed chests", gameHour, gameMinute);
    
    for (auto iter = chestSpawnTable.m_Records.begin(); iter != chestSpawnTable.m_Records.end(); ++iter) {
        const ChestSpawnDef& def = iter->second;
        if (def.spawnType == 0) {
            // 只处理固定时间刷新的宝箱
            refreshFixedTimeChest(def, gameTimeInMinutes);
        }
    }
}

void ChestManager::refreshFixedTimeChest(const ChestSpawnDef& def, int gameTimeInMinutes)
{
    if (def.fixedSpawnRefreshTime.empty()) {
        CHESTMGR_LOG("Fixed spawn chest %d has no refresh time defined", def.id);
        return;
    }
    
    std::istringstream ss(def.fixedSpawnRefreshTime);
    int hour, minute;
    char delimiter;
    
    if (ss >> hour >> delimiter >> minute && delimiter == ':') {
        // 将时间转换为分钟计数
        int refreshTimeInMinutes = hour * 60 + minute;
        
        // 检查是否到达刷新时间（允许5分钟的误差）
        int timeDiff = std::abs(gameTimeInMinutes - refreshTimeInMinutes);
        if (timeDiff <= 5 || timeDiff >= 1435) { // 考虑跨天的情况
            CHESTMGR_LOG("Time to refresh fixed chest type %d at game time %02d:%02d", def.id, hour, minute);
            
            // 移除同类型的旧宝箱
            removeChestsByType(def.id);
            
            // 生成固定位置的宝箱
            spawnFixedChests(def);
        } else {
            CHESTMGR_LOG("Not time to refresh fixed chest %d, time diff: %d minutes", 
                def.id, timeDiff);
        }
    } else {
        CHESTMGR_LOG("Invalid refresh time format for chest %d: %s", 
            def.id, def.fixedSpawnRefreshTime.c_str());
    }
}

void ChestManager::removeChestsByType(int chestId)
{
    if (!m_containerMgr) return;
    
    CHESTMGR_LOG("Removing chests of type %d", chestId);
    
    // 移除指定类型的宝箱
    int removedCount = 0;
    for (auto it = m_chests.begin(); it != m_chests.end();) {
        if (it->isBuildingChest) {
            it++;
        } else {
			if (it->config && it->config->id == chestId) {
				if (it->isSpawned) {
					// 检查区块是否加载
					int chunkX = it->pos.x >> 4; // 除以16
					int chunkZ = it->pos.z >> 4;

					if (isChunkLoaded(chunkX, chunkZ)) {
						m_world->setBlockAir(it->pos);
						m_containerMgr->destroyContainer(it->pos);
						CHESTMGR_LOG("Removed chest id %d at %d, %d, %d for refresh",
							chestId, it->pos.x, it->pos.y, it->pos.z);
						removedCount++;
					}
					else {
						CHESTMGR_LOG("Chunk not loaded for chest id %d at %d, %d, %d, marking as unspawned",
							chestId, it->pos.x, it->pos.y, it->pos.z);
						// 标记为未生成，这样当区块再次加载时不会生成
						it->isSpawned = false;
					}
				}
				it = m_chests.erase(it);
			}
			else {
				++it;
			}
        }
    }
    
    CHESTMGR_LOG("Removed %d chests of type %d", removedCount, chestId);
}

void ChestManager::refreshRandomChest(const ChestSpawnDef& def)
{
    CHESTMGR_LOG("Refreshing random chest %d, randomSpawnCountMin %d, randomSpawnCountMax %d", 
        def.id, def.randomSpawnCountMin, def.randomSpawnCountMax);
    
    // 检查是否已存在此类型的宝箱
    int existingCount = 0;
    for (const auto& chest : m_chests) {
        if (chest.config && chest.config->id == def.id) {
            existingCount++;
        }
    }
    
    if (existingCount > 0) {
        CHESTMGR_LOG("Found %d existing chests of type %d, removing them first", existingCount, def.id);
        removeChestsByType(def.id);
    }
    
    // 生成随机数量的宝箱
    std::uniform_int_distribution<> distCount(def.randomSpawnCountMin, def.randomSpawnCountMax);
    int count = distCount(m_randGen);
    CHESTMGR_LOG("Generating %d random chests of type %d", count, def.id);
    
    std::uniform_int_distribution<> distX(-m_mapSize/2, m_mapSize/2);
    std::uniform_int_distribution<> distZ(-m_mapSize/2, m_mapSize/2);
    
    int attempts = 0;
    int generatedCount = 0;
    const int MAX_ATTEMPTS = 1000;
    
    while (count > 0 && attempts < MAX_ATTEMPTS) {
        attempts++;
        WCoord pos(distX(m_randGen), 0, distZ(m_randGen));
        
        // 检查与其他宝箱的最小距离
        bool tooClose = false;
        const int MIN_CHEST_DISTANCE = 50;
        for (const auto& existingChest : m_chests) {
            int dx = existingChest.pos.x - pos.x;
            int dz = existingChest.pos.z - pos.z;
            if (dx*dx + dz*dz < MIN_CHEST_DISTANCE*MIN_CHEST_DISTANCE) {
                tooClose = true;
                break;
            }
        }
        
        if (!tooClose) {
            SocChestInfo chest;
            chest.pos = pos;
            chest.config = &def;
            chest.isSpawned = false;
            m_chests.push_back(chest);
            generatedCount++;
            CHESTMGR_LOG("Generate random chest id %d, position at %d, %d, %d, not spawned, m_chests size %d", 
                def.id, pos.x, pos.y, pos.z, m_chests.size());
            count--;
        }
    }
    
    if (attempts >= MAX_ATTEMPTS && count > 0) {
        CHESTMGR_LOG("Reached max attempts (%d) for random chest %d, %d chests not generated", 
            MAX_ATTEMPTS, def.id, count);
    }
    
    CHESTMGR_LOG("Successfully added %d random chests of type %d after %d attempts", 
        generatedCount, def.id, attempts);
}

void ChestManager::spawnFixedChests(const ChestSpawnDef& def)
{    
    CHESTMGR_LOG("Spawning fixed chest %d", def.id);
    
    // 生成固定数量的宝箱
    int count = std::max(1, def.randomSpawnCountMin); // 使用randomSpawnCountMin作为固定宝箱的数量
    int generatedCount = 0;
    
    std::uniform_int_distribution<> distX(-m_mapSize/4, m_mapSize/4); // 在地图中心区域生成
    std::uniform_int_distribution<> distZ(-m_mapSize/4, m_mapSize/4);
    
    // 检查是否已有此类型的宝箱（可能是因为没有被正确清理）
    for (const auto& existingChest : m_chests) {
        if (existingChest.config && existingChest.config->id == def.id) {
            // CHESTMGR_LOG("Warning: Found existing chest of type %d when trying to spawn new ones", def.id);
        }
    }
    
    int maxAttempts = count * 3; // 为了生成count个宝箱，最多尝试3倍次数
    int attempts = 0;
    
    for (int i = 0; i < count && attempts < maxAttempts; ++i, ++attempts) {
        // 根据概率决定是否生成
        std::uniform_real_distribution<float> distProb(0.0f, 1.0f);
        if (distProb(m_randGen) > def.fixedSpawnProbability) {
            continue;
        }
        
        // 生成位置
        WCoord pos(distX(m_randGen), 0, distZ(m_randGen));
        
        // 检查与其他宝箱的最小距离
        bool tooClose = false;
        const int MIN_CHEST_DISTANCE = 50;
        for (const auto& existingChest : m_chests) {
            int dx = existingChest.pos.x - pos.x;
            int dz = existingChest.pos.z - pos.z;
            if (dx*dx + dz*dz < MIN_CHEST_DISTANCE*MIN_CHEST_DISTANCE) {
                tooClose = true;
                break;
            }
        }
        
        if (!tooClose) {
            SocChestInfo chest;
            chest.pos = pos;
            chest.config = &def;
            chest.isSpawned = false;
            m_chests.push_back(chest);
            generatedCount++;
            
            CHESTMGR_LOG("Added fixed chest %d at position %d, %d, %d", 
                def.id, pos.x, pos.y, pos.z);
            
            // 避免重复尝试生成同一个位置
            i++;
        }
    }
    
    CHESTMGR_LOG("Generated %d/%d fixed chests of type %d", generatedCount, count, def.id);
}

void ChestManager::clearExistingChests() 
{
    if (!m_containerMgr) return;
    
    CHESTMGR_LOG("Clearing existing chests");
    
    // 清理之前的宝箱
    for (auto it = m_chests.begin(); it != m_chests.end();) {
        // 不清理建筑宝箱
        if (it->isBuildingChest) {
            ++it;
            continue;
        }
        
        if (it->isSpawned) {
            // 首先检查区块是否加载
            int chunkX = it->pos.x >> 4; // 除以16
            int chunkZ = it->pos.z >> 4;
            
            if (isChunkLoaded(chunkX, chunkZ)) {
                m_world->setBlockAir(it->pos);
                m_containerMgr->destroyContainer(it->pos);
                CHESTMGR_LOG("clear chest at %d, %d, %d", it->pos.x, it->pos.y, it->pos.z);
            } else {
                CHESTMGR_LOG("chunk not loaded for chest at %d, %d, %d", it->pos.x, it->pos.y, it->pos.z);
            }
        }
        
        it = m_chests.erase(it);
    }
}

bool ChestManager::isValidChestPosition(const WCoord& pos, const std::string& terrainType) const 
{
    if (m_world->isBlockLiquid(pos)) return false;
    if (!m_world->isBlockSolid(pos.x, pos.y-1, pos.z)) return false;
    
    return true;
}

bool ChestManager::spawnChest(const SocChestInfo& chestInfo, int chunkX, int chunkZ, int& err) 
{
    err = 0;

    m_containerMgr = dynamic_cast<WorldContainerMgr*>(m_world->getContainerMgr());
    if (!m_containerMgr || !m_world) {
        err = -1;
        return false;
    }
    
    int chestId = 0;
    if (chestInfo.isBuildingChest)
    {
        chestId = generateBuildingChestId(chestInfo.blockId, chunkX, chunkZ);
        CHESTMGR_LOG("spawnChest buildingChest chestId %d, pos %d, %d, %d", chestId, chestInfo.pos.x, chestInfo.pos.y, chestInfo.pos.z);
    } else {
        CHESTMGR_LOG("spawnChest normal chestId %d, pos %d, %d, %d", chestId, chestInfo.pos.x, chestInfo.pos.y, chestInfo.pos.z);
    }

    if (chestId > 0) {
        bool b1 = m_world->setBlockAll(chestInfo.pos, chestId, 0, 3, true);
        if (b1) {
            WorldStorageBox* pchest = m_containerMgr->addDungeonChest(chestInfo.pos, chestId, nullptr);
            if (pchest) {
                return true;
            }
            else { err = -2; }
        }
        else {
            err = -3;
        }
    }
    return false;
}

void ChestManager::handleChestDestruction(const SocChestInfo& chestInfo) 
{
    switch (chestInfo.config->dropType) {
        case 0:
            // TODO: 实现物品散落逻辑
            // 使用 chestInfo.config->scatterRange
            break;
            
        case 1:
            // TODO: 实现掉落小包裹逻辑
            break;
            
        case 2:
            // 直接消失，不需要额外处理
            break;
    }
}

std::string ChestManager::getTerrainTypeAtPosition(const WCoord& pos) const 
{
    // TODO: 实现地形类型判断逻辑
    return "forest";
}

void ChestManager::onChunkLoaded(int chunkX, int chunkZ) {
    // 尝试在新加载的区块生成常规宝箱
    trySpawnChestsInChunk(chunkX, chunkZ);
}

bool ChestManager::isChunkLoaded(int chunkX, int chunkZ) const {
    Chunk* pChunk = m_world->getChunk(chunkX, chunkZ);
    return pChunk != nullptr;
}

void ChestManager::trySpawnChestsInChunk(int chunkX, int chunkZ) {
    // 计算chunk的边界
    const int CHUNK_SIZE = 16;
    int minX = chunkX * CHUNK_SIZE;
    int maxX = minX + CHUNK_SIZE;
    int minZ = chunkZ * CHUNK_SIZE;
    int maxZ = minZ + CHUNK_SIZE;

    // 遍历所有待生成的宝箱
    for (auto& chest : m_chests) {
        if (chest.isSpawned) {
            continue;
        }

        // 检查宝箱是否在当前chunk内
        if (chest.pos.x >= minX && chest.pos.x < maxX && chest.pos.z >= minZ && chest.pos.z < maxZ) {



            if (!chest.isBuildingChest) {
                chest.pos.y = m_world->getTopSolidOrLiquidBlock(chest.pos.x, chest.pos.z);
                if (chest.pos.y <= 0) {
                    CHESTMGR_LOG("chest %d y adjust to %d, badly... not spawn", chest.chestId, chest.pos.y);
                    continue;
                } else {
                    CHESTMGR_LOG("chest %d y adjust to %d", chest.chestId, chest.pos.y);
                }

                std::string terrainType = getTerrainTypeAtPosition(chest.pos);
                if (isValidChestPosition(chest.pos, terrainType)) {
                    int err = 0;
                    bool b = spawnChest(chest, chunkX, chunkZ, err);
                    if (b) {
                        chest.isSpawned = true;
                        CHESTMGR_LOG("spawn chest id %d, position at %d, %d, %d", chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z);
                    }
                    else {
                        CHESTMGR_LOG("spawn chest id %d, position at %d, %d, %d failed, err = %d", chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z, err);
                    }
                }                
            } else {
                int chestId = generateBuildingChestId(chest.blockId, chunkX, chunkZ);;
                chest.chestId = chestId;

                int err = 0;
                bool success = spawnChest(chest, chunkX, chunkZ, err);
                if (success) {
                    chest.isSpawned = true;
                    CHESTMGR_LOG("Try spawn building chest id %d at (%d, %d, %d) success", 
                        chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z);
                } else {
                    CHESTMGR_LOG("Try spawn building chest id %d at (%d, %d, %d) failed, error: %d", 
                        chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z, err);
                }                
            }
        }
    }
}

void ChestManager::onChunkUnloaded(int chunkX, int chunkZ) {
    // 计算chunk的边界
    const int CHUNK_SIZE = 16;
    int minX = chunkX * CHUNK_SIZE;
    int maxX = minX + CHUNK_SIZE;
    int minZ = chunkZ * CHUNK_SIZE;
    int maxZ = minZ + CHUNK_SIZE;

    // 标记该区块内的宝箱为未生成状态
    for (auto& chest : m_chests) {
        if (chest.isSpawned && 
            chest.pos.x >= minX && chest.pos.x < maxX &&
            chest.pos.z >= minZ && chest.pos.z < maxZ) {
            
            chest.isSpawned = false;
            
            if (chest.isBuildingChest) {
                CHESTMGR_LOG("Marking building chest id %d at %d, %d, %d as unspawned due to chunk unload", 
                    chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z);
            } else {
                CHESTMGR_LOG("Marking chest at %d, %d, %d as unspawned due to chunk unload", 
                    chest.pos.x, chest.pos.y, chest.pos.z);
            }
        }
    }
}

// 添加建筑宝箱
bool ChestManager::addBuildingChest(int blockId, const WCoord& pos) {
    
    SocChestInfo chest;
    chest.pos = pos;
    chest.chestId = 0;
    chest.blockId = blockId;
    chest.isBuildingChest = true;
    chest.isSpawned = false;
    m_chests.push_back(chest);
    
    CHESTMGR_LOG("Added building blockId %d at position %d, %d, %d", blockId, pos.x, pos.y, pos.z);
    
    return true;
}

// 从JSON加载配置
bool ChestManager::loadBuildingChestsFromJson() {
    
    // 使用FileManager读取文件
    dynamic_array<UInt8> fileData;
    bool fileExists = Rainbow::GetFileManager().LoadFullPathFileAsBinary(m_buildingChestConfigPath.c_str(), fileData);
    
    if (!fileExists || fileData.empty()) {
        CHESTMGR_LOG("Failed to open building chest config file: %s", m_buildingChestConfigPath.c_str());
        return false;
    }
    
    try {
        // 将读取的二进制数据转换为字符串并解析JSON
        std::string jsonStr(reinterpret_cast<const char*>(fileData.data()), fileData.size());
        nlohmann::json jsonData = nlohmann::json::parse(jsonStr);
        
        // 移除现有的建筑宝箱
        auto it = m_chests.begin();
        while (it != m_chests.end()) {
            if (it->isBuildingChest) {
                it = m_chests.erase(it);
            } else {
                ++it;
            }
        }
        
        if (jsonData.contains("buildingChests") && jsonData["buildingChests"].is_array()) {
            
            for (const auto& item : jsonData["buildingChests"]) {
                SocChestInfo chest;
                chest.chestId = 0;
                chest.blockId = item.contains("blockId") ? item["blockId"].get<int>() : 0;
                chest.pos.x = item["posX"];
                chest.pos.y = item["posY"];
                chest.pos.z = item["posZ"];
                chest.isSpawned = true;
                chest.isBuildingChest = true;

                m_chests.push_back(chest);
                CHESTMGR_LOG("Loaded building chest at position %d, %d, %d, blockId: %d", chest.pos.x, chest.pos.y, chest.pos.z, chest.blockId);
            }
        }
        
        int buildingChestCount = 0;
        for (const auto& chest : m_chests) {
            if (chest.isBuildingChest) {
                buildingChestCount++;
            }
        }
        
        CHESTMGR_LOG("Loaded %d building chests from %s", buildingChestCount, m_buildingChestConfigPath.c_str());
        return true;
    }
    catch (const std::exception& e) {
        CHESTMGR_LOG("Error parsing building chest config file: %s", e.what());
        return false;
    }
}

// 保存配置到JSON
bool ChestManager::saveBuildingChestsToJson(const std::string& filePath) {
    std::string targetPath = filePath.empty() ? m_buildingChestConfigPath : filePath;
    if (targetPath.empty()) {
        CHESTMGR_LOG("No file path specified for saving building chest config");
        return false;
    }
    
    try {
        nlohmann::json jsonData;
        nlohmann::json chestsArray = nlohmann::json::array();
        
        // 只保存建筑宝箱
        for (const auto& chest : m_chests) {
            if (chest.isBuildingChest) {
                nlohmann::json chestJson;
                chestJson["blockId"] = chest.blockId;
                chestJson["posX"] = chest.pos.x;
                chestJson["posY"] = chest.pos.y;
                chestJson["posZ"] = chest.pos.z;
                
                chestsArray.push_back(chestJson);
            }
        }
        
        jsonData["buildingChests"] = chestsArray;
        
        // 将JSON转换为格式化字符串
        std::string jsonStr = jsonData.dump(4);
        
        // 使用FileManager保存文件
        bool success = Rainbow::GetFileManager().SaveFile(targetPath, jsonStr.c_str(), jsonStr.size());
        
        if (!success) {
            CHESTMGR_LOG("Failed to save file: %s", targetPath.c_str());
            return false;
        }
        
        int buildingChestCount = 0;
        for (const auto& chest : m_chests) {
            if (chest.isBuildingChest) {
                buildingChestCount++;
            }
        }
        
        CHESTMGR_LOG("Saved %d building chests to %s", buildingChestCount, targetPath.c_str());
        return true;
    }
    catch (const std::exception& e) {
        CHESTMGR_LOG("Error saving building chest config: %s", e.what());
        return false;
    }
}

const std::vector<SocChestInfo>& ChestManager::getChests() const
{
    return m_chests;
}

// 生成建筑宝箱真实id
int ChestManager::generateBuildingChestId(int blockId, int chunkX, int chunkZ) {
    auto replaceInfo = GetDefManagerProxy()->getBuildReplaceDef(blockId);
    if (replaceInfo && m_world && m_world->GetWorldMgr()) {
        ChunkRandGen randGen;
        randGen.setSeed64(m_world->getChunkSeed(chunkX, chunkZ));
        return EcosysBuildHelp::randReplaceId(replaceInfo, randGen);
    }
    return 0;
}

// 处理指定位置宝箱
void ChestManager::refreshBuildingChests() {
    int buildingChestCount = 0;
    

    for (auto& chest : m_chests) {
        if (!chest.isBuildingChest || chest.isSpawned) {
            continue;
        }
        
        // 检查区块是否加载
        int chunkX = chest.pos.x >> 4;
        int chunkZ = chest.pos.z >> 4;
        
        if (!isChunkLoaded(chunkX, chunkZ)) {
            CHESTMGR_LOG("Refresh building chest id %d at (%d, %d, %d) failed, chunk not loaded, chunk pos = (%d, %d)", 
                chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z, chunkX, chunkZ);
            continue;
        }
        
        // 调整Y坐标
        if (chest.pos.y <= 0) {
            chest.pos.y = m_world->getTopSolidOrLiquidBlock(chest.pos.x, chest.pos.z);
            if (chest.pos.y <= 0) {
                CHESTMGR_LOG("Refresh building chest id %d at (%d, %d, %d) failed, y adjust failed, not spawning", 
                    chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z);
                continue;
            }
            CHESTMGR_LOG("Refresh building chest id %d at (%d, %d, %d) y adjusted to %d", 
                chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z, chest.pos.y);
        }
        
        // 对于建筑宝箱，重新生成ID
        int actualChestId = generateBuildingChestId(chest.blockId, chunkX, chunkZ);
        if (actualChestId == 0) {
            CHESTMGR_LOG("Refresh building chest id %d at (%d, %d, %d) failed, generate building chest id failed, blockId = %d", 
                chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z, chest.blockId);
            continue;
        }
        
        // 准备用于生成的ChestInfo
        chest.chestId = actualChestId;
        
        // 尝试生成宝箱
        int err = 0;
        bool success = spawnChest(chest, chunkX, chunkZ, err);
        if (success) {
            chest.isSpawned = true;
            buildingChestCount++;
            CHESTMGR_LOG("Refresh building chest id %d at (%d, %d, %d) success", 
                chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z);
        } else {
            CHESTMGR_LOG("Refresh building chest id %d at (%d, %d, %d) failed, error: %d", 
                chest.chestId, chest.pos.x, chest.pos.y, chest.pos.z, err);
        }
    }

    CHESTMGR_LOG("Refresh %d building chests", buildingChestCount);
}

// 新增函数，用于清除所有建筑箱子
void ChestManager::clearBuildingChests() 
{
    if (!m_containerMgr) return;
    
    CHESTMGR_LOG("Start clearing building chests for new day");
    
    int removedCount = 0;
    int checkedCount = 0;
    for (auto it = m_chests.begin(); it != m_chests.end();) {
        if (it->isBuildingChest && it->isSpawned) {
            // 检查区块是否加载
            int chunkX = it->pos.x >> 4;
            int chunkZ = it->pos.z >> 4;
            
            if (isChunkLoaded(chunkX, chunkZ)) {
                m_world->setBlockAir(it->pos);
                m_containerMgr->destroyContainer(it->pos, false);
                CHESTMGR_LOG("Clear building chest id %d at (%d, %d, %d) for new day refresh", it->chestId, it->pos.x, it->pos.y, it->pos.z);
                removedCount++;
                it->isSpawned = false;
            } else {
                CHESTMGR_LOG("Clear building chest id %d at (%d, %d, %d) failed, chunk not loaded, chunk pos = (%d, %d)", 
                    it->chestId, it->pos.x, it->pos.y, it->pos.z, chunkX, chunkZ);
                // 保留建筑箱子记录，只是将其标记为未生成，以便之后重新生成
                it->isSpawned = false;
                checkedCount++;
            }
            
            ++it;
        } else {
            ++it;
        }
    }
    
    CHESTMGR_LOG("Cleared %d building chests for new day, checked %d building chests", removedCount, checkedCount);
}
