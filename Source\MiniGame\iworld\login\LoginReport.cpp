#include "LoginReport.h"
#include "MiniReportMgr.h"
#include "Platforms/PlatformInterface.h"
#include "ClientInfo.h"
#include "GlobalFunctions.h"
#include "gamePlatSDKUtils/DeviceIDHelper.h"
#include "GetClientInfo.h"
#include "ClientInterface.h"

namespace MINIW
{

	/* 将platform设置为埋点要求的格式 */
	const char* SetReportPlatform(const char* platform)
	{
		if (strcmp(platform, "android") == 0)
		{
			return "Android";
		}
		else if (strcmp(platform, "ios") == 0)
		{
			return "iOS";
		}
		else
		{
			return "Windows";
		}
	}

	namespace LoginReport
	{
		void ReportUserInfo()
		{
			std::string mode;
			std::string systemVersion;
			std::string hardware_os; //机型，分辨率
			std::string phoneInfo = GetClientInfo()->getMobilePhoneInfo();
			std::string platform = SetReportPlatform(GetClientInfo()->getPlatformStr());
			std::string netType = IntToString(GetClientInfo()->getNetworkState());

			//phoneInfo = "{\"Model\":\"Model\", \"SDTotal\" : \"SDTotal\", \"RamTotal\" : \"RamTotal\", \"WindowWidth\" : \"800\", \"WindowHeigh\" : \"900\", \"SystemVersion\" : \"WindowWidth\"}";
			jsonxx::Object tmpObject;
			tmpObject.parse(phoneInfo);
			if (tmpObject.has<jsonxx::String>("Model")) {
				mode = tmpObject.get<jsonxx::String>("Model");
			}
			if (tmpObject.has<jsonxx::String>("SystemVersion")) {
				systemVersion = tmpObject.get<jsonxx::String>("SystemVersion");
			}
			if (tmpObject.has<jsonxx::Number>("WindowWidth") && tmpObject.has<jsonxx::Number>("WindowHeigh")) {
				int width = (int)tmpObject.get<jsonxx::Number>("WindowWidth");
				int heigth = (int)tmpObject.get<jsonxx::Number>("WindowHeigh");
				char buf[128];
				sprintf(buf, "%d*%d", width, heigth);
				hardware_os = buf;
			}
#ifdef IWORLD_UNIVERSE_BUILD
			Mini::GetHttpReportMgr().setDeviceInfo(GetDeviceId(), mode, hardware_os, netType, platform, systemVersion);
#else
			Mini::GetHttpReportMgr().setDeviceInfo(GetDeviceId(), mode, hardware_os, netType, platform, systemVersion, MINIW::GetAppReportSessionId());
#endif // IWORLD_UNIVERSE_BUILD

			//海外修正这个值为apply_id（不额外定义公参），默认值是1，用于区分星工场等app
			int biz_id = GetClientInfo()->getApplyId();
			std::string apiid = IntToString(GetClientInfo()->GetAppId());
			std::string lang = ""; //IntToString(cInfo.getMobileLang());//这里语言还没初始化好，这里的语言不做上报，提前调getGameData("lang")会导致切语言问题 code_by:huangfubin 2022.8.1

			Mini::GetHttpReportMgr().setBizInfo(biz_id, gFunc_getCountry(), GetClientInfo()->GetClientVersionStr(), apiid, lang, GetClientInfo()->GetEngineVersionStr());
		
			//GameAnalytics::SetDeviceInfo(GetDeviceId(), mode, hardware_os, netType, platform, systemVersion);
		}
	}
}


