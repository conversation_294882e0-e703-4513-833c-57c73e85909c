#include "DrinkWaterStateAction.h"
#include "DefManagerProxy.h"
#include "PlayerAttrib.h"
#include "SoundComponent.h"
#include "WorldManager.h"
#include "SandboxIdDef.h"

DrinkWaterStateAction::DrinkWaterStateAction(ClientPlayer* pPlayer)
: ActionBase(pPlayer)
{
}

DrinkWaterStateAction::~DrinkWaterStateAction()
{

}

bool DrinkWaterStateAction::drinkWater(int status, int itemId, int drinkType)
{
#ifndef IWORLD_SERVER_BUILD
	auto soundComp = mpCtrl->getSoundComponent();
	if (soundComp)
	{
		if (status == PLAYEROP_STATUS_BEGIN)
		{
			soundComp->playSound("misc.drink", 1.0f, 1.0f);
		}
		else if (status == PLAYEROP_STATUS_END)
		{
			soundComp->playSound("misc.drink", 1.0f, 1.0f, 4);
		}
	}
#endif // IWORLD_SERVER_BUILD
	itemId = itemId == 4 ? 3 : itemId;
	const FoodDef* fooddef = GetDefManagerProxy()->getFoodDef(itemId);
	int amount = 100;
	auto pWorld = mpCtrl->getWorld();
	if (pWorld && !pWorld->isRemoteMode())
	{
		if (drinkType == DRINK_BLOCK_WATER && mpCtrl->m_PickResult.isIntersectLiquid)
		{
			if (status == PLAYEROP_STATUS_END)
			{
				mpCtrl->drinkWaterWithBlock(mpCtrl->m_PickResult.block, amount, drinkType, itemId);
				//mpCtrl->ConsumptionWaterBlock(mpCtrl->m_PickResult.firstIntersectBlock, amount);
				//mpCtrl->getPlayerAttrib()->drinkWater(amount, drinkType, itemId);
				return true;
			}
		}
		else if (drinkType == DRINK_WATER_WITH_TOOL)
		{
			if (status == PLAYEROP_STATUS_BEGIN)
			{
				mpCtrl->setOperate(PLAYEROP_DRINKWATER, fooddef->UseTime, itemId);
				mpCtrl->useItemOnTrigger(itemId);
			}
			else if (status == PLAYEROP_STATUS_END)
			{
				auto waterbug = mpCtrl->getEquipGrid(EQUIP_WEAPON);
				int curValue = waterbug->getWaterVolume();
				amount = std::min(curValue, amount);
				if (amount > 0)
				{
					waterbug->addWaterVolume(-amount);
					mpCtrl->getPlayerAttrib()->drinkWater(amount, drinkType, itemId);
				}
			}
			return true;
		}
		else if (drinkType == DRINK_WATER_WITH_DEVICE && mpCtrl->m_PickResult.intersect_block)
		{
			if (status == PLAYEROP_STATUS_END)
			{
				mpCtrl->drinkWaterWithBlock(mpCtrl->m_PickResult.block, amount, drinkType, itemId);
				//mpCtrl->ConsumptionWaterBlock(mpCtrl->m_PickResult.block, amount);
				//mpCtrl->getPlayerAttrib()->drinkWater(amount, drinkType, itemId);
			}
			return true;
		}
		else if (drinkType == TOOL_FILL_WATER && mpCtrl->m_PickResult.isIntersectLiquid)
		{
			if (status == PLAYEROP_STATUS_END)
			{
				auto waterbug = mpCtrl->getEquipGrid(EQUIP_WEAPON);
				amount = waterbug->getMaxWaterVolume() - waterbug->getWaterVolume();
				if (amount > 0)
				{
					mpCtrl->ConsumptionWaterBlock(mpCtrl->m_PickResult.firstIntersectBlock, amount);
					waterbug->addWaterVolume(amount);
				}
			}
			return true;
		}
	}



	//const FoodDef* fooddef = GetDefManagerProxy()->getFoodDef(itemId);
	//if (fooddef)
	//{
	//	auto pWorld = mpCtrl->getWorld();
	//	if (status == PLAYEROP_STATUS_BEGIN)
	//	{
	//		mpCtrl->setOperate(PLAYEROP_DRINKWATER, fooddef->UseTime, itemId);
	//		mpCtrl->useItemOnTrigger(itemId);
	//	}
	//	else
	//	{
	//		assert(status == PLAYEROP_STATUS_END || status == PLAYEROP_STATUS_CANCEL);

	//		if (status == PLAYEROP_STATUS_END)
	//		{
	//			mpCtrl->addOWScore(amount);
	//			if (pWorld && !pWorld->isRemoteMode())
	//			{
	//				mpCtrl->getPlayerAttrib()->drinkWater(itemId);
	//				mpCtrl->addAchievement(3, ACHIEVEMENT_USEITEM, itemId, 1);
	//				mpCtrl->updateTaskSysProcess(TASKSYS_USE_ITEM, itemId);
	//				
	//				// 观察者事件接口
	//				ObserverEvent_ActorItem obevent(mpCtrl->getUin(), itemId, 1);
	//				ObserverEventManager::getSingleton().OnTriggerEvent("Item.expend", &obevent);

	//				mpCtrl->breakHorseInvisible();
	//			}


	//			
	//			auto sound = mpCtrl->getSoundComponent();
	//			if (sound)
	//			{
	//				sound->playSound("misc.drink", 1, 1, 4);
	//			}
	//		}

	//		mpCtrl->onOperateEnded();
	//	}

	//	return true;
	//}
	return false;
} 