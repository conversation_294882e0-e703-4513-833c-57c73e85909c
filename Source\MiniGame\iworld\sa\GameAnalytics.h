#pragma once

#include "ta_analytics_sdk.h"
#include <string>
#include <type_traits>



// 全局变量声明
extern class GameAnalytics* g_pGameAnalytics;

class GameAnalytics {
public:
  // 构造函数和析构函数
  GameAnalytics();
  ~GameAnalytics();

  // 初始化
  static bool Init(const std::string& device_id, int env);

  // 设置游戏环境
  static void SetGameEvn(int apiid, std::string app_ver, int env)
  {
    s_commonProps.apiid = apiid;
    s_commonProps.app_version = app_ver;
    s_commonProps.env = env;
  };
  // 设置会话相关信息
  static void SetSessionInfo(const std::string& session_id, int64_t session_start_time);

  // 设置游戏会话信息
  static void SetGameSessionInfo(const std::string game_session_id,int64_t game_session_start_time)
  {
    s_commonProps.game_session_id = game_session_id;
    s_commonProps.game_session_start_time = game_session_start_time;
  };

  // 设置国家信息
  static void SetCountryInfo(std::string country, std::string province){
    s_commonProps.country = country;
    s_commonProps.province = province;
  };

  // 登录/登出
  static void Login(const std::string& login_id);
  static void Logout();

  // 通用事件（用于自定义事件，或预留给未来扩展）
  static void TrackEvent(const std::string& event_name,
                         const std::string& param1 = "",
                         const std::string& param2 = "",
                         const std::string& param3 = "",
                         int num_param1 = 0,
                         int num_param2 = 0,
                         bool bool_param1 = false);

  // 玩家创建/加载角色
  static void TrackPlayerCreated(const std::string& character_id, const std::string& character_class);
  static void TrackPlayerLoaded(const std::string& character_id);

   // 模板重载，支持直接传递不同类型的值
  template<typename T>
  static void SetUserProfile(const std::string& property_name, const T& value);
 
    static std::string genLogid();
protected:
  // 创建带公共属性的 TDJSONObject
  static thinkingdata::TDJSONObject createCommonProperties();

private:
    static bool m_initialized;
  // 存储公共参数
  struct CommonProperties {
    
    
    // 基础信息
    std::string device_id;
    int env;
    std::string ip_address;
    std::string app_version;
    int apiid;
    std::string os_type;
    std::string country;
    std::string province;
    int64_t channel;
    

    // 会话信息
    std::string session_id;
    int64_t session_start_time;
    // 游戏会话信息
    std::string game_session_id;
    int64_t game_session_start_time;

    // 用户信息
    std::string uin;
    std::string log_id;
  };
  
  static CommonProperties s_commonProps;
};
