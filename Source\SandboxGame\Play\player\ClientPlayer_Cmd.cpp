#include "ClientPlayer.h"
#include "OgreStringUtil.h"
#include "world.h"
#include "container_world.h"
//#ifdef _SCRIPT_SUPPORT_
//#endif
#include "MiniGameApi.h"
#include "ui_framemgreditor.h"
#include "ActorLocoMotion.h"
#include "BlockGodStatue.h"
#include "BlockGeom.h"
#include "OgreUtils.h"
#include "ActorBall.h"
#include "world.h"
#include "ActorHalfGiant.h"
#include "ActorGiant.h"
#include "ActorFlySnakeGod.h"
#include "ClientVacantBoss.h"
#include "OgreUtils.h"
#include "ChunkGen_Normal.h"
#include "BlockMaterialBase.h"
#include "WorldManager.h"
#include "UgcAssetMgr.h"
#include "ActorFireLordBoss.h"

#ifdef ENABLE_PLAYER_CMD_COMMAND
#include "IPlayerCmdCommand.h"
#endif

ActorFireLordBoss* G_BOSSactor = nullptr;

//һˢ½λ
WCoord GetNearMobSpawnPos(ClientPlayer *player)
{
	//int dir =player->getCurPlaceDir();
	WCoord center = CoordDivBlock(player->getPosition());
	WCoord pos;
	for(int i=0; i<200; i++)
	{
		pos = center;
		pos.x += (std::rand()%9)-4;
		pos.z += (std::rand()%9)-4;
		pos.y += (std::rand()%5)-2;

		if(!player->getWorld()->getBlockMaterial(pos)->defBlockMove())
		{
			//½֮Ƿз谭  fixedڷ by :lihuimiao
			Rainbow::Vector3f beginpos = Rainbow::Vector3f(center.x, center.y, center.z);
			Rainbow::Vector3f endPos = Rainbow::Vector3f(pos.x, pos.y, pos.z);
			Rainbow::Vector3f posint = endPos - beginpos;
			posint = MINIW::Normalize(posint);
			posint *= 1.0f;
			int leng = (int)Distance(endPos, beginpos);
			bool  obstacle = false;
			for (int i = 0; i <= leng; i++)
			{
				if (i != 0)
					beginpos += posint;
				WCoord pos2 = WCoord(beginpos);
				if (player->getWorld()->getBlockMaterial(pos2)->defBlockMove())//谭
				{
					obstacle = true;
				}
			}

			if(!obstacle)
				break;
		}
	}

	return BlockBottomCenter(pos);
}

//һˢ½λ
WCoord GetNearMobSpawnPos(World *pworld, const WCoord &center)
{
	WCoord pos;
	for(int i=0; i<200; i++)
	{
		pos = center;
		pos.x += (rand()%9)-4;
		pos.z += (rand()%9)-4;
		pos.y += (rand()%5)-2;

		if(!pworld->getBlockMaterial(pos)->defBlockMove())
		{
			break;
		}
	}

	return BlockBottomCenter(pos);
}

WCoord GetPlaceModelPos(ClientPlayer *player)
{
	Rainbow::Vector3f dir;
	PitchYaw2Direction(dir, player->getLocoMotion()->m_RotateYaw, player->getLocoMotion()->m_RotationPitch);
	dir.y = 0;
	WCoord blockpos = CoordDivBlock(player->getPosition()+dir*(BLOCK_FSIZE*2.0f));
	return blockpos;
}

/*
static bool AddInitItemTo(GameInitItem *items, int itemid, int num, int prob)
{
	for(int i=0; i<MAX_GAMEINIT_ITEMS; i++)
	{
		if(items[i].itemid == 0)
		{
			items[i].itemid = itemid;
			items[i].num = num;
			items[i].prob = prob;
			return true;
		}
	}
	return false;
}*/

bool ClientPlayer::execCmd(const char *cmd)
{

#if GM_PROFILER
	//nothing
#else

#ifndef IWORLD_DEV_BUILD
	if (m_pWorld->isRemoteMode())
	{
		return true;
	}
#endif

#endif

	std::string cmdstr(cmd);
	std::vector<core::string>paramsCore = Rainbow::StringUtil::split(cmdstr, " \t\n");
	std::vector<std::string>params;
	for (auto v : paramsCore)
	{
		params.push_back(v.c_str());
	}

	std::string name = params[0];
	std::transform(name.begin(), name.end(), name.begin(), ::toupper);
#ifdef ENABLE_PLAYER_CMD_COMMAND
	IPlayerCmdCommand* command = NULL;
	if (name == "TP")
		command = ENG_NEW(TeleportCommand)();
	if (name == "FILTER")
		command = ENG_NEW(FilterStringCommand)();
	else if (name == "GT")
		command = ENG_NEW(GameTuneCommand)();
	else if(name == "ITEM")
		command = ENG_NEW(AddBackPackItemCommand)();
	else if (name == "MOD")
		command = ENG_NEW(LoadModCommand)();
	else if(name == "MOB")
		command = ENG_NEW(SpawnActorCommand)();
	else if (name == "MODMOB")
		command = ENG_NEW(SpawnModActorCommand)();
	else if(name == "MOBGEN")
		command = ENG_NEW(MobGenCommand)();
	else if(name == "CLEARMOB")
		command = ENG_NEW(ClearMobsCommand)();
	else if (name == "SETTIMESCALEMOBS")
	{
		command = ENG_NEW(SetTimeScaleMobsCommand)();
	}
	else if(name == "FX")
		command = ENG_NEW(PlayParticleEffectCommand)();
	else if(name == "RAIN")
		command = ENG_NEW(ToggleRainCommand)();
	else if (name == "LIGHTNING")
		command = ENG_NEW(CreateLightningCommand)();
	else if(name == "THUNDER")
		command = ENG_NEW(ToggleThunderCommand)();
	else if(name == "PET")
		command = ENG_NEW(SpawnPetCommand)();
	else if(name == "PET_NEW")
		command = ENG_NEW(SummonPetCommand)();
	else if(name == "DISPORE")
		command = ENG_NEW(RemoveBlockCommand)();
	else if(name == "CLRBLOCK")
		command = ENG_NEW(ClearBlocksCommand)();
	else if(name == "TIME")
		command = ENG_NEW(SetHoursCommand)();
	else if(name == "ADDWTIME")
		command = ENG_NEW(AddWorldTimeCommand)();
	else if(name == "TIMESPEED")
		command = ENG_NEW(SetDayTimeSpeedCommand)();
	else if(name == "VIEWRADIUS")
		command = ENG_NEW(SetViewRangeCommand)();
	else if(name == "HP")
		command = ENG_NEW(AddHpCommmand)();
	else if(name == "RAD")
		command = ENG_NEW(AddRadiationCommand)();
	else if(name == "HPOVER")
		command = ENG_NEW(SetOverflowHPCommand)();
	else if(name == "ST")
		command = ENG_NEW(AddStrengthCommand)();
	else if(name == "STOVER")
		command = ENG_NEW(SetOverflowStrengthCommand)();
	else if(name == "TOGGLESTRENGTH")
		command = ENG_NEW(ToggleUseStrengthCommand)();
	else if(name == "ADDFOOD")
		command = ENG_NEW(AddFoodLevelCommand)();
	else if(name == "ADDFOODSAT")
		command = ENG_NEW(AddFoodSatLevelCommand)();
	else if(name == "KILLME")
		command = ENG_NEW(KillSelfCommand)();
	else if(name == "ACHI")
		command = ENG_NEW(SetAchievementCompletionNumCommand)();
	else if(name == "BUFF")
		command = ENG_NEW(AddBuffCommand)();
	else if(name == "RMBUFF")
		command = ENG_NEW(RemoveBuffCommand)();
	else if(name == "ENCH")
		command = ENG_NEW(AddEnchantmentCommand)();
	else if(name == "RMENCH")
		command = ENG_NEW(RemoveEnchantmentCommand)();
	else if(name == "ENCHS")
		command = ENG_NEW(PostEnchantmentInfoCommand)();
	else if(name == "BTICK")
		command = ENG_NEW(SetBlockTickCommand)();
    else if(name == "STATISTIC")
		command = ENG_NEW(PostWorldStatisticsCommand)();
    else if(name == "GOD")
		command = ENG_NEW(GodCommand)();
	else if(name == "MODEL")
		command = ENG_NEW(PlaceVoxelModelCommand)();
	else if(name == "CAPMODEL")
		command = ENG_NEW(SaveVoxelModelCommand)();
    else if(name == "CAPMODEL2")
		command = ENG_NEW(SaveVoxelModel2Command)();
	else if(name == "PINDEX")
		command = ENG_NEW(ChangePlayerModelCommand)();
	else if(name == "GM")
		command = ENG_NEW(CallLuaGMFunctionCommand)();
	else if(name == "SCR")
		command = ENG_NEW(SetScreenBrightnessCommand)();
	else if(name == "EXP")
		command = ENG_NEW(AddExpCommand)();
	else if(name == "RECORD")
		command = ENG_NEW(CreateWorldRecordCommand)();
	else if(name == "PLAY_PAUSE")
		command = ENG_NEW(PauseRecordingCommand)();
	else if(name == "PLAY_GOTO")
		command = ENG_NEW(GotoRecordTickCommand)();
	else if (name == "PLAY_SPEED")
		command = ENG_NEW(SetRecordSpeedCommand)();
	else if(name == "PORTAL")
		command = ENG_NEW(CreatePortalCommand)();
	else if(name == "PLAYMOTION")
		command = ENG_NEW(PlayMotionCommand)();
	else if(name == "CHEST")
		command = ENG_NEW(AddDungeonChestCommand)();
	else if(name == "BLOCKNUM")
		command = ENG_NEW(PostBlockCountInfoCommand)();
	else if(name == "GAMEMODE")
		command = ENG_NEW(SwitchGameModCommand)();
	else if(name == "SND")
		command = ENG_NEW(SetGlobalSoundCommand)();
	else if(name == "FOG")
		command = ENG_NEW(SwitchFogCommand)();
	else if(name == "HURTNUM")
		command = ENG_NEW(SwitchDisplayHurtDataCommand)();
	else if(name == "TRACKBLOCK")
		command = ENG_NEW(TrackBlockCommand)();
	else if(name == "ADDMINICOIN")
		command = ENG_NEW(AddMiniCoinCommand)();
	else if(name == "FOGR")
		command = ENG_NEW(SetFogVisibleRangeCommand)();
	else if(name == "FOGT")
		command = ENG_NEW(SetFogTimeRangeCommand)();
	else if(name == "TORCH")
		command = ENG_NEW(SetTorchLightColorCommand)();
	else if(name == "FIREWORK")
		command = ENG_NEW(CreateFireworkCommand)();
	else if(name == "RULE")
		command = ENG_NEW(ChangeRuleCommand)();
	else if(name == "GSTAGE")
	{
		//if(params.size() > 1) GetWorldManagerPtr()->setCustomGameStage((CGAME_STAGE_TYPE)Str2Int(params[1]));
	}
	else if(name == "ACCHORSE")
		command = ENG_NEW(SummonHorseCommand)();
	else if(name == "HORSEEGG")
		command = ENG_NEW(HorseEggCommand)();
	else if(name == "UNLOCKTYPE")
		command = ENG_NEW(AddUnlockItemCommand)();
	else if(name == "UNLOCKITEM")
		command = ENG_NEW(UnlockItemCommand)();
	else if(name == "MUTATE")
		command = ENG_NEW(MutateCommand)();
	else if(name == "LOADLUA")
		command = ENG_NEW(LoadLuaCommand)();
	else if(name == "CALL")
		command = ENG_NEW(CallLuaFunctionCommand)();
	else if(name == "LOADCSV")
		command = ENG_NEW(LoadCsvCommand)();
	else if(name == "PHY")//3DԿ
		command = ENG_NEW(SwitchDebugPhysicsCommand)();
	else if(name == "AIRWALL")
		command = ENG_NEW(SwitchAirWallVisibilityCommand)();
	else if(name == "SCN")
		command = ENG_NEW(SetNightColorCommand)();
	else if(name == "SCD")
		command = ENG_NEW(SetDayColorCommand)();
	else if(name == "CAMANIM")
		command = ENG_NEW(OperateCameraCommand)();
	else if (name == "ANIM")
		command = ENG_NEW(PlayAnimCommand)();
	else if(name == "PERMITS")
		command = ENG_NEW(SetPlayerGamePermitsCommand)();
	else if(name == "GAMEVAR")
		command = ENG_NEW(SetGameVarCommand)();
	else if(name == "TESTITEMS")
		command = ENG_NEW(SetTestItemIconIndexCommand)();
	else if(name == "ENMOD")
		command = ENG_NEW(EncryptModCommand)();
	else if(name == "SHADOW")
		command = ENG_NEW(SetShadowCommand)();
	else if(name == "REFLECT")
		command = ENG_NEW(SetReflectCommand)();
	else if(name == "LEAF")
		command = ENG_NEW(SetCubicLeavesCommand)();
	else if(name == "TRAIN")
		command = ENG_NEW(SetTrainDataCommand)();
	else if (name == "BULLET")
		command = ENG_NEW(SetProjectileDataCommand)();
	else if(name == "BIOMEPOS")
		command = ENG_NEW(PostEcosystemInfoCommand)();
	else if (name == "TEST_GASUI")
		command = ENG_NEW(TestGasUICommand)();
	else if (name == "TOBIOME")
		command = ENG_NEW(TransferBiomeCommand);
	else if (name == "CREATECANYON")
		command = ENG_NEW(CreateCanyonCommand);
	else if (name == "CREATENEWCAVE")
		command = ENG_NEW(CreateNewCaveCommand);
	else if(name == "TREE")
		command = ENG_NEW(CreateEcosysUnitCommand)();
	else if(name == "GENICONS")
		command = ENG_NEW(GenIconsCommand)();
	else if(name == "EXPOBJ")
		command = ENG_NEW(ExportChunksCommand)();
	else if(name == "DBFX")
		command = ENG_NEW(SwitchDestroyEffectVisibilityCommand)();
	else if(name == "FACE")
		command = ENG_NEW(SetFaceExpressionCommand)();
	else if (name == "BP")
		command = ENG_NEW(AddBluePrintCommand)();
	else if (name == "BPMINI")
		command = ENG_NEW(SwitchBluePrintOfficialCommand)();
	/*else if (name == "BUILDBP")
	{
		if (params.size() > 3)
		{
			WCoord start(Str2Int(params[1].c_str()), Str2Int(params[2].c_str()), Str2Int(params[3].c_str()));
			GetWorldManagerPtr()->buildBluePrint("test", m_pWorld, start);
		}
	}*/
	else if(name == "OWNMAP")
		command = ENG_NEW(ChangeMapOwnerUinCommand)();
	else if (name == "TESTAVT")
		command = ENG_NEW(TestAvatarCommand)();
	else if (name == "DEVLUA")
		command = ENG_NEW(CallDevScriptCommand)();
	else if (name == "LITECALL")
		command = ENG_NEW(LiteCallCommand)();
	else if (name == "MOVESPEED")
		command = ENG_NEW(SetMoveSpeedCommand)();
	else if (name == "CS")
		command = ENG_NEW(ChangeSkinCommand)();
	else if (name == "SSCMD")
		command = ENG_NEW(CallScriptSupportGMFunctionCommand)();
	else if (name == "SHAPE")
		command = ENG_NEW(TryShiftShapeCommand)();
	else if (name == "BTREE")
		command = ENG_NEW(SwitchRobotTestCommand)();
	else if (name == "STL")
		command = ENG_NEW(ExportSTLCommand)();
	else if (name == "AVATAR_CUS")
		command = ENG_NEW(ExportFullyCustomModelCommand)();
	else if (name == "EXPMOD")
		command = ENG_NEW(ExportModCommand)();
	else if(name == "RUNE")//code by:tanzhenyu
		command = ENG_NEW(RuneCommand)();
	else if (name == "VOLCANO")
		command = ENG_NEW(GotoVolcanoCommand)();
	else if (name == "PROFILE")
		command = ENG_NEW(ProfileCommand)();
	else if (name == "DANCE")
		command = ENG_NEW(DanceCommand)();
	else if (name == "CULLDISTANCE")
		command = ENG_NEW(CullDistanceCommand)();
	else if (name == "PROFILER") //ڿ߹رProfiler
		command = ENG_NEW(ProfilerCommand)();
	else if (name == "DANCE")
		command = ENG_NEW(DanceCommand)();

	if (name == "SSTAT")
		command = ENG_NEW(ServerStatCommand)();
	else if (name == "SCLOSE")
		command = ENG_NEW(ServerCloseCommand)();
	else if (name == "SLOG")
		command = ENG_NEW(SwitchServerLogCommand)();
	else if (name == "SPERF")
		command = ENG_NEW(ServerPerfCommand)();
	else if (name == "DUSTSTORM")
		command = ENG_NEW(DuststormCommand)();
	else if (name == "TEST_BLOCK")
		command = ENG_NEW(BlockTestCommand)();
	else if (name == "SYNC")
		command = ENG_NEW(SyncTestCommand)();
	else if (name == "LOADBP")
		command = ENG_NEW(LoadBluePrintCommand)();
	// Ϸelse if ᳬ128Ŀ error C1061
	if (name == "TEMPEST")
		command = ENG_NEW(TempestCommand)();
	else if (name == "EXPWOBJ")
		command = ENG_NEW(ExportStudioMapChunksCommand)();
	else if (name == "WATERP")
		command = ENG_NEW(WaterPressureCommand)();
	else if (name == "WATERINFO")
		command = ENG_NEW(WaterInfoCommand)();
	else if (name == "GENBUILD")
		command = ENG_NEW(GENModelCommand)();
	else if (name == "SWITCH_SKYBOX")
		command = ENG_NEW(SwitchSkyboxCommand)();
	else if (name == "SANDBOX")
		command = ENG_NEW(SandboxCommand)();
	else if (name == "CLEARCUSTOM")
		command = ENG_NEW(ClearCustomCommand)();
	else if (name == "CLEARBLOCK")
		command = ENG_NEW(ClearSPBlockCommand)();
	else if (name == "CLEARDB")
		command = ENG_NEW(ClearDisplayBoardCommand)();
	else if (name == "SWITCH_QUALITY")
		command = ENG_NEW(SwitchGraphicQuality)();
	else if (name == "SWITCH_EFFECT_QUALITY")
		command = ENG_NEW(SwitchEffectQuality)();
	//#if DEBUG_MODE || PROFILE_MODE
	else if (name == "MAGIC")
		command = ENG_NEW(AdjustPlayerCommand)();
	//#endif
	else if (name == "SNOW")
		command = ENG_NEW(ToggleSnowCommand)();
	else if (name == "BIOME_GENMAP")
		command = ENG_NEW(GenTerrianPicture)();
	else if (name == "WEATHER")
		command = ENG_NEW(ChangeWeatherCommand)();
	else if (name == "BOT")
		command = ENG_NEW(BotConversationsCommand)();
	else if (name == "TEMP")
		command = ENG_NEW(TemperatureCommand)();
	else if (name == "BLIZZARD")
		command = ENG_NEW(BlizzardCommand)();
	else if (name == "TASK")
		command = ENG_NEW(TaskCommand)();
	else if (name == "AURORA")
		command = ENG_NEW(OpenAuroraCommand)();
	else if (name == "SNOWCOVER")
		command = ENG_NEW(SnowCoverCommand)();
	else if (name == "RELOAD")
		command = ENG_NEW(ReloadShaderCommand)();
#ifndef IWORLD_SERVER_BUILD
	else if (name == "FPSGRAPHIC")
		command = ENG_NEW(FPSGraphicCommand)();
	else if (name == "GODRAY")
		command = ENG_NEW(GodrayCommand)();
	else if (name == "MODELLEAF")
		command = ENG_NEW(ModelLeafCommand)();
#endif
	else if (name == "ENABLEBOXDEBUG")
		command = ENG_NEW(DebugActorBox)();
	else if (name == "DANGERNIGHT")
		command = ENG_NEW(ToggleDangerNightCommand)();
	else if (name == "CLEARSOUNDCACHE")
		command = ENG_NEW(ClearSoundCacheCommand)();
	if (name == "CDWI")
		command = ENG_NEW(CreateDoubleWeaponIconCommand);
	else if(name == "CBIC")//Count Blocks in this chunk
		command = ENG_NEW(CountBlockInThisChunk);
	else if(name == "ENABLETLBOX")
		command = ENG_NEW(DebugTLBox);
	else if (name == "CABI")
		command = ENG_NEW(CreateAllBlockIconCommand);
	else if (name == "COMBOLOG")
		command = ENG_NEW(ComboAttackLogCommand);
	else if (name == "GETACTOR")
		command = ENG_NEW(GetNearActorCommand);
	else if (name == "ADS")
		command = ENG_NEW(GunAdsCommand);
	else if (name == "ADST")
		command = ENG_NEW(GunAdstCommand);
	else if (name == "LW")
		command = ENG_NEW(GunLookWeaponCommand);
	else if (name == "AFA")
		command = ENG_NEW(GunAutoFireAdsCommand);
	else if (name == "ADDENTRY")
		command = ENG_NEW(EntryTrigCommand);
	else if (name == "QUERYPREFAB")
		command = ENG_NEW(QueryPrefabLoadStateCommand);
	else if (name == "GLTF")
		command = ENG_NEW(GltfCommand);
	else if (name == "VEH")
		command = ENG_NEW(VehCommand);
	else if (name == "AIRDROP")
		command = ENG_NEW(AirDropCommand);
	else if (name == "FLYSPEED")
		command = ENG_NEW(SetFlySpeedCommand)();
	else if (name == "VISION")
		command = ENG_NEW(SetVisionSizeCommand)();
	else if (name == "COR")
		command = ENG_NEW(CorpseCommand)();
	else if (name == "CAM")
		command = ENG_NEW(CameraParamCommand)();
	else if (name == "WALL")
		command = ENG_NEW(WallCommand)();
	else if (name == "CLOSEBUILDPROTECT")
		command = ENG_NEW(CloseBuildProtectCommand)(false);
	else if (name == "OPENBUILDPROTECT")
		command = ENG_NEW(CloseBuildProtectCommand)(true);
	else if (name == "CREATEAIRDROPCHEST")
		command = ENG_NEW(CharlesDebugCommand)();
	else if (name == "MOBCOLLIDE")
		command = ENG_NEW(MonsterCollideCommand)();
	else if (name == "HANDITEMDEF")
		command = ENG_NEW(HandItemDefCommand)();
	else if (name == "ITEMSCALE")
		command = ENG_NEW(ItemScaleCommand)();
	if (!command)
	{
		if (name == "CREATEBOSS")
		{
			ActorFireLordBoss* actor = SANDBOX_NEW(ActorFireLordBoss);
			actor->init(3518);
			actor->setSpawnPoint(CoordDivBlock(this->getPosition()));

			WCoord pos = actor->getPosition();
			m_pWorld->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
			static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->spawnBoss(actor);
			//actor->startAppeal();

			G_BOSSactor = actor;
		}

		if (name == "REMOVEBOSS")
		{
			if (G_BOSSactor)
			{
				static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->removeActorByChunk(G_BOSSactor);
				SANDBOX_DELETE(G_BOSSactor);
			}

			//MINIW::ScriptVM::game()->callFile("luascript/mobs.lua");
		}

		return false;
	}
	command->exec(*this, params);
	ENG_DELETE(command);
#endif
	return true;
}

void ClientPlayer::exportSTLForMiniCode(WCoord& start, WCoord& end, const std::string &stlName)
{
#ifdef ENABLE_PLAYER_CMD_COMMAND
	ExportSTLCommand* command = ENG_NEW(ExportSTLCommand)();
	command->exportSTL(*this, start, end, stlName);
	ENG_DELETE(command);
#endif
}