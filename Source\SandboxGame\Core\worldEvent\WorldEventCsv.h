#ifndef __WorldEventCsv_H__
#define __WorldEventCsv_H__
#include "CsvLoader/AbsCsvReader.h"
#include "LazySingleton.h"
#include "DefDataTable.h"
#include "defdata.h"
#include "json/jsonxx.h"

#include <string> // 用于解析位置字符串
#include <map> // 用于事件类型映射表
#include <vector>

using namespace MINIW;

struct DropPos{
    int x;
    int y;
    int z;
};

// 世界事件数据定义
struct WorldEventDef
{
    int ID;                 // 事件ID
    std::string EventType;  // 事件类型
    std::string Name;       // 事件名称
    float Speed;            //空投速度
    int FirstTime;           // 持续时间（秒）
    int Interval;           // 冷却时间（秒）
    int Priority;           // 优先级
    std::string Params;     // 参数
    std::string Description; // 描述
    int dropType;            // 空投位置类型1：大范围刷新 2：固定遗迹 3：固定位置
    DropPos drop_pos1;       // 空投位置
    DropPos drop_pos2;       // 空投位置
    int PlanceModelId;      //空投飞机模型
    int TreasureId;         //宝箱id
    int Harm;               //空投伞伤害
    std::string boardcast_msg; //广播消息
    int MaxHeight;          //最大高度
    int MaxSizeX;           //最大范围X
    int MaxSizeZ;           //最大范围Z
    bool IsDebug;            //是否调试
    bool Enable;            // 是否启用
    int TimeOut;             // 空投超时时间(s)
    std::string ChestModel;        //空投箱子模型 actor  block
    
};

// 世界事件CSV解析类
class WorldEventCsv : public AbsCsvReader {//tolua_exports
protected:
    const char* getName() override;
public:
    WorldEventCsv();
    ~WorldEventCsv();

    void onParse(MINIW::CSVParser& parser) override;
    void onClear() override;
    const char* getClassName() override;

    //tolua_begin
    WorldEventDef* getById(int id);
    WorldEventDef* getByIndex(int i);
    int getNum();

    // 获取所有事件定义
    std::vector<WorldEventDef*> getAllEvents();

    // 将事件定义转换为JSON
    std::string getEventToJson(int id);
    //tolua_end

private:
    /**
     * @brief 解析位置字符串，格式为[type,{x1,z1},{x2,z2}]
     * @param posStr 位置字符串
     * @param outDropType 输出的投放类型
     * @param outDropPos1 输出的第一个位置坐标
     * @param outDropPos2 输出的第二个位置坐标
     * @return 解析是否成功
     */
    bool ParsePositionString(const std::string& posStr, int& outDropType,
        DropPos& outDropPos1, DropPos& outDropPos2);

    // 事件数据表
    DefDataTable<WorldEventDef> m_WorldEventTable;

    DECLARE_LAZY_SINGLETON(WorldEventCsv)
};//tolua_exports

#endif//__WorldEventCsv_H__

