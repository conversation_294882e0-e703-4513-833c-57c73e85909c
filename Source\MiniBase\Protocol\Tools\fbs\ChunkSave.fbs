
include "FlatSaveCommon.fbs";

namespace FBSave;

struct TempSource {
	tick : int;
	index : uint;
	pos : short;
	val : float;
}

table SectionSave {
  index : ubyte;
  blocks : [ushort]; //原blockid与data
  blocklights : [ubyte];
  dirtylights : [uint];
  dirtylights1 : [uint];
  ndirtylight0 : short;
  ndirtylight1 : short;
  blockex : [ubyte];//blockid与data扩展
  tempsources : [TempSource];// 区域温度源
}

table UgcComponent {
  name : string;
  detail : [byte];
}

table ActorObj {
  basedata : ActorCommon;
  
  modelpath : string;
  modeltype : int;
  extradata : int;
  scale : Vec3;
  
  interacted : bool;
  scriptcomponent : string; //组件
  isparent : bool;
  parentid : ulong;
  roll : float;
  
  children : [byte]; //子对象
  
  modelcomponent : [byte]; //模型组件 
  
  ugccomponents : [byte]; //ugc的纯c++组件
}

table ActorMob {
  basedata : ActorCommon;

  defid : int;
  hp : float;
  owner : uint;
  color : int;
  buffs : [ActorBuff];
  mods : [AttribMod];
  equips : [ItemIndexGrid];
  growage : int;
  scale:float= 1.0;
  dieticks : int;
  bags : [ItemIndexGrid];
  food : float;
  bodyscale : float = 1.0;
  bonfirepos : Coord3; 
  animwaketicks : short;
  displayname : string;
  climbing : bool = false;
  serverid : string;
  eaten : bool = false;
  needeat : int = 0;
  temperature : float;
  growdvalue : int;
  growtime : int;
  componentData : string;
  teamid:int=0;
  bb : string;
  
  scriptcomponent : string; //组件
  ugccomponents : [byte]; //ugc的纯c++组件
  children : [byte]; //子对象
  extendattr : [float]; //生物其他属性
}

table ActorEnderman {
  mobdata : ActorMob;
  carryblock : ushort;
  carryblockdata : ushort;
  carryblockex : ubyte = 0;
}

table ActorSandworm {
  mobdata : ActorMob;
}

table ActorHorse {
  mobdata : ActorMob;

  ridden : ulong;
  equips : [ItemIndexGrid];
  skills : [int];
  maxhp : short;
  landspeed : short;
  flyspeed : short;
  swimspeed : short;
  jumpheight : short;

  binduin : int;
  armoropen : ubyte;
  otherriddens : [ulong];
  energy : float;
  tired : byte;
  shieldCoolingTicks : short;
  extendskills : [int];
  extendriddens : [long];
  rakeopen : ubyte;
  rakeToolLiving : byte;
}

table ActorAquaticMob {
  mobdata : ActorMob;

  droughtTolerance: int;
  moveTarget: Coord3;
}

table ActorFlyMob {
  mobdata : ActorMob;
  moveTarget: Coord3;
  luadata : string;
}

table ActorTrixenieMob
{
  mobdata : ActorMob;
  droughtTolerance : int;
  moveTarget : Coord3;
  moveType : byte;
}

table ActorSandMan
{
  mobdata : ActorMob;
  tickstate : int;
  oldhp : int;
  absorbsandnum : int;
  iscoreexposed : bool;
}


table ActorArrow {
  basedata : ActorCommon;

  shooter : ulong;
  itemid : int;
  blockpos : Coord3;
  inblock_id : ushort;
  inblock_data : ushort;
  knockback : float;
  atkpoint : float;
  buffatk : float;
  startpos : Coord3;
  pickup : byte;
  inground : byte;
  airticks : int;
  groundticks : int;
  infire : byte;
  inblock_idex : ubyte = 0;
}


table ActorNewArrow {
  basedata : ActorCommon;

  shooter : ulong;
  itemid : int;
  blockpos : Coord3;
  inblock_id : ushort;
  inblock_data : ushort;
  knockback : float;
  atkpoint : float;
  buffatk : float;
  startpos : Coord3;
  pickup : byte;
  inground : byte;
  airticks : int;
  groundticks : int;
  infire : byte;
  inblock_idex : ubyte = 0;
  firelv : byte = 0;
}

table ActorEgg {
  basedata : ActorCommon;
  shooter : ulong;
  itemid : int;
}

table ActorGlowSticks {
  basedata : ActorCommon;
  shooter : ulong;
  itemid : int;
}

table ActorHook {
  basedata : ActorCommon;
  shooter : ulong;
  itemid : int;
  startpos : Coord3;
  state : int;
  blockpos : Coord3;
  handitem : int;
}

table ActorLaser {
  basedata : ActorCommon;
  shooter : ulong;
  itemid : int;
  tickinair : int;
  startpos : Coord3;
}

table ActorAttract {
  basedata : ActorCommon;
  shooter : ulong;
  itemid : int;
  startpos : Coord3;
  state : int;
  blockID : int;
  blockData : int;
  handitem : int;
  attractActorObj : ulong;
}

table ActorImpulse {
  basedata : ActorCommon;
  shooter : ulong;
  itemid : int;
  startpos : Coord3;
  attackCount : int;
  knockback : float;
  atkpoint : float;
  buffatk : float;
  infire : byte;
  firelv : byte;
}

table ActorBlockLaser {
  basedata : ActorCommon;  
  itemid : int;
  startpos : Coord3;
  initBlockPos : Coord3;
  blockDir : byte;
  initActorObj : ulong;
  nextActorObj : ulong;
  power : int;
  blockShoot : byte;
  shooter : ulong;
  lastSetPowerpos : Coord3;
}

table ActorHalfGiant {
  basedata : ActorCommon;

  defid : int;
  hp : float;
  missionflags : int;
}

table ActorGhost {
  basedata : ActorCommon;

  defid : int;
  hp : float;
  missionflags : int;
}

table ActorGiant {
  basedata : ActorCommon;

  defid : int;
  hp : float;
  missionflags : int;
  maxhp : float;
}

table ActorSnowBall {
  basedata : ActorCommon;
  shooter : ulong;
  itemid : int;
}

table ActorProjectile {
  basedata : ActorCommon;
  shooter : ulong;
  itemid : int;
  durable : int;
  enchants : [int];
  color : uint;
  blockid : int = 0;
  blockpos : Coord3;
  rotatequat : Quat4;
  runes : [ItemGridRune];
  userData : string;
  passActorNum : int;
  puncture : float;
  maxdurable : int;
}

table ActorThornBall {
  basedata : ActorCommon;
  shooter : ulong;
  itemid : int;
  rotatequat : Quat4;
  impactActor : bool = false;
  isDrop : bool = false;
}

table ActorCoconut {
  basedata : ActorCommon;
  shooter : ulong;
  itemid : int;
  rotatequat : Quat4;
}

table ActorItem {
  basedata : ActorCommon;

  itemid : int;
  num : int;
  durable : int;
  delayticks : int;
  enchants : [int];
  userdatastr : string;
  serverid	: string;
  runes : [ItemGridRune];
  creatorid : int;
  toughness : int;
  datacomponents : [byte];
  maxdurable : int;
}

table ActorExpOrb {
  basedata : ActorCommon;

  exp : int;
  delayticks : int;
}

table ActorTNT {
  basedata : ActorCommon;

  fuse : int;
  placeby : ulong;
  bombtype : byte;
  itemid : int;
  exploderadius : ushort;
}

table ActorFallSand {
  basedata : ActorCommon;

  blockid : ushort;
  blockdata : ushort;
  dropitem : byte;
  blockidex : ubyte = 0;
}

table ActorFlyBlock {
  basedata : ActorCommon;
  blockid : ushort;
  blockdata : ushort;
  maxdist : int;
  dropitem : byte;
  startblock : Coord3;
  blockidex : ubyte = 0;
}

table ActorMinecartEmpty {
  basedata : ActorCommon;

  itemid : int;
  ridden : ulong;
  railknot : Coord3;
  rail_t : float;
  inreverse : byte;
  rotatequat : Quat4;
  headcar : ulong;
  tailcar : ulong;
  irailout : byte;
}

table ActorIceBall_Normal {
  basedata : ActorCommon;

  acceleration : Vec3;
  tickinair : int;
  shooter : ulong;
}

table ActorLargeFireball {
  basedata : ActorCommon;

  acceleration : Vec3;
  tickinair : int;
  shooter : ulong;
}

table ActorLavaBall {
  basedata : ActorCommon;

  acceleration : Vec3;
  tickinair : int;
  shooter : ulong;
}

table ActorChaosBall {
  basedata : ActorCommon;

  acceleration : Vec3;
  tickinair : int;
  shooter : ulong;
}

table ActorPoisonBall {
  basedata : ActorCommon;

  acceleration : Vec3;
  tickinair : int;
  shooter : ulong;
}

table ActorDragon {
  basedata : ActorCommon;

  defid : int;
  hp : float;
  missionflags : int;
  maxhp : float;
}

table ActorFrostWyrm {
  basedata : ActorMob;

  maxhp : float;
  bossphase : int;
  istemp : bool;
  spawnpos : Coord3;
  chunkx : int;
  chunky : int;
}

table ActorEnderEye {
  basedata : ActorCommon;

  startpos : Coord3;
  targetpos : Coord3;
  findtarget : byte;
}

table ActorThrowable {
  basedata : ActorCommon;

  shooter : ulong;
  itemid : int;
}

table ActorTrader {
  basedata : ActorCommon;

  defid : int;
  spawntime : int;
  items : [ItemIndexGrid];
  equips : [ItemIndexGrid];
  bags : [ItemIndexGrid];
  food : float;
}

table ActorFirework {
  basedata : ActorCommon;

  firetype : int;
  firedata : int;
  fuse : int;
}

table ActorBoat {
  basedata : ActorCommon;

  itemid : int;
  ridden : ulong;
  otherriddens : [ulong];
  hp : float;
}

table ActorRiverLantern {
  basedata : ActorCommon;
  itemid : int;
}


table ActorNpc {
  mobdata : ActorMob;
}

struct MechaUnitBlock {
  x : byte;
  y : byte;
  z : byte;
  block : ushort;
}

table MechaSlideData {
   curmovelen : short;
   speed : short;
   move_from : ushort;
   move_target : ushort;
}

table MechaRotateData {
   speed : float;
}

table ActorMechaUnit {
  basedata : ActorCommon;

  attachblocks : [MechaUnitBlock];
  containers : [ChunkContainer];
  originblock : Coord3;
  driverblock : Coord3;
  driverblockid : ushort;
  movetype : byte;
  slidedata : MechaSlideData;
  rotatedata : MechaRotateData;
  beblocked : byte;
  loopType : byte = 0;
  angle : int = 0;
  end : byte = 0;
  attachblockex : [ubyte];
  driverblockidex : ubyte = 0;
}

table ActorThrowBlock {
  basedata : ActorCommon;
  attachblocks : MechaUnitBlock;
  containers : ChunkContainer;
  attachblockex : ubyte = 0;
}

union SectionActorUnion {
  ActorMob,
  ActorArrow,
  ActorItem,
  ActorExpOrb,
  ActorTNT,
  ActorFallSand,
  ActorFlyBlock,
  ActorMinecartEmpty,
  ActorLargeFireball,
  ActorEnderman,
  ActorDragon,
  ActorEnderEye,
  ActorThrowable,
  ActorTrader,
  ActorFirework,
  ActorBoat,
  ActorHorse,
  ActorLavaBall,
  ActorChaosBall,
  ActorNpc,
  ActorNewArrow,
  ActorSnowBall,
  ActorEgg,
  ActorProjectile,
  ActorAquaticMob,
  ActorRiverLantern,
  ActorMechaUnit,
  ActorFallGravel,
  ActorFlyMob,
  ActorThrowBlock,
  ActorBall,
  ActorRocket,
  ActorHook,
  ActorHalfGiant,
  ActorGhost,
  ActorGiant,
  ActorAttract,
  ActorImpulse,
  ActorBlockLaser,
  ActorLaser,
  ActorVehicleAssemble,
  ActorChassis,
  ActorBasketBall,
  ActorShapeShiftHorse,
  ActorDragonMount,
  ActorVillager,
  ActorSouvenir,
  ActorMoonMount,
  ActorSkinNpc,
  ActorPoisonBall,
  ActorSavagePriest,
  ActorFlySnakeGod,
  ActorDouDuHorse,
  ActorPray,
  ActorHomeNpc,
  ActorHomelandLives,
  ActorPet,
  ActorVacantBoss,
  ActorPumpkinHorse,
  ActorTrixenieMob,
  ActorVideoAQY,
  ActorDesertBusInessman,
  ActorDesertBusInessmanGuard,  
  ActorDesertVillger,
  ActorSandworm,  
  ActorSandMan,
  ActorPackHorse,
  ActorFishingVillager,
  ActorFisherMan,
  ActorSeaSpiritGuarding,
  ActorPatrolMob,
  ActorIslandBusInessman,
  ActorDriftBottle,
  ActorThornBall,
  ActorPirateShip,	
  ActorPirateChest,	
  ActorCoconut,
  ActorCrab,
  ActorHippocampus,
  ActorHippocampusHorse,
  ActorGlowSticks,
  ActorPipeline,
  ActorFrostWyrm,
  ActorPushSnowBall,
  ActorSnowMan,
  ActorPortal,
  ActorYak,
  ActorIcicle,
  ActorSnowHare,
  ActorIceBall_Normal,
  ActorObj,
  ActorVortex,
  ActorTravelingTrader,
  ActorStorageBoxHorse,
  ActorPlayerCorpse,
  ActorCubeChest,
}

table SectionActor {
  actor : SectionActorUnion;
  productname : string;
}

table ContainerCommon {
  wid : ulong;
  blockpos : Coord3;
  owner : uint;
}

table ContainerValue {
  basedata : ContainerCommon;

  subtype : int;
  value : int;
}

table ContainerString {
  basedata : ContainerCommon;

  subtype : int;
  value : string;
}

table ContainerFurnace {
  basedata : ContainerCommon;

  items : [ItemGrid];
  curheat : int;
  maxheat : int;
  meltticks : int;
  melting : byte;
  curheat2 : int;
  maxheat2 : int;
  curheat3 : int;
  maxheat3 : int;
  temperature : int;
  provideHeat : float;
  provideHeat2 : float;
  provideHeat3 : float;
  meltticks2 : int;
  meltticks3 : int;
}

table ContainerFurnaceArray {
  basedata : ContainerCommon;

  items : [ItemGrid];
  melting : byte;
  temperature : int;

  curheatArray : [int];
  maxheatArray : [int];
  provideHeatArray : [float];
  meltticksArray : [int];
  offlineTime : uint;
}

table ContainerStorage {
  basedata : ContainerCommon;

  items : [ItemGrid];
  indices : [ubyte];
  key : int;
}

table ContainerStoragePassword {
  basedata : ContainerCommon;

  items : [ItemGrid];
  indices : [ubyte];
  key : int;
  password : int = -1;
}

table ContainerDecomposition {
  basedata : ContainerCommon;

  items : [ItemGrid];
  indices : [ubyte];
  key : int;

  status : ubyte;
  lasttime : ulong;
}

table ContainerFunnel {
  basedata : ContainerCommon;

  items : [ItemGrid];
  indices : [ubyte];
  maxgrids : int;
  transferticks : short;
  upsidedown : byte;
}

table ContainerEmitter {
  basedata : ContainerCommon;

  items : [ItemGrid];
  indices : [ubyte];
  maxgrids : int;
}

table ContainerPiston {
  basedata : ContainerCommon;
  blockid : ushort;
  blockdata : ushort;
  dir : byte;
  extending : byte;
  renderhead : byte;
  curprogress : float;
  blockidex : ubyte = 0;
}

table ContainerMobSpawner {
  basedata : ContainerCommon;

  spawndelay : short;
  mindelay : short;
  maxdelay : short;
  spawncount : byte;
  forcespawn : byte;
  mobresid : int;
  maxnearby : int;
  duration : int;
  maxduration : int;
}

table ContainerSigns {
  basedata : ContainerCommon;

  text : string;
  textinit : byte;
  fonttype : byte;
  changtime : ulong = 0;
  oldtext : string;
  textwidth : byte;
}

table ContainerEffect {
  basedata : ContainerCommon;

  effectid : ubyte;
  dir : ubyte;
  offset : Coord3;
  isshow : ubyte = 1;
}

table ContainerHorseEgg {
  basedata : ContainerCommon;

  broodticks : int;
}

table ContainerOstrichEgg {
  basedata : ContainerCommon;

  broodticks : int;
}

table ContainerDragonCup {
  basedata : ContainerCommon;

  active : ubyte;
}

table ContainerItemExpo {
  basedata : ContainerCommon;

  item : ItemGrid;
}

table ContainerWeaponRack {
  basedata : ContainerCommon;

  item : ItemGrid;
}

table ContainerMecha {
  basedata : ContainerCommon;
  bindunit : ulong;
  looptype : byte = 0;
  angle : int = 0;
  realangle : int = 0;
  actionmode : int = 0;
  suspensionlevel : int = 0;
  axistype : byte = 0;
  fuel : int = 0;
}

table ContainerSensor {
  basedata : ContainerCommon;

  neighbors : [ushort];
  items : [ItemGrid];
  indices : [ubyte];
  sensordistance : float;
  neighborsEx : [ubyte];
}

table ContainerCollider {
  basedata : ContainerCommon;

  items : [ItemGrid];
  indices : [ubyte];
  trigger : byte = 0;
}

table ContainerRailKnot {
  basedata : ContainerCommon;

  outknot : Coord3;
  inknots : [Coord3];
  outknot1 : Coord3;
}

table ContainerAlienTotem {
  basedata : ContainerCommon;

  oxygenticks : int;
  beaconticks : int;
}

table ContainerRadioUnit {
  basedata : ContainerCommon;

  subtype : ubyte;
  powervalue : ubyte;
  conntargets : [Coord3];
  statevalue : ubyte = 0;
}

table ContainerStoneCore {
  basedata : ContainerCommon;
  actorObj : ulong;
  dir : byte;
  power : int;
}

table RadioConnInfo {
  channel : int;   
  conntargets : [Coord3];
}

table ContainerInterpreterUnit {
  basedata : ContainerCommon;
  
  subtype : ubyte;
  items : [ItemGrid];
  indices : [ubyte]; 
  curuserdatastr : string; 
  curgridline : int; 
  curgridspeed : int;
  curgridvaluestr : string; 
  curpowervalue : [int]; 
  curconnect : [RadioConnInfo];
  curinterid : int;
  statevalue : ubyte = 0;
}

table ContainerBookEditorTable {
  basedata : ContainerCommon;

  items : [ItemGrid];
  indices : [ubyte];
}

table ContainerSensorValue {
	basedata : ContainerCommon;
	sensorvalue :  float ;
	isreverse :  ubyte;
}

table ContainerBlockLogo
{
	basedata : ContainerCommon;
	logoid : ushort;
	frame : ubyte;
}

table ContainerBlockKeyPedestal
{
	basedata : ContainerCommon;
	state : ubyte;
}

table ContainerRevivalStatue
{
  basedata : ContainerCommon;
  revivePoint : [Coord3];
  mapid : [int];
}

table ContainerVenom
{
  basedata : ContainerCommon;
  corrodeDirTicks : [int];  
}

table ContainerElectricBaseSave
{
	basedata : ContainerCommon;
	dir : byte;
	power : int;
	openDir : uint;
	tickEmit : [int];
	tickDir : int;
	emitLength : uint;
	tickWithDraw : [int];
	tickWithDrawDir : int;
	curLaserType : [uint];
	tickPowerCount : int;
	tickPower : int;
	tickResetPowerCount : int;
}

table ContainerElectricSplitterSave
{
	basedata : ContainerElectricBaseSave;
}

table ContainerElectricDelaySave
{
	basedata : ContainerElectricBaseSave;
	level : uint;
}

table ContainerElectricResisterSave
{
	basedata : ContainerElectricBaseSave;
	level : uint;
}

table ContainerElectricCounterSave
{
	basedata : ContainerElectricBaseSave;
	updir : int;
	leftdir : int;
	ip1state : bool;
	ip2state : bool;
	tickcount : int;
}

table ContainerPiplineSave
{
	basedata : ContainerElectricBaseSave;
	connectDir : ushort;
	transferDir : ushort;
	tick: short;
	dir: ushort;
	endDir: short;
	pipleActor: ulong;
	itemid : int;
}

table ContainerDetectionPipeSave
{
	basedata : ContainerPiplineSave;
	items : [ItemGrid];
}

union ContainerUnion {
  ContainerValue,
  ContainerString,
  ContainerFurnace,
  ContainerStorage,
  ContainerPiston,
  ContainerMobSpawner,
  ContainerSigns,
  ContainerFunnel,
  ContainerEmitter,
  ContainerEffect,
  ContainerHorseEgg,
  ContainerDragonCup,
  ContainerItemExpo,
  ContainerMecha,
  ContainerSensor,
  ContainerRailKnot,
  ContainerAlienTotem,
  ContainerFurnaceOxy,
  ContainerRadioUnit,
  ContainerStoragePassword,
  ContainerInterpreterUnit,
  ContainerStoneCore,
  ContainerGiantTotem,
  ContainerRegionReplicator,
  ContainerBlueprint,
  ContainerBuildBluePrint,
  ContainerMeasureDistance,
  ContainerCollider,
  ContainerCustomModel,
  ContainerSelectMobSpawner,
  ContainerTransfer,
  ContainerBookEditorTable,
  ContainerBookCabinet,
  ContainerActorModel,
  ContainerWheel,
  ContainerWorkshop,
  ContainerFullyCustomModel,
  ContainerActioner,
  ContainerVehicleMecha,
  ContainerVehicleActioner,
  ContainerDriverSeat,
  ContainerSensorValue,
  ContainerArmPrismatic,
  ContainerBonFire,
  ContainerTombStone,
  ContainerBed,
  ContainerVillageTotem,
  ContainerVillagerFlag,
  ContainerImportModel,
  ContainerAltarTbl,
  ContainerOneQuarterBlock,
  ContainerHomelandPlant,
  ContainerBlockLogo,
  ContainerBlockKeyPedestal,
  ContainerPot,
  ContainerFarmland,
  ContainerStarStationTransferConsole,
  ContainerStarStationTransferCabin,
  ContainerFeedTrough,
  ContainerRevivalStatue,
  ContainerPeristele,
  ContainerCoagulation,
  ContainerKeyEffect,
  ContainerMiniClub,
  ContainerByLua,
  ContainerCanvas,
  ContainerPopulusLeaf,
  ContainerOstrichEgg,
  ContainerVenom,
  ContainerFishFrame,
  ContainerPoseidonStatue,
  ContainerShells,
  ContainerModel,
  ContainerGiantScallops,
  ContainerCoconut,
  ContainerDriverSeatModel,
  ContainerGlowStick,
  ContainerSeaPlant,
  ContainerColorPalette,
  ContainerSmallTorch,
  ContainerSolidSand,
  ContainerStake,
  ContainerPlutonicRock,
  ContainerKeyDoor,
  ContainerCollectingPipe,
  ContainerElectricBaseSave,
  ContainerElectricSplitterSave,
  ContainerElectricDelaySave,
  ContainerElectricResisterSave,
  ContainerElectricCounterSave,
  ContainerPiplineSave,
  ContainerDetectionPipeSave,
  ContainerLightMushroom,
  ContainerShellBed,

  ContainerPedalSingleFB,

  ContainerStretchForzen,
  ContainerThick,
  ContainerIceCrystalShroom,
  ContainerVillageTotemIce,
  ContainerVillageFlagBuilding,
  ContainerVillageFlagIce,
  ContainerDoorplate,
  ContainerSchoolFence,
  ContainerElectricRay,
  ContainerAnimalEgg,
  ContainerArrowSigns,
  ContainerWeaponRack,
  ContainerActorBodyModelBase,
  ContainerMobBodyModel,
  ContainerPlayerBodyModel,
  ContainerManualEmitter,
  ContainerClipTrap,
  ContainerFusionCage,
  ContainerSimpleModelBase,
  ContainerDragonFlower,
  ContainerBellFlower,
  ContainerStove,
  ContainerDeathJar,
  ContainerNewPot,
  ContainerDummy,
  ContainerStarStationCargo,
  ContainerMod,
  ContainerComputer,
  ContainerMonsterSummoner,
  ContainerTalkingStatue,
  ContainerPolaroidFrame,
  ContainerModTransfer,
  ContainerFurnaceArray,
  ContainerTerritory,
  ContainerSocDoor,
  ContainerAutoSocDoor,
  ContainerDecomposition,
  ContainerErosion,
  ContainerWaterStorage,
}

table ChunkContainer {
  container : ContainerUnion;
}

struct BlockSchedule
{
  pos : Coord3;
  blockid : int;
  tick : int;
  priority : int;
}

table ChunkContainers{
  containers : [ChunkContainer];
}

table ChunkActors{
  version : int;
  actors : [SectionActor];
}

table ChunkSave {
  biomes : [ubyte];
  sections : [SectionSave];
  actors : [SectionActor];
  containers : [ChunkContainer];
  scheduleblocks : [BlockSchedule];
  dungeonpos : Coord3;
  populated : ubyte = 1;
  lighting : ubyte;
  suspendDirtylights : [uint];
  dirtyLightsGenerated : ubyte = 0;
  searchblocks : [ushort];
  version : int;
  airlandbiomes : [ubyte];
  actors_ex : [ubyte];
  searchblocksEx : [ubyte];//id扩展
}

root_type ChunkSave;

table ActorFallGravel {
  basedata : ActorCommon;
  blockid : ushort;
  blockdata : ushort;
  dropitem : byte;
  blockidex : ubyte = 0;
}

table ActorBall {
  basedata : ActorCommon;
  model_id : int;
}

table ActorBasketBall {
  basedata : ActorCommon;
}

table ActorChassis {
  basedata : ActorCommon;
  blockid : ushort = 0;
  rotatequat : Quat4;
  blockidex : ubyte = 0;
}

table ActorRocket {
  basedata : ActorCommon;

  itemid : int;
  ridden : ulong;
  state : short;
  fuel : short;
  stateticks : int;
  hp : float;
}

table ActorVideoAQY {
  basedata : ActorCommon;
  
  x : int;
  y : int;
  w : int;
  h : int;
  rotationx : int;
  rotationy : int;
  rotationz : int;
  param : string;
}

table ContainerFurnaceOxy {
  basedata : ContainerCommon;

  items : [ItemGrid];
  curheat : int;
  maxheat : int;
  meltticks : int;
  melting : byte;
  oxyvalue : int;
  hascover : int;
}

table ContainerGiantTotem {
  basedata : ContainerCommon;
}

table ContainerRegionReplicator {
  basedata : ContainerCommon;
  knot : Coord3;
  state : int;
}

table ContainerBlueprint {
  basedata : ContainerCommon;
  items : [ItemGrid];
  sheetname : string;
  datafilename : string;
  datanickname : string;
  dim : Coord3;
  copyblockpos : Coord3;
  knotpos : Coord3;
}

table ContainerBuildBluePrint {
  basedata : ContainerCommon;

  items : [ItemGrid];
  filename : string;
  dim : Coord3;
  buildindex : int;
  startpos : Coord3;
  rotatetype : int;
  buildrate : int;
}

table ContainerMeasureDistance {
  basedata : ContainerCommon;
}

table ContainerCustomModel {
  basedata : ContainerCommon;
  dim : Coord3;
  startpos : Coord3;
  items : [ItemGrid];
  dirtype : byte;
}

table ContainerSelectMobSpawner {
  basedata : ContainerCommon;

  spawndelay : short;
  mindelay : short;
  maxdelay : short;
  spawncount : byte;
  forcespawn : byte;
  mobresid : int;
  maxnearby : int;
  duration : int;
  maxduration : int;
  generationswitch : byte;
  numberdetection : byte;
  isspawndelay : byte;
  spawnwide : int;
  Spawnhigh : int;
  firstspawn : byte;
}

table ContainerTransfer {
	basedata : ContainerCommon;
	transfercorepos : Coord3;
	mapid			: short;
	transferstep    : short;
}

table ContainerBookCabinet {
	basedata : ContainerCommon;
	items : [ItemGrid];
}

table ContainerBonFire {
	basedata : ContainerCommon;
	items : [ItemGrid];
	indices : [ubyte];
	heatvalue : int;
	roaststate : int;
	roastcountdown : int;
	heatcountdown : int;
	attractcountdown : int;
	attractstate : int;
	bindactors : [ulong];
	roaststate_array : [int];
	roastcountdown_array : [int];
	roastseq : [int];
}

table ContainerTombStone{
	basedata : ContainerCommon;
	text : string;
	content : string;
	isFirstSpawn : byte;
	items : [ItemGrid];
	indices : [ubyte];
	profession : short;
}

table VABlockData {
  block : ushort;
  relativepos : ushort;
  blockhp : ushort;
  blockCost : ushort;
  blockGroup : ushort;
  blockHeat : ushort;
  blockGroupJoint : ushort;
  oriGroupid : ushort;
  blockex : ubyte = 0;
}

table ActorVehicleAssemble {
  basedata : ActorCommon;
  blocks : [VABlockData];
  modelname : string;    // Discard
  ridden : ulong;
  otherriddens : [ulong];
  direction : byte;
  lifecurrent : float;
  filename : string;
  name : string;
  desc : string;
  fuelcurrent : int;
  itemid : int;
  coredechp : int = 0;
  rotatequat : Quat4;
  containers : [ChunkContainer];
  vehiclelines : [VehicleBlockLine];
  mainchassispos : Coord3;
  blockGroupNum : uint;
}

table VABlockEnterData {
  block : ushort;
  info : uint;
  blockex : ubyte = 0;
}


table ActorVehicleAssembleEnterData {
  basedata : ActorCommon;
  blocks : [VABlockEnterData];
  modelname : string;    // Discard
  ridden : ulong;
  otherriddens : [ulong];
  direction : byte;
  lifecurrent : float;
  filename : string;
  name : string;
  desc : string;
  fuelcurrent : int;
  itemid : int;
  rotatequat : Quat4;
  containers : [ChunkContainer];
  vehiclelines : [VehicleBlockLine];
  jointgroup : [uint];
  blockGroupNum : uint;
  chassisupdatepos : [Coord3];
  chassisupdateposquat : [Quat4];
  wheelupdatepos : [Coord3];
  wheelupdateposquat : [Quat4];
}

table ActorVehicleAssembleEnter {
  data : [ubyte];
  unziplen : int;
}

table ContainerActorModel {
	basedata : ContainerCommon;
	bonemodels : [BoneModelData];
	type : short;
	skindisplay : bool = true;
	direction : byte;
	downloadcmnum : short = 0;
}

table ContainerWheel {
  basedata : ContainerCommon;
  direction : byte;
  anglerange : short;
  startpos : Coord3;
}

table ContainerWorkshop {
  basedata : ContainerCommon;
  dim : Coord3;
  startpos : Coord3;
  dirtype : byte;
  item : ItemGrid;
  buildindex : int;
  start : bool = false;
  itemid : int;
  name : string;
  desc : string;
  showCentroid : bool = false;
  vehiclelines : [VehicleBlockLine];
}

table ContainerFullyCustomModel {
	basedata : ContainerCommon;
	fullycustommodelkey : string;
	isfcmblock : bool = false;
	direction : byte;
}

table ContainerArmPrismatic {
  basedata : ContainerCommon;
  bindunit : ulong;
  actionmode : int = 0;
  extendsize : int = 0;
  state	: int = 0;
  isaction : bool = false;
  power : int = 0;
}

table ActionerPart {
	pos : Coord3;
	id : int;
	actiondata : [int];
	curline : int;
	workstate : int;
	tickcount : int;
	actionmode : int;
	actionval : int;
	remainval : int;
}

table ContainerActioner{
	basedata : ContainerCommon;
	partsdata : [ActionerPart];
	banlist : [int];
	perruntime : float;
	isloop : bool;
	isworking : bool;
}

table ContainerVehicleMecha{
	basedata : ContainerCommon;
	bindunit : ulong;
	looptype : byte;
	angle : int;
	realangle : int;
	actionmode : int;
	actionersignal : byte;
	slidetick : int;
	actionerangle : int;
	actionerpos : Coord3;
	originval : int;
	initialval : int;
	slidepos  : float;
	jointstate : bool;
	suspensionlevel : int = 0;
	axistype : byte = 0;
	fuel : int = 0;
}

table ContainerVehicleActioner{
	basedata : ContainerCommon;
	partsdata : [ActionerPart];
	banlist : [int];
	perruntime : float;
	isloop : bool;
	isworking : bool;
}

table ContainerPot {
	basedata : ContainerCommon;
	items : [ItemGrid];
	CookingTime : [int];
	IsMaking :bool;
	CookingProgress : float;
	CraftID : int;
	bonfireend :bool;
}

table SeatPartData {
	position : Coord3;
	ischarge : bool;
}

table SeatKeyData {
	position : Coord3;
	index	 : int;
	keyid	 : int;
}

table ContainerDriverSeat{
	basedata : ContainerCommon;
	linesdata : [SeatPartData];
	keysdata  : [SeatKeyData];
}

table ActorShapeShiftHorse {
  mobdata : ActorMob;

  ridden : ulong;
  equips : [ItemIndexGrid];
  skills : [int];
  maxhp : short;
  landspeed : short;
  flyspeed : short;
  swimspeed : short;
  jumpheight : short;

  binduin : int;
  armoropen : ubyte;
  otherriddens : [ulong];
  energy : float;
  tired : byte;
  skillcd : [float];
}

table ActorDragonMount {
  mobdata : ActorMob;

  ridden : ulong;
  equips : [ItemIndexGrid];
  skills : [int];
  maxhp : short;
  landspeed : short;
  flyspeed : short;
  swimspeed : short;
  jumpheight : short;

  binduin : int;
  armoropen : ubyte;
  otherriddens : [ulong];
  energy : float;
  tired : byte;
  skillcd : [float];
}

table ActorVillager {
  mobdata : ActorMob;
  carried : ulong = 0;
  hairid : int = 0;
  haircolor : uint = 0;
  faceid : int = 0;
  fatigue : byte = 0;
  extremisval : short = 0;
  bedbindpos : Coord3;
  hunger : short = 0;
  hungerstatus : byte = 0;
  workbindpos : Coord3;
  profession : byte = 0;
  traitor : bool = false;
  disposition : byte = 0;
  favor : short = 0;
  demand : byte = 0;
  speedofbreakingblock : float = 1.0;
  wakeuptime : short;
  sleeptime : short;
  stayuptime : short;
  spawntime : int;
  isstayingup : bool = false;
  foodbytamed : short = 0;
  randomnameid : int = -1;
  icegirl : byte = 0;
}

table ActorSouvenir{
	mobdata : ActorMob;
}

table ContainerBed
{
  basedata : ContainerCommon;
  bindactor : ulong;
  actorstatus : byte = 0;
}

table ContainerThick
{
  basedata : ContainerCommon;
  monsterid :int;
  hp:int;
  hareColor :int;
}
table ContainerVillageTotem {
  basedata : ContainerCommon;
}

table ContainerVillagerFlag {
  basedata : ContainerCommon;
}

table ContainerImportModel {
	basedata : ContainerCommon;
	modelkey : string;
	direction : byte;
}

table ActorMoonMount {
  mobdata : ActorMob;

  ridden : ulong;
  equips : [ItemIndexGrid];
  skills : [int];
  maxhp : short;
  landspeed : short;
  flyspeed : short;
  swimspeed : short;
  jumpheight : short;

  binduin : int;
  armoropen : ubyte;
  otherriddens : [ulong];
  energy : float;
  tired : byte;
  skillcd : [float];
}

table ContainerAltarTbl {
  basedata : ContainerCommon;
}

table OneQuarterBlockData {
  posindex : byte;
}

table ContainerOneQuarterBlock {
  basedata : ContainerCommon;
  placetype : byte;
  onequarterinfo : [OneQuarterBlockData];
}

table ActorSavagePriest {
  mobdata : ActorFlyMob;
  isclone : bool;
}

table ActorFlySnakeGod {
  basedata : ActorCommon;
  defid : int;
  hp : float;
  missionflags : int;
  spawnpos : Coord3;
}

table ActorVacantBoss {
  basedata : ActorCommon;
  defid : int;
  hp : float;
  missionflags : int;
  spawnpos : Coord3;
  buffs : [ActorBuff];
  moveTarget: Coord3;
}

table ActorSkinNpc {
	mainPlayer :ulong;
	modelID : int;
	basedata : ActorCommon;
}

table ActorDouDuHorse {
  mobdata : ActorMob;

  ridden : ulong;
  equips : [ItemIndexGrid];
  skills : [int];
  maxhp : short;
  landspeed : short;
  flyspeed : short;
  swimspeed : short;
  jumpheight : short;

  binduin : int;
  armoropen : ubyte;
  otherriddens : [ulong];
  energy : float;
  tired : byte;
  skillcd : [float];
}

table ActorPumpkinHorse {
  mobdata : ActorMob;
  
  ridden : ulong;
  equips : [ItemIndexGrid];
  skills : [int];
  maxhp : short;
  landspeed : short;
  flyspeed : short;
  swimspeed : short;
  jumpheight : short;

  binduin : int;
  armoropen : ubyte;
  otherriddens : [ulong];
  energy : float;
  tired : byte;
  skillcd : [float];
}

table ActorPackHorse {
  mobdata : ActorMob;
  
  ridden : ulong;
  equips : [ItemIndexGrid];
  skills : [int];
  maxhp : short;
  landspeed : short;
  flyspeed : short;
  swimspeed : short;
  jumpheight : short;

  binduin : int;
  armoropen : ubyte;
  otherriddens : [ulong];
  energy : float;
  tired : byte;
  packs : [int];
  followType : int;
  explorePos : Coord3;
}

table ActorStorageBoxHorse {
  mobdata : ActorHorse;
  containers : ContainerStorage;
}

table ActorPray {
	mobdata : ActorMob;
	state : int;
}

table ActorHomeNpc {
  mobdata : ActorMob;
  npctype : int;
}

table ActorPlayerCorpse{
  basedata : ActorCommon;
  containers : ContainerStorage;
}

table ContainerHomelandPlant {
	basedata : ContainerCommon;
    farmLandID : int;				
	seedServerID: string;	
    seedID : int;					
    sowTime : int;				
    growthTime : int;				
    stage : int;					
    manureNumFlag : int;			
    variationFlag : int;			
    manureTime : int;				
    isSelfFarmlandFlag : int;		
}

table ActorHomelandLives {
	mobdata : ActorMob;

	flag	: int;
}

table ActorPet {
	mobdata : ActorMob;
	petid : int;
	stage : int;
	quality : int;
    petname : string;
}

table ContainerFarmland {
	basedata : ContainerCommon;
    wetworldtime : int;				
    wetby : int;						
}



table ContainerStarStationTransferConsole {
	basedata : ContainerCommon;                       
	pos : Coord3;
	mapid : short;
	starstationid : int;
}

table ContainerStarStationTransferCabin {
	basedata : ContainerCommon;
	pos : Coord3;
	mapid : short;
	level: short;
	bindplayeruin: int;
	connectedstarstationid : int;
}



table ContainerFeedTrough {
	basedata : ContainerCommon;
	
	troughlevel: int;
	troughitemid : int; 
	leftfeedcount : int;
  triangle : int = 0;
  model : int = -1;
}

table ContainerPeristele {
	basedata : ContainerCommon;
    color : int;					
}

table ContainerCoagulation {
  basedata : ContainerCommon;
  isContactLava : bool = false;
  tick : int = 0;
  place1:int = 0;
  place2:int = 0;
  place3:int = 0;
  corePos : Coord3;
  blowtick:int=0;
  hotmaxheight:int=0;
}

table ContainerKeyEffect {
	basedata : ContainerCommon;
}

table ContainerByLua {
  basedata : ContainerCommon;
  subTypeLua: int;
  jsonxxData: [ubyte];
}

table ContainerMiniClub {
  basedata : ContainerCommon;
  text : string;
}

table ActorDesertBusInessman {
  basedata : ActorTrader;
  hp : float;
  special : bool = false;
  explorePos : Coord3;
  exploreType : int = 0;
  bedBindPos : Coord3;
  guardIds : [long];
  workPos : Coord3;
  hateUin : int = -1;
  warnTick : float = 0;
  warnType : byte = 0;
  yaw : float = 0;
  avoid : bool = false;
  needAvoid : bool = false;
  dealCamel : long = -1;
  pickingCamel : long = -1;
  followId : long;
 }

table ActorDesertBusInessmanGuard {
  basedata : ActorCommon;
  followId : long;
  hp : float;
  workPos : Coord3;
  nightWorkPos : Coord3;
  gatherPos : Coord3;
  yaw : float = 0;
  bedPos : Coord3;
  hateUin : int = -1;
  behaviourType : int = 0;
  avoid : bool = false;
  needAvoid : bool = false;
}

table ActorDesertVillger {
  basedata : ActorMob;
  bedBindPos : Coord3;
  villgePos : Coord3;


}

table ContainerCanvas
{
  basedata : ContainerCommon;
  bindactor : ulong;
  actorstatus : byte = 0;
  stage : byte = 0;
}

table ContainerPopulusLeaf {
  basedata : ContainerCommon;
  changetime : int;
}

table ContainerGlowStick {
  basedata : ContainerCommon;
  duration : int;
  minusCD : int;
}

table ContainerSeaPlant {
  basedata : ContainerCommon;
  time : uint;
  data : int;
}
table ContainerFishFrame {
  basedata : ContainerCommon;
  fishState : [int];
  dryTime : [int];
}

table ActorFishingVillager {
	basedata : ActorMob;
	bedBindPos : Coord3;
	villgePos : Coord3;
	wharfPos : Coord3;
	hateUin : int = -1;
}

table ActorFisherMan {
	basedata : ActorTrader;
	bedBindPos : Coord3;
	villgePos : Coord3;
	wharfPos : Coord3;
	hateUin : int = -1;
}

table ActorSeaSpiritGuarding {
	basedata : ActorMob;
	isberserk : bool = false;
}

table ActorCrab {
	basedata : ActorMob;
}

table ActorHippocampus {
	basedata : ActorMob;
}
table ActorHippocampusHorse {
	basedata : ActorMob;
}



table ContainerPoseidonStatue {
  basedata : ContainerCommon;
}

table ActorPatrolMob {
  basedata  : ActorMob;
  patrolPos : Coord3;
}

table ActorIslandBusInessman{
  basedata : ActorTrader;
  salePos  : Coord3;
}

table ContainerShells
{
	basedata : ContainerCommon;
}

table ContainerModel
{
  basedata : ContainerCommon;
  modelID : string;
  dir : int;
  blocktype : int;
  corePosition : Coord3;
}


table ContainerDriverSeatModel
{
  basedata : ContainerDriverSeat;
  modelID : string;
  dir : int;
  blocktype : int;
  corePosition : Coord3;
}

table ActorDriftBottle{
	basedata  : ActorMob;
	userData : string;
}
table ContainerCoconut{
    basedata  : ContainerCommon;
    leafMeshIndex : int;
}
table ContainerGiantScallops{
    basedata  : ContainerCommon;
    tick : int;
    status : int;
    bCrap : bool;
}
table ActorPirateShip {
	basedata : ActorMob;
	BombShipSpawn : bool = false;
}

table ActorPirateChest {
  basedata : ActorCommon;
  shooter : ulong;
  itemid : int;
}


table ContainerColorPalette {
	basedata : ContainerCommon;
    color : uint;
	count : int;
	colorid : int;
}

table ContainerSmallTorch {
  basedata : ContainerCommon;
  firetime : int;
}

table ContainerSolidSand {
	basedata : ContainerCommon;
    coverid : int;
	coverdata : int;
}

table ContainerStake
{
  basedata : ContainerCommon;
  dirConnect : [int];   
}

table ContainerPlutonicRock {
  basedata : ContainerCommon;
}

table ContainerKeyDoor {
  basedata : ContainerCommon;
  hasKey : bool = false;
}

table SocLock {
  type : int;
  status : int;
  mainid : int;
  lockid : int;
  lockpassword : int;
  main_lockpassword : int;
  mainids : [int];
}

table ContainerSocDoor {
  basedata : ContainerErosion;
  lock : SocLock;
}

table ContainerAutoSocDoor {
  basedata : ContainerErosion;
  lock : SocLock;
  isopen : bool = false;
}

table ContainerCollectingPipe {
  basedata : ContainerCommon;

  items : [ItemGrid];
  indices : [ubyte];
  maxgrids : int;
  transferticks : short;
  upsidedown : byte;
}

table ContainerWaterStorage {
  basedata : ContainerErosion;
  watervolume : int;
}

table ActorPipeline{
	basedata : ActorItem;
}

table PedalPosInfo{
	pos : Coord3;
	type : int = 0;
	active : bool = false;
}

table ContainerPedalSingleFB {
  basedata : ContainerCommon;
  info : PedalPosInfo;
  inputs : [PedalPosInfo];
  outputs : [PedalPosInfo];
}

table ContainerLightMushroom {
  basedata : ContainerCommon;

  m_CheckTicks : int;
  m_index : int;
  parentPos : Coord3;
  putOnBlockPos : Coord3;
  m_isFect : bool;
}

table ActorIcicle {
  basedata : ActorCommon;
  shooter : ulong;
  itemid : int;
  rotatequat : Quat4;
}

table ActorSnowHare {
  mobdata : ActorMob;
  furColor : int;

}

table ActorYak {
  mobdata : ActorMob;
}

table ContainerShellBed
{
  basedata : ContainerCommon;
  bindactor : ulong;
  actorstatus : byte = 0;
}

table ActorPortal {
  basedata : ActorCommon;
  isBegin: bool;
}

table ContainerStretchForzenEulerSave
{
	x : float;
	y : float;
	z : float;
}

table StretchForzenPlayerEquipInfo
{
	slot : byte;
	itemId : int;
}

table StretchForzenPlayerAvatarInfo
{
	part : int;
	modelid : int;
}

table ContainerStretchForzen{
	basedata : ContainerCommon;
	playerUin : long;
	mobId : int;
	animId : int;
	animTick : int;
	version: int;
	bodyRotate : ContainerStretchForzenEulerSave;
	headRotate : ContainerStretchForzenEulerSave;
	headBoneId : int;
	headScale : float;
	playerIndex : int;
	mutateMob : int;
	customSkins : string;
	mobFb : [ubyte];
	skinShow : [string];
	equipInfo : [StretchForzenPlayerEquipInfo];
	avatarInfo : [StretchForzenPlayerAvatarInfo];
	mobType : int;
}

table ActorPushSnowBall {
  basedata : ActorCommon;
  level : int;
  boundSize : int;
}

table ActorSnowMan {
  mobdata : ActorMob;  
  active : bool;
  leaf : int;
  partdata : int;
  slenderbranch : int;
}

table ContainerIceCrystalShroom
{
	basedata : ContainerCommon;

}

table ContainerVillageTotemIce {
  basedata : ContainerCommon;
  flagPoint : [Coord3];
  villagers : [string];
  haveAward : bool;
}

table ContainerVillageFlagBuilding {
  basedata : ContainerCommon;
  fileName : string;
  rotateType : int;
  notFinish : [Coord3];
  userdata: string;
  bindpos : Coord3;
  startpos : Coord3;
  dim : Coord3;
  items : [ItemGrid];
}

table ContainerVillageFlagIce {
  basedata : ContainerCommon;
  bindpos : Coord3;
}

table ContainerDoorplate {
  basedata : ContainerCommon;

  text : string;
  textinit : byte;
  fonttype : byte;
  changtime : ulong = 0;
  oldtext : string;
}

table ContainerSchoolFence {
	basedata : ContainerCommon;
	xsubtype : int;
	xvalue : int;
	zsubtype : int;
	zvalue : int;
}

table ContainerElectricRay {
	basedata : ContainerCommon;
	connect : [byte];

}

table ContainerAnimalEgg
{
	basedata : ContainerCommon;
	tick : uint;
	mobid : int;
	replaceBlockid : uint;
	replaceBlockdata : uint;
}

table ContainerArrowSigns 
{
  basedata : ContainerCommon;

  text : string;
  textinit : byte;
  fonttype : byte;
  changtime : ulong = 0;
  oldtext : string;
  fontscale : float;
}

table ContainerEulerSave
{
	x : float;
	y : float;
	z : float;
}

table ContainerPlayerAvatarInfo
{
	part : int;
	modelid : int;
}

table ContainerActorBodyModelBase{
	basedata : ContainerCommon;
	animId : int;
	animTick : int;
	version: int;
	bodyOffset : ContainerEulerSave;
	bodyRotate : ContainerEulerSave;
	bodyScale : float;
	headRotate : ContainerEulerSave;
	headBoneId : int;
	headScale : float;
	mutateMob : int;
	customSkins : string;
	skinShow : [string];
	equipInfo : [ItemGrid];
}

table ContainerMobBodyModel
{
	basedata : ContainerActorBodyModelBase;
	mobId : int;
	mobFb : [ubyte];
	mobType : int;
	matType : int;
	customMatcapTex : string;
	customDiffuseTex : string;
	matIntensity : float;
	matProportion : float;
}

table ContainerPlayerBodyModel
{
	basedata : ContainerActorBodyModelBase;
	playerUin : long;
	playerIndex : int;
	avatarInfo : [ContainerPlayerAvatarInfo];
}

table ContainerQuation
{
	x : float;
	y : float;
	z : float;
	w : float;
}

table ContainerManualEmitter
{
	basedata : ContainerCommon;
	playerUin : long;
	items : [ItemGrid];
	dir : byte;
	rotate : ContainerQuation;
	curTick : float;
}

table ContainerClipTrap
{
  basedata : ContainerCommon;
  modelID : string;
  dir : int;
  blocktype : int;
  triggerObjID : ulong;
}
table ContainerFusionCage {
  basedata : ContainerCommon;
  itemfst : ItemGrid;
  itemSnd : ItemGrid;
  itemTrd : ItemGrid;
  cageValue : int;
  doneValue : int;
  maxValue : int;
  interactiveuin : int;
}

table ContainerSimpleModelBase
{
	basedata : ContainerCommon;
	modelId : int;
	bodyRotate : ContainerEulerSave;
}

table ContainerDragonFlower
{
	basedata : ContainerSimpleModelBase;
}

table ContainerBellFlower
{
	basedata : ContainerSimpleModelBase;
}

table ActorVortex
{
	mobdata : ActorMob;
}

table ContainerStove {
  basedata : ContainerCommon;
  items : [ItemGrid];
  cooktime : int;
  curheat : int;
  iscooking : byte;
  craftid : int;
  makenum : int;
	dir : int;
	heat : int;
}

table ContainerDeathJar
{
  basedata : ContainerCommon;
  items : [ItemGrid];
  indices : [ubyte];
  key : int;
}

table ContainerNewPot {
	basedata : ContainerCommon;
	items : [ItemGrid];
	CookingTime : [int];
	IsMaking :bool;
	CookingProgress : float;
	CraftID : int;
	bonfireend :bool;
	inputnum :int;
	curbonfiretick :int;
	players: [uint];
	dir : int;
}

table ContainerDummy
{
	basedata : ContainerMobBodyModel;
}

table ActorTravelingTrader
{
  mobdata : ActorMob;
  styleID : int = 0;
  sleeping : bool = false;
  bodyShow : bool = false;
}

table ContainerStarStationCargo
{
    basedata : ContainerCommon;                       
    connectedstarstationid : int;
}

table ContainerMod {
	basedata : ContainerCommon;
	modelkey : string;
	blockid : uint;
	commondata : [KeyValueData];
	submeshdata : [ubyte];
	show : bool = true;
	texture : string;
	animid : int;
	animmode : ubyte;
}

table ContainerComputer {
  basedata : ContainerCommon;
}

table ContainerMonsterSummoner {
  basedata : ContainerCommon;
  active : bool;
  level : int;
  typeCD : int;
  CD : int;
  doneTime : int;
  levelType : int;
}

table ContainerTalkingStatue {
  basedata : ContainerCommon;
  textId : ulong = 0;
}

table PolaroidPartFrameInfo
{
	blockpos : Coord3;
	edgetype : int;
}

table ContainerPolaroidFrame {
  basedata : ContainerCommon;
  photourl : string;
  photostretchmode : int;
  frameurl : string;
  framewidth : uint;
  frameheight : uint;
  framepartsinfo: [PolaroidPartFrameInfo];
}

table ContainerModTransfer {
	basedata : ContainerMod;
	mapid : uint;
	stationid : uint;
}

table ContainerTerritory {
  basedata : ContainerStorage;
  flagPoint : [Coord3];
  villagers : [string];
  haveAward : bool;
}

table ContainerErosion {
  basedata : ContainerCommon;
  hp : float;
  blockid : int;
  maintenance_protection_ticks : int;
  offline_time : int;
}

table ActorCubeChest {
  basedata : ActorCommon;
  container : ContainerStorage;
  itemid : int;
}