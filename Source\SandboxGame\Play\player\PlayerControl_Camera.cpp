#include "PlayerControl.h"
#include "GameCamera.h"
#include "CameraModel.h"
#include "CameraManager.h"
#include "ClientInfoProxy.h"
#include "PlayerLocoMotion.h"
#include "TouchControlLua.h"
#include "BlockBed.h"
#include "BlockHearth.h"
#include "GameMode.h"
#include "PlayerAnimation.h"
#include "PlayerStateController.h"
#include "IClientGameManagerInterface.h"
#include "IClientGameInterface.h"
#include "PCControlLua.h"
#include "ui_framemgr.h"
#include "GunUseComponent.h"
////#include "MpGameSurvive.h"
#include "IRecordInterface.h"
#include "PlayerAttrib.h"
#include "UILib/ui_framemgr.h"
#include "Sound/MusicManager.h"
#include "ClientActorFuncWrapper.h"
#include "CarryComponent.h"
#include "RiddenComponent.h"
#include "GameNetManager.h"
#include "GameUI.h"
#include "DefManagerProxy.h"
//#include "UGCRuntime.h"
#include "AdventureGuideMgrProxy.h"
#include "WorldManager.h"
#ifdef BUILD_MINI_EDITOR_APP
#include "StudioIPCProtocol.h"
#include "SandboxSceneNetwork.h"
#include "IPCSocketMgr.h"
#endif


#include "TemperatureComponent.h"

#include "ActorBody.h"
#include "SandboxIdDef.h"
#include "OgreUtils.h"
#include "MoveControl.h"
#include "CommonTween.h"

using namespace MINIW;
using namespace Rainbow;

float _getRotateYaw(ClientActor* actor, const Rainbow::Vector3f& dir, float src)
{
	float yaw;
	Direction2PitchYaw(&yaw, NULL, dir);
	float limitAngle = actor->getRotaionLimitAngle();
	return LimitAngle(src, yaw, limitAngle);
}

void PlayerControl::setPlayerCamera(int playerindex, int mutatemob, const char* customskins)
{
	if (m_CameraModel)
	{
		if (getGunLogical())
		{
			getGunLogical()->unregisterFirstPersonEvent();
		}
		m_CameraModel->onLeaveWorld();
		OGRE_DELETE(m_CameraModel);
	}

	bool canswitch = false;
	std::function<void(bool)> func = [&](bool asyncLoad)
	{
		if (asyncLoad) {
			switchCurrentItem();
		}
		else
		{
			canswitch = true;
		}
	};
	std::string sType, sID;
	parsePlayerBaseModelID(m_strCustomModel, sType, sID);
	if (sType == "importmodel")
		m_CameraModel = ENG_NEW(CameraModel)(playerindex, mutatemob, customskins, sID.c_str(), func);
	else
		m_CameraModel = ENG_NEW(CameraModel)(playerindex, mutatemob, customskins, nullptr, func);

	//m_CameraModel = ENG_NEW(CameraModel)(playerindex, mutatemob, customskins);
	if (m_pWorld) m_CameraModel->onEnterWorld(m_pWorld);
	if (m_pCamera) m_pCamera->setCameraMode(m_CameraModel);
	if (m_PlayerAnimation) m_PlayerAnimation->refresh();

	int oldViewmod = m_oldViewMode;
	setViewMode(m_ViewMode);
	m_oldViewMode = oldViewmod;
	if (getGunLogical())
	{
		getGunLogical()->registerFirstPersonEvent();
	}
	if (canswitch) {
		switchCurrentItem();
	}

	m_CameraModel->playHandAnim(101100);
}

bool PlayerControl::isPlayerClockRotationWithCamera()
{
	int dorsumid = getCurDorsumID();
	if (m_ViewMode == CAMERA_TPS_BACK_2)
	{
		if (dorsumid == ITEM_FIRE_ROCKET)
		{
			return true;
		}

		if (dorsumid == ITEM_SNAKEGOD_WING && (isFlying() || (!getLocoMotion()->isInLiquid() && !getLocoMotion()->m_OnGround)))
		{
			return true;
		}

		if (isFishing())
		{
			return true;
		}

		if (getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL && getCatchBall())
		{
			return true;
		}

		return false;
	}

	return true;
}

void PlayerControl::facingMovingDirection(bool checkopway)
{
	bool specialRule = false;
	auto actionState = m_StateController->getActionState();
	auto moveState = m_StateController->getMovementState();
	if (getOPWay() != PLAYEROP_WAY_BASKETBALLER)
	{
		std::vector<int> list;
		int animSeqIdCount = getBody()->getCurPlayAnimIds(list);
		// 				LOG_INFO("now animSeqIdCount = %d, animState = %d, moveState = %d", animSeqIdCount, actionState, moveState);
		// 				if(list.size())
		for (int i = 0; i < (int)list.size(); i++)
		{
			specialRule |= checkIsFreeViewFormTPSBACK2(list.at(i), moveState, actionState);
			// 					LOG_INFO("now animSeqId%d = %d", i, list.at(i));
			if (specialRule)
			{
				break;
			}
		}
	}

	float rotateYaw = 0;
	float rotationPitch = 0;
	Rainbow::Vector3f forward(m_pCamera->getLookDir().GetNormalized());
	//auto forward = m_pCamera->getEngineCamera()->GetForward();
	Direction2PitchYaw(&rotateYaw, &rotationPitch, forward);

	if (!isInSpectatorMode() && !isInvisibleByRide())	//  ********：处于坐骑隐身技能中不设置模型显示状态  codeby： keguanqiang
	{
		if (rotationPitch < -48)
		{
			getBody()->show(false);
		}
		else
		{
			getBody()->show(true);
		}
	}

	//乘坐过山车时锁定视角（用于处理动作视角下过山车不会后退问题） by：Jeff
	auto RidComp = getRiddenComponent();
	ClientActor* RidingActor = NULL;
	bool isRidingTrain = false;
	if (RidComp)
	{
		RidingActor = RidComp->getRidingActor();
	}
	if (RidingActor)
	{
		isRidingTrain = RidingActor->getObjType() == OBJ_TYPE_MINECART;
	}

	if (checkopway == false || (getOPWay() == PLAYEROP_WAY_BASKETBALLER && ("Obstruct" == actionState || "Grab" == actionState)) ||
		specialRule || isRidingOnCrab() || isRidingTrain)
	{
		//下面是锁定朝向
		lockEyeDirWithView();
	}
	else
	{
		if (m_MoveRight != 0 || m_MoveForward != 0)
		{
			//以当前相机朝向为正前方参考，旋转角色
// 					float rotateYaw_Input = 0;
// 					float rotationPitch_Input = 0;
// 					Rainbow::Vector3f dirinput(m_MoveRight, 0, m_MoveForward);
// 					dirinput  = MINIW::Normalize(dirinput);
// 					Direction2PitchYaw(&rotateYaw_Input, &rotationPitch_Input, dirinput);
// 					rotateYaw += (rotateYaw_Input + 180);
// 					PitchYaw2Direction(dirinput, rotateYaw, rotationPitch);

					//引擎中角色面向z的负方向，所以这里要取反
			auto right = -CrossProduct(forward, Vector3f::yAxis).GetNormalized();
			auto dirinput = m_MoveForward * forward + m_MoveRight * right;

			m_MoveForward = (float)Sqrt(m_MoveRight * m_MoveRight + m_MoveForward * m_MoveForward);
			m_MoveRight = 0;
			/*
			if ((!getPlayerAttrib()->isNewStatus() && getPlayerAttrib()->hasBuff(MOVEREVERSE_BUFF)) || getPlayerAttrib()->hasStatusEffect(STATUS_EFFECT_MOVEREVERSE))
			{
				static_cast<LivingLocoMotion*>(getLocoMotion())->m_MoveForward = -m_MoveForward;
				static_cast<LivingLocoMotion*>(getLocoMotion())->m_MoveStrafing = -m_MoveRight;
			}
			else
			{
				static_cast<LivingLocoMotion*>(getLocoMotion())->m_MoveForward = m_MoveForward;
				static_cast<LivingLocoMotion*>(getLocoMotion())->m_MoveStrafing = m_MoveRight;
			}
			*/
			LivingAttrib* livingAttrib = getLivingAttrib();
			if (livingAttrib && livingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE)) {

			}
			else
			{
				WCoord targetPos = getEyePosition() + dirinput * 100.0f;
				setLookAt(targetPos.x, targetPos.y, targetPos.z, 30.0f, 30.0f);
				//没有被限制行动时，允许旋转视角
				if (getFreezing() != FREEZING_STATE_NOMOVE)
				{
					getLocoMotion()->setMoveDir(dirinput);
					if (isNewMoveSyncSwitchOn())
					{
						setMoveControlYaw(getLocoMotion()->m_RotateYaw);
					}
				}
				//LogStringMsg("==> moving and rotate body to:%f", getLocoMotion()->m_RotateYaw);
			}
		}
		else
		{
			if (getBody()->isLookAt())
			{
				getBody()->setLookTargetPitch(rotationPitch);
			}
			else
			{
				LivingAttrib* livingAttrib = getLivingAttrib();
				if (livingAttrib && livingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE)) {

				}
				else
				{
					Rainbow::Vector3f dirinput;
					PitchYaw2Direction(dirinput, getLocoMotion()->m_RotateYaw, rotationPitch);
					WCoord targetPos = getEyePosition() + dirinput * 100.0f;
					setLookAt(targetPos.x, targetPos.y, targetPos.z, 30.0f, 30.0f);
				}
				//LogStringMsg("==> stand and rotate body to:%f", getLocoMotion()->m_RotateYaw);
			}
		}
	}
}

void PlayerControl::resetEyeDir(bool checkopway)
{
	//跟随的观战模式摄像机处理不同
	if ((isInSpectatorMode() && getSpectatorType() == SPECTATOR_TYPE_FOLLW))
	{
		ClientPlayer* toSpectatorplayer = getToSpectatorPlayer();
		if (toSpectatorplayer)
		{
			Rainbow::Quaternionf quat;
			Rainbow::Vector3f lookdir;
			quat = AngleEulerToQuaternionf(Vector3f(toSpectatorplayer->getLocoMotion()->m_RotationPitch, toSpectatorplayer->getLocoMotion()->m_RotateYaw, 0));
			//quat.setEulerAngle(toSpectatorplayer->getLocoMotion()->m_RotateYaw, toSpectatorplayer->getLocoMotion()->m_RotationPitch, 0);
			//quat.rotate(lookdir, Rainbow::Vector3f(0, 0, 1.0f));
			lookdir = RotateVectorByQuat(quat, Rainbow::Vector3f::zAxis);
			getLocoMotion()->setMoveDir(lookdir);
			if (isNewMoveSyncSwitchOn())
			{
				setMoveControlYaw(getLocoMotion()->m_RotateYaw);
			}

			WCoord targetPos = getEyePosition() + lookdir * 100.0f;
			setLookAt(targetPos.x, targetPos.y, targetPos.z, 30.0f, 30.0f);
		}
	}
	else if (m_ViewMode == CAMERA_CUSTOM_VIEW)
	{
		int rotating = getCameraConfigOption(CAMERA_OPTION_INDEX_ROTATING);

		if (CameraManager::GetInstance().getCameraEditState() != CAMERA_EDIT_STATE_EDIT
			&& (rotating == CRT_CAMERA_AND_BODY || rotating == CRT_ONLY_BODY))
		{
			float yaw, pitch;
			int rotating_limit = getCameraConfigOption(CAMERA_OPTION_INDEX_ROTATING_LIMIT);

			if (getCameraConfigOption(CAMERA_OPTION_INDEX_CAMERA_MOTION) == CAR_FOLLOWED_CAMERA)
			{
				Rainbow::Vector3f offdir = m_CurrentCameraConfig.getCameraLookDir();
				// 当前相机朝向 - 朝向偏移 = 当前人物朝向
				Rainbow::Vector3f bodydir = Rainbow::Vector3f(m_pCamera->m_RotateYaw, m_pCamera->m_RotatePitch, 0.0f) - offdir;

				yaw = bodydir.x + 180.0f;
				pitch = bodydir.y;
			}
			else
			{
				yaw = m_pCamera->m_RotateYaw + 180.0f;
				pitch = m_pCamera->m_RotatePitch;
			}
			// 侧视角，人脸朝向跟随移动方向变化
			bool need_turn = false;
			if (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_CONFIG_SET) == CCG_FLATVIEW)
			{
				if (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_FACE) == CFBD_YES_TURN_FACE && m_CurrentCameraConfig.NeedReverse)
				{
					m_CurrentCameraConfig.HasReversed = !m_CurrentCameraConfig.HasReversed;
					m_CurrentCameraConfig.NeedReverse = false;
					need_turn = true;
				}
			}

			// 旋转限制
			switch (rotating_limit)
			{
			case CRL_NO_LIMIT:
				yaw += m_CurrentCameraConfig.HasReversed ? 180.0f : 0;

				getLocoMotion()->m_RotateYaw = yaw;
				getLocoMotion()->m_RotationPitch = pitch;
				if (isNewMoveSyncSwitchOn())
				{
					setMoveControlYaw(yaw);
					setMoveControlPitch(pitch);
				}
				break;
			case CRL_ONLY_X:
				yaw += m_CurrentCameraConfig.HasReversed ? 180.0f : 0;

				getLocoMotion()->m_RotateYaw = yaw;
				if (isNewMoveSyncSwitchOn())
					setMoveControlYaw(yaw);
				break;
			case CRL_ONLY_Y:
				if (need_turn)
				{
					yaw += m_CurrentCameraConfig.HasReversed ? 180.0f : 360.0f;
					getLocoMotion()->m_RotateYaw = yaw;
					if (isNewMoveSyncSwitchOn())
						setMoveControlYaw(yaw);
				}
				getLocoMotion()->m_RotationPitch = pitch;
				if (isNewMoveSyncSwitchOn())
					setMoveControlPitch(pitch);
				break;
			}

			setLookAt(getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch, 10.0f, 10.0f);
		}
		else if (CameraManager::GetInstance().getCameraEditState() != CAMERA_EDIT_STATE_EDIT && rotating == CRT_NOTHING && 
			m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_PLAYER_FACING_MOVING_DIRC) == PFMD_FACING_MOVING_DIRECTION_YES)
		{
			facingMovingDirection(checkopway);
		}
	}
	else
	{
		if (!isPlayerClockRotationWithCamera())
		{
			facingMovingDirection(checkopway);
		}
		else
		{
			auto funcWrapper = getFuncWrapper();
			bool isBtreeControl = funcWrapper ? funcWrapper->getBtreeControl() : false;
			if (!isBtreeControl)
			{
				Rainbow::Vector3f lookdir(m_pCamera->getLookDir());
				WCoord targetPos = getEyePosition() + lookdir * 100.0f;
				if (getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL && (m_ViewMode != CameraControlMode::CAMERA_FPS && m_ViewMode != CameraControlMode::CAMERA_TPS_BACK && m_ViewMode != CameraControlMode::CAMERA_TPS_BACK_SHOULDER) && getCatchBall())
				{

				}
				else
				{
					int isForbidRotate = false;
					LivingAttrib* livingAttrib = getLivingAttrib();
					if (livingAttrib && livingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE)) {
						isForbidRotate = true;
					}
					if (getOPWay() == PLAYEROP_WAY_FISHING || isForbidRotate)		// 钓鱼不转人物朝向
					{

					}
					else
					{
						setLookAt(targetPos.x, targetPos.y, targetPos.z, 30.0f, 30.0f);
					}
					//没有被限制行动时，允许旋转视角
					if (getFreezing() != FREEZING_STATE_NOMOVE)
					{
						getLocoMotion()->setMoveDir(lookdir);
						if (isNewMoveSyncSwitchOn())
						{
							setMoveControlYaw(getLocoMotion()->m_RotateYaw);
						}
					}
				}

			}
			if (!IsRunSandboxPlayer() && !isInSpectatorMode() && !isInvisibleByRide())	// ********：处于坐骑隐身技能中不设置模型显示状态  codeby： keguanqiang
			{
				if (m_ViewMode == CameraControlMode::CAMERA_FPS)
				{
					getBody()->show(false);
				}
				else
				{
					getBody()->show(true);
				}
			}
		}
	}

	resetEyeDirExUGC(checkopway);
}

bool PlayerControl::checkIsFreeViewFormTPSBACK2(int animSeqId, const std::string& moveState, const std::string& actionState)
{
	//手持武器的Idle状态
	if (isComboWeaponInHand() && (animSeqId == 600169 || animSeqId == 600168 || animSeqId == 600142))
		return false;

	if (isSleeping() || isRestInBed() || getSitting() || isDead())
		return false;

	// 锁定朝向
	if ("ChargeAttack" == actionState || "GunUse" == actionState || "UseEmitter" == actionState)
	{
		return true;
	}

	// 动作列表
	static std::vector<int> animSeqIdList = {
		0, 100101, 100111, 100109, 100117, 100114, 100115, 100103,
		100123, 100100, 100113, 100144, 100137, 100136, 101002,
		600100, 600101, 600102, 600103, 600104, 100116	// 20211019：播放跳舞动作时候在背视角2时候人物不能随着视角变化而变化  codeby： huangxin
	};

	// 检查是否有动作id
	bool hasSeqId = false;
	for (auto const& seqId : animSeqIdList)
	{
		if (seqId == animSeqId)
		{
			hasSeqId = true;
			break;
		}
	}

	if (!hasSeqId && !(100104 == animSeqId && ("Swim" == moveState || "Walk" == moveState || "Jetpack" == moveState))
		&& !(100121 == animSeqId && "Fly" == moveState) && !isIdolExpressionAction(animSeqId)
		&& !(100105 == animSeqId && actionState == "Sit"))	//背视角2坐下时会有一帧攻击动作导致设置的朝向失效，这里做处理 by：Jeff 2023.3.7
	{
		return true;
	}

	//if ("ChargeAttack" == actionState || "GunUse" == actionState || "UseEmitter" == actionState || 
	//		(0 != animSeqId && !((100101 == animSeqId || 100111 == animSeqId)/* && (Walk == moveState || Idle == moveState)*/) &&
	//		!(100109 == animSeqId/* && Jump == moveState*/) && !(100104 == animSeqId && ("Swim" == moveState || "Walk" == moveState || "Jetpack" == moveState)) &&
	//		!(100117 == animSeqId/* && Eat == actionState*/) && !(100121 == animSeqId && "Fly" == moveState) && 100114 != animSeqId && 100115 != animSeqId && 100103 != animSeqId &&
	//		100123 != animSeqId && !(100100 == animSeqId/* && ActionIdle == actionState*/) && 100113 != animSeqId && 100144 != animSeqId && 100137 != animSeqId && 100136 != animSeqId &&
	//		!isIdolExpressionAction(animSeqId)
	//		// 20211019：播放跳舞动作时候在背视角2时候人物不能随着视角变化而变化  codeby： huangxin
	//		&& 600100 != animSeqId && 600101 != animSeqId && 600102 != animSeqId && 600103 != animSeqId && 600104 != animSeqId && 100116 != animSeqId)
	//	//背视角2坐下时会有一帧攻击动作导致设置的朝向失效，这里做处理 by：Jeff 2023.3.7
	//	&& !(100105 == animSeqId && actionState == "Sit"))
	//{
	//	return true;
	//}
	return false;
}

Rainbow::Vector3f PlayerControl::getCameraLookDir()
{
	if (m_ViewMode == CAMERA_TPS_BACK_2)
	{
		return m_pCamera->getLookDir();
	}

	return ClientPlayer::getCameraLookDir();
}

void PlayerControl::lockEyeDirWithView()
{
	Rainbow::Vector3f lookdir(m_pCamera->getLookDir());
	WCoord targetPos = getEyePosition() + lookdir * 100.0f;
	setLookAt(targetPos.x, targetPos.y, targetPos.z, 30.0f, 30.0f);
	float rotateYaw = 0;
	float rotationPitch = 0;
	Direction2PitchYaw(&rotateYaw, &rotationPitch, m_pCamera->getLookDir());
	getLocoMotion()->m_RotateYaw = rotateYaw;
	getLocoMotion()->m_RotationPitch = rotationPitch;
	if (isNewMoveSyncSwitchOn())
	{
		setMoveControlYaw(rotateYaw);
		setMoveControlPitch(rotationPitch);
	}
	//没有被限制行动时，允许旋转视角
	if (getFreezing() != FREEZING_STATE_NOMOVE)
	{
		getLocoMotion()->setMoveDir(lookdir);
		if (isNewMoveSyncSwitchOn())
		{
			setMoveControlYaw(getLocoMotion()->m_RotateYaw);
		}
	}
}

void PlayerControl::setUIHide(bool b, bool isShowCursor, bool isChangeDispayName)
{
	GetGameUIPtr()->setUIHide(b);
	// 某些情况下，不需要改变昵称的可见性
	if (isChangeDispayName)
	{
		getBody()->setVisibleDispayName(!b);
	}
	if (GetIClientGameManagerInterface()->getICurGame())
		GetIClientGameManagerInterface()->getICurGame()->setPlayerVisibleDispayName(!b);

	if (GetGameUIPtr()->isUIHide())
	{
		if (isShowCursor)
		{
			GetGameUIPtr()->ShowCursor(true);
		}
		else
		{
			GetGameUIPtr()->ShowCursor(false);
		}
	}
	else
	{
		GetGameUIPtr()->ShowCursor(true);
	}
}

void PlayerControl::switchActView()
{
	if (g_WorldMgr == NULL)
		return;

	if (g_WorldMgr->m_RuleMgr == NULL)
		return;

	if (!(g_WorldMgr->isGameMakerRunMode() && g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_CAMERA) == 3))
	{
		if (getViewMode() == CAMERA_FPS)
		{
			m_NeedRevertToFPS |= 4;
			setViewMode(CAMERA_TPS_BACK);
		}
	}
}

//20210927 codeby:chenwei 装扮互动切换视角模式抽取到函数
void PlayerControl::switchSkinActView()
{
	if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_CAMERA) == 3))
	{
		//202101020 codeby:wangyu 正视角、俯视角和自由视角则不切换
		if (getViewMode() == CAMERA_TPS_FRONT || getViewMode() == CAMERA_TPS_OVERLOOK || getViewMode() == CAMERA_TPS_BACK_2)
		{
			return;
		}
		//20210928 codeby:chenwei 所有动画都切可环绕视图 //20211020 codeby:wangyu 修复记录了还没恢复的视角
		if (m_ActBeforeViewMode == -1)
		{
			m_ActBeforeViewMode = getViewMode();  //20210929 codeby:chenwei 记录播放前视角
		}
		setViewMode(CAMERA_TPS_BACK_2);
	}
}

void PlayerControl::recoverActView()
{
	if (m_NeedRevertToFPS & 4)
	{
		setViewMode(CAMERA_FPS);
		m_NeedRevertToFPS &= (~4);
		m_ActBeforeViewMode = -1;
	}
	else if (m_ActBeforeViewMode >= CameraControlMode::CAMERA_FPS &&
		m_ActBeforeViewMode < CameraControlMode::CAMERA_MAX_MODE &&
		!isSittingInStarStationCabin())
	{
		// 恢复播放前视角
		setViewMode(m_ActBeforeViewMode);
		m_ActBeforeViewMode = -1;
	}
}

void PlayerControl::setViewMode(int mode, bool ignoreBasketBaller/* =false */)
{
	/*if (m_ViewMode == mode) return;
	if (GetDefManagerProxy()->isFishNeedUp(m_Body->getCurShowEquipItemId(EQUIP_WEAPON)))//举鱼状态下，如果被其他地方切换成第一人称视角则强行切换为第三人称视角。
	{
		if (mode == CameraControlMode::CAMERA_FPS)
		{
			if (m_oldViewMode == CameraControlMode::CAMERA_TPS_BACK)
			{
				mode = CameraControlMode::CAMERA_TPS_BACK_2;
			}
			else
			{
				mode = CameraControlMode::CAMERA_TPS_BACK;
			}
		}
	}
	if (getOPWay() == PLAYEROP_WAY_BASKETBALLER && !ignoreBasketBaller)
	{
		mode = CAMERA_TPS_BACK_2;
	}
	if (mode < CameraControlMode::CAMERA_THIRD_SHOULDER) {
		if (mode<0 || mode>getViewModeNum()) mode = CameraControlMode::CAMERA_FPS;
	}
	if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		mode = CameraControlMode::CAMERA_TPS_BACK;
	}

	auto CarryComp = getCarryComponent();
	if (CarryComp && CarryComp->isCarrying()) {
		//扛起野人时视角设置
		mode = CameraControlMode::CAMERA_TPS_BACK;
	}

	// 冒险强制新手引导强制第一视角
	if (!GetAdventureGuideMgrProxy()->canInput())
	{
		mode = CameraControlMode::CAMERA_FPS;
	}
	//如果是持新枪状态，只能切第一人称和第三人称
	if (g_pPlayerCtrl->m_GunHoleState == GunHoldState::CUSTOMGUN)
	{
		if (mode != CameraControlMode::CAMERA_FPS && mode != CameraControlMode::CAMERA_TPS_BACK)
			return;
	}

	if (m_iRecoverViewMode >= 0 && isShapeShift()) // 变形状态下锁定了视角不能切换
	{
		return;
	}
	*/


	//soc中只有两个视角，第一和第三人称视角
	if (GetClientInfoProxy()->GetAppId() != 999)
	{
		if (mode != CameraControlMode::CAMERA_FPS && mode != CameraControlMode::CAMERA_TPS_BACK_SHOULDER/*CAMERA_TPS_BACK*/)
		{
			mode = CameraControlMode::CAMERA_FPS;
		}
	}
	// if (mode == m_ViewMode)
	// {
	// 	return;
	// }
	
	m_oldViewMode = m_ViewMode;
	m_ViewMode = mode;
	if (m_pCamera)
	{
		m_pCamera->setMode(CameraControlMode(m_ViewMode));
		if (m_ViewMode == CameraControlMode::CAMERA_TPS_BACK_2)
		{
			m_pCamera->setDistMultiply(1.2f);
		}
	}
	if (getGunLogical())
	{
		getGunLogical()->setComViewMode(mode);
	}

	CheckSpectatorPlayerShow();

	MINIW::ScriptVM::game()->callFunction("ViewModeChange", "");

	int iToolId = getCurToolID();
	if (GetDefManagerProxy()->getCustomGunDef(iToolId))
	{
		applyEquipsOnShortcut(EQUIP_WEAPON, false);//新的枪械子部件第一人称和第三人称渲染层级问题
	}

	if (getPlayerAttrib())
		getPlayerAttrib()->ApplyWeaponEnchantEffect(getBody());

	sendViewModeToSpectator();

	needHandleWeaponMotionForView(getWeaponMotionStatus(), getWeaponMotionName(), true);
	m_CameraOffset.Set(0, 0, 0);
	
#ifdef BUILD_MINI_EDITOR_APP
	CmdData cmdData;
	cmdData.cmdType = CMDTYPE_NOTIFY;
	cmdData.cmdName = static_cast<unsigned>(PROTOCOL::CameraAngleChange_G2E);
	jsonxx::Object obj;
	obj << jsonxx::String("ccm") << jsonxx::Number(m_ViewMode);
	cmdData.pCmdParam = &obj;
	WSServerMgr::getSingletonPtr()->sendCMDData(WSServerMgr::getSingletonPtr()->mainServer, cmdData);
#endif
}

void PlayerControl::setViewMode(int mode, float x, float y, float z)
{
	setViewMode(mode);
	m_CameraOffset.Set(x, y, z);
}

void PlayerControl::SetViewLock(int mode, bool bLock)
{
	setViewMode(mode);
	m_ViewLock = bLock;
}

bool PlayerControl::IsViewLock()
{
	return m_ViewLock;
}

static int changeViewModelCustomOrder[] = {
	CameraControlMode::CAMERA_FPS,
	CameraControlMode::CAMERA_TPS_BACK_2,
	CameraControlMode::CAMERA_TPS_FRONT,
	CameraControlMode::CAMERA_TPS_BACK,
	CameraControlMode::CAMERA_TPS_OVERLOOK,
	CameraControlMode::CAMERA_CUSTOM_VIEW,
	-1,
};

static int changeViewModelPlayDefaultOrder[] = {
	CameraControlMode::CAMERA_FPS,
	CameraControlMode::CAMERA_TPS_BACK_2,
	CameraControlMode::CAMERA_TPS_FRONT,
	CameraControlMode::CAMERA_TPS_BACK,
	CameraControlMode::CAMERA_TPS_OVERLOOK,
	-1,
};

static int changeViewModelDefault[] = {
	CameraControlMode::CAMERA_FPS,
	CameraControlMode::CAMERA_TPS_BACK_2,
	CameraControlMode::CAMERA_TPS_FRONT,
	CameraControlMode::CAMERA_TPS_BACK,
	-1,
};

int   PlayerControl::getChangeNextViewMode()
{
	int* changeViewModelOrder = changeViewModelDefault;
	// 玩法模式下，多两种视角：俯视角和自定义视角
	if (m_pWorld && g_WorldMgr && (g_WorldMgr->isGameMakerMode() || g_WorldMgr->isGameMakerRunMode()))
	{
		// 设置过自定义视角
		if (g_WorldMgr->getCustomCameraOption(CAMERA_OPTION_INDEX_CONFIG_SET) != CCG_SYSTEM_DEFAULT)
			changeViewModelOrder = changeViewModelCustomOrder;
		else
			changeViewModelOrder = changeViewModelPlayDefaultOrder;
	}

	int curIndex = 0;
	int curSize = 1;
	while (changeViewModelOrder[curSize - 1] != -1)
	{
		if (changeViewModelOrder[curSize - 1] == m_ViewMode)
		{
			curIndex = curSize - 1;
		}
		curSize++;
	}

	int nexindex = (curIndex + 1) % (curSize - 1);

	return changeViewModelOrder[nexindex];
	//return (m_ViewMode + 1) % g_pPlayerCtrl->getViewModeNum();
}


int PlayerControl::getViewModeNum()
{
	// 玩法模式下，多两种视角：俯视角和自定义视角
	if (m_pWorld && GetWorldManagerPtr() && (GetWorldManagerPtr()->isGameMakerMode() || GetWorldManagerPtr()->isGameMakerRunMode()))
	{
		// 设置过自定义视角
		if (GetWorldManagerPtr()->getCustomCameraOption(CAMERA_OPTION_INDEX_CONFIG_SET) != CCG_SYSTEM_DEFAULT)
			return  CAMERA_MAX_MODE - 2; //减去钢琴的两种新增视角
		else
			return CAMERA_MAX_MODE - 3; //减去钢琴的两种新增视角
	}
	//使用相机的时候可以切到自定义相机
	if (IsUsePolaroidCamera())
		return 5;
	return 4;
}

//20210724: 触发器新API  codeby:wangshuai
bool PlayerControl::isShakingCamera()
{
	return m_IsShaking;
}
//20210724: 触发器新API  codeby:wangshuai
void PlayerControl::setShakeCamera(bool shaking, int power, float duration)
{
	m_IsShaking = shaking;
	if (!shaking) {
		m_pCamera->m_ShakingPower.x = 0;
		m_pCamera->m_ShakingPower.y = 0;
		m_pCamera->m_ShakingPower.z = 0;
		m_pCamera->m_ShakingDuration = 0;
		m_pCamera->m_ShakingOriginDuration = 0;
	}
	else {
		if (power <= 1) {
			power = 1;
		}
		else if (power >= 1000) {
			power = 1000;
		}
		if (duration <= 0) {
			duration = 1;
		}
		float x = 0.1f * power;
		float y = 0.1f * power;
		// float z = 0.01f * power;
		m_pCamera->m_ShakingPower.x = x;
		m_pCamera->m_ShakingPower.y = y;
		m_pCamera->m_ShakingPower.z = 0;
		m_pCamera->m_ShakingDuration = duration;
		m_pCamera->m_ShakingOriginDuration = duration;
	}
}

void PlayerControl::TweenRotCameraKeyFrame(float keyDeltaTime, float degreeValue, string tweenFun)
{
	if (!m_pCamera)
	{
		return;
	}

	if (m_tweenID)
	{
		CommonTween::Free(m_tweenID);
		m_tweenID = 0;
		m_tween = nullptr;
	}

	if (!m_tween)
	{
		m_tween = &CommonTween::Create()
			.Init({ { "RotDegree", {(float)0.0f},
			[this](string& attrName, vector<float>& attrVal) {
				if (m_pCamera)
				{
					float rotDegree = attrVal[0];
					Quaternionf quat = AxisAngleToQuaternionf(Vector3f(0, 0, 1), Deg2Rad(rotDegree));
					Quaternionf quatStart = m_pCamera->GetEngineCameraRot();
					Quaternionf quatTo = quatStart * quat;
					m_pCamera->SetEngineCameraRot(quatTo);
				}
			} } },
				[this](int id) {
				m_tweenID = 0;
			m_tween = nullptr;
			});
	}

	((CommonTween*)m_tween)->To(keyDeltaTime, { { "RotDegree", {degreeValue}, tweenFun } });
}

void PlayerControl::TweenRotCameraStart()
{
	if (m_tweenID == 0 && m_tween)
	{
		((CommonTween*)m_tween)->Start(m_tweenID);
	}
}

void PlayerControl::TweenRotCameraClear()
{
	if (m_tweenID > 0 && m_tween && m_pCamera)
	{
		CommonTween::Free(m_tweenID);
		m_tweenID = 0;
		m_tween = nullptr;

		Quaternionf quat = AxisAngleToQuaternionf(Vector3f::zAxis, 0.f);
		Quaternionf quatStart = m_pCamera->GetEngineCameraRot();
		Quaternionf quatTo = quatStart * quat;
		m_pCamera->SetEngineCameraRot(quatTo);
	}
}

CameraConfig* PlayerControl::getCurCameraConfig()
{
	return &m_CurrentCameraConfig;
}

void PlayerControl::resetCameraPos()
{
	float yaw, pitch;
	Rainbow::Vector3f offset;
	int config_set = m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_CONFIG_SET);

	// 根据玩家当前位置，重新计算摄像机的位置
	switch (config_set)
	{
	case CCG_SYSTEM_DEFAULT:
	case CCG_BACKVIEW:
	{
		//offset = Rainbow::Vector3f(162.0f, 34.0f, -190.0f);
		offset = Rainbow::Vector3f(47, 66, -166);
		yaw = getLocoMotion()->m_RotateYaw + 180.0f;
		pitch = 15.0f;
		break;
	}
	case CCG_FLATVIEW:
	{
		offset = Rainbow::Vector3f(500, 0, 0);
		yaw = getLocoMotion()->m_RotateYaw + 180.0f - 90.0f;
		pitch = 0.0f;
		break;
	}
	case CCG_TOPVIEW:
	{
		offset = Rainbow::Vector3f(0, 600, 0);
		yaw = getLocoMotion()->m_RotateYaw + 180.0f;
		pitch = 85.0f;
		break;
	}
	}

	m_pCamera->setFov(getCameraConfigFov());
	m_pCamera->getEngineCamera()->SetVerticalFieldOfView(getCameraConfigFov());


	// 先把人物调整成平视
	setLookAt(getLocoMotion()->m_RotateYaw, 0, 360.0f, 360.0f);
	getLocoMotion()->m_RotationPitch = 0.0f;

	m_CurrentCameraConfig.InitPlayerYaw = getLocoMotion()->m_RotateYaw;
	m_CurrentCameraConfig.InitPlayerPitch = getLocoMotion()->m_RotationPitch;

	m_pCamera->setRotate(yaw, pitch);

	m_pCamera->getEngineCamera()->SetWorldRotation(Rainbow::Quaternionf(pitch, yaw, 0, 1.0f));
	setCameraConfigLookDir();

	m_CurrentCameraConfig.setCameraPosition(offset);
	m_pCamera->getEngineCamera()->SetWorldPosition(getCameraConfigPosition(getLocoMotion()->m_RotateYaw + 180.0f, getLocoMotion()->m_RotationPitch).toVector3());

}

void PlayerControl::setCameraRotate(int Yaw, int Pitch, float mul)
{
	if (m_pCamera)
	{
		m_pCamera->setRotate((float)Yaw, (float)Pitch);
		m_pCamera->setDistMultiply(mul);
	}
}

void PlayerControl::applyCurCameraConfig()
{
	if (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_CONFIG_SET) != CCG_SYSTEM_DEFAULT)
	{
		// 进入自定义视角时需要先把人物调整成平视
		setLookAt(getLocoMotion()->m_RotateYaw, 0, 360.0f, 360.0f);
		getLocoMotion()->m_RotationPitch = 0.0f;

		m_CurrentCameraConfig.InitPlayerYaw = getLocoMotion()->m_RotateYaw;
		m_CurrentCameraConfig.InitPlayerPitch = getLocoMotion()->m_RotationPitch;

		// 应用自定义视角中摄像机的设置
		auto dir = getCameraConfigLookDir();

		m_pCamera->setFov(getCameraConfigFov());
		m_pCamera->getEngineCamera()->SetVerticalFieldOfView(getCameraConfigFov());
		m_pCamera->getEngineCamera()->SetWorldPosition(getCameraConfigPosition(getLocoMotion()->m_RotateYaw + 180.0f, getLocoMotion()->m_RotationPitch).toVector3());
		//m_pCamera->getEngineCamera()->SetWorldPosition(getCameraConfigPosition(m_LocoMotion->m_RotateYaw + 180.0f, m_LocoMotion->m_RotationPitch).toVector3());
		m_pCamera->getEngineCamera()->SetWorldRotation(AngleEulerToQuaternionf(Vector3f(dir.y, dir.x, 0)));
		m_pCamera->setRotate(dir.x, dir.y);

		m_CurrentCameraConfig.HasReversed = m_CurrentCameraConfig.NeedReverse = false;

		// 移动引导提示箭头
		//Rainbow::Quaternionf quat = getBody()->getRotation();
		//Rainbow::Vector3f off(0, -50.0f, -100.0f);
		//quat.rotate(off, off);

		//m_MoveDirective->setPosition(getEyePosition().toVector3() + off);
		//m_MoveDirective->update(TimeToTick(dtime));
		//m_MoveDirective->show(true);
		//m_MoveDirective->setScale(Rainbow::Vector3f(0.3f, 0.3f, 0.3f));
		//m_MoveDirective->setRotation(getLocoMotion()->m_RotationPitch, -90.0f, 0.0f);
		//m_MoveDirective->setPosition(WorldPos(0, 1000, -300));
		//getBody()->getEntity()->bindObject(106, m_MoveDirective);
	}
	else
	{
		//躺床上或者睡觉不调整
		if (!isRestInBed() && !isSleeping() && !isUseHearth())
		{
			// 把摄像机的朝向调整和人物朝向一致
			auto RidComp = getRiddenComponent();
			if (((!RidComp) || (RidComp && !RidComp->isVehiclePassenger())) && getLocoMotion())
				m_pCamera->setRotate(getLocoMotion()->m_RotateYaw + 180.0f, getLocoMotion()->m_RotationPitch);
			//m_pCamera->getEngineCamera()->SetVerticalFieldOfView(getCameraConfigFov());	// same as next: m_pCamera->setFov(getCameraConfigFov());
			m_pCamera->setFov(getCameraConfigFov());
		}

		// 移动引导提示箭头
		//m_MoveDirective->show(false);
		//getBody()->getEntity()->unbindObject(m_MoveDirective);
	}

	UpdateXrayEffectEnable();
}

void PlayerControl::resetCurCameraConfig(bool isSavedConfig)
{
	if (isSavedConfig)
	{
		if (GetWorldManagerPtr()->m_CustomCamera)
			m_CurrentCameraConfig = *GetWorldManagerPtr()->m_CustomCamera;
	}
	else
	{
		CameraConfig::GetDefaultValue(getCamera()->getMode() == CAMERA_FPS ? CCG_SYSTEM_DEFAULT : CCG_BACKVIEW, m_CurrentCameraConfig);
	}
}

int PlayerControl::getCameraConfigOption(unsigned char option)
{
	return m_CurrentCameraConfig.getOption(option);
}

void PlayerControl::setCameraConfigOption(unsigned char option, unsigned char value)
{
	m_CurrentCameraConfig.setOption(option, value);

	// 修改摄像机的运动类型时，同步重新计算摄像机的位置
	switch (option)
	{
	case CAMERA_OPTION_INDEX_CAMERA_MOTION:
	case CAMERA_OPTION_INDEX_CONFIG_SET:
		setCameraConfigPosition();
		setCameraConfigLookDir();
		break;
	case CAMERA_OPTION_INDEX_AUTO_ZOOM:
		if (isHost())
		{
			SetXrayEffectEnable(value == CAZ_AUTO_ZOOM_NO_AND_XRAY);
		}
		break;
	}
}

float PlayerControl::getCameraConfigFov()
{
	return m_CurrentCameraConfig.getFov();
}

void PlayerControl::setCameraConfigFov(float fov)
{
	m_CurrentCameraConfig.setFov(fov);
	if (m_pCamera)
	{
		m_pCamera->setFov(fov);
		if (m_pCamera->getEngineCamera())	
		{
			m_pCamera->getEngineCamera()->SetVerticalFieldOfView(fov);
		}
	}
}

WCoord PlayerControl::getCameraConfigPosition(float yaw, float pitch)
{
	WCoord pos = m_CurrentCameraConfig.getCameraPosition();

	//相对位置转换成绝对位置
	if (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_CAMERA_MOTION) == CAR_FOLLOWED_CAMERA)
	{
		Rainbow::Quaternionf quat;
		if (getBody() == NULL) return pos;
		Rainbow::Vector3f offset, eyepos = getBody()->getBindPointPos(0) + Rainbow::Vector3f(0, getEyeHeight() + 10.0f, 0);

		if (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_ROTATING) == CRT_ONLY_BODY
			|| m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_ROTATING) == CRT_NOTHING)
		{

			//quat.setEulerAngle(m_CurrentCameraConfig.InitPlayerYaw + 180.0f, 0, 0.0f);
			//quat.rotate(offset, pos.toVector3());
			quat = AngleEulerToQuaternionf(Vector3f(0, m_CurrentCameraConfig.InitPlayerYaw + 180.0f, 0.0f));
			offset = RotateVectorByQuat(quat, pos.toVector3());
		}
		else
		{
			//quat.setEulerAngle(yaw, 0, 0.0f);
			//quat.rotate(offset, pos.toVector3());
			quat = AngleEulerToQuaternionf(Vector3f(0, yaw, 0.0f));
			offset = RotateVectorByQuat(quat, pos.toVector3());
		}

		if (m_pWorld && m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_AUTO_ZOOM) == CAZ_AUTO_ZOOM_YES)
		{
			// 视线阻挡判断
			float dist = offset.Length();
			offset.NormalizeSafe();
			pos = WCoord(m_pCamera->CalCollidedEyePos(m_pWorld, Rainbow::WorldPos::fromVector3(eyepos), offset, dist));
		}
		else
		{
			pos = WCoord(WorldPos::fromVector3(eyepos + offset));
		}
	}

	return pos;
}

void PlayerControl::setCameraConfigPosition()
{
	WCoord pos = m_pCamera->getEngineCamera()->GetPosition();

	// 摄像机跟随模式下，自定义相机位置保存的是人和摄像机的位置偏移量
	if (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_CAMERA_MOTION) == CAR_FOLLOWED_CAMERA)
	{
		Rainbow::Quaternionf quat;
		Rainbow::Vector3f offpos_body, eyepos = getBody()->getBindPointPos(0) + Rainbow::Vector3f(0, getEyeHeight() + 10.0f, 0);

		//quat.setEulerAngle(getLocoMotion()->m_RotateYaw+180.0f, getLocoMotion()->m_RotationPitch, 0);
		if (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_CONFIG_SET) == CCG_FLATVIEW)
		{
			//quat.setEulerAngle(m_CurrentCameraConfig.InitPlayerYaw + 180.0f, 0, 0);
			quat = AngleEulerToQuaternionf(Vector3f(0, m_CurrentCameraConfig.InitPlayerYaw + 180.0f, 0));
		}
		else
		{
			//quat.setEulerAngle(m_LocoMotion->m_RotateYaw + 180.0f, 0, 0);
			quat = AngleEulerToQuaternionf(Vector3f(0, getLocoMotion()->m_RotateYaw + 180.0f, 0));
		}
		quat = quat.Inverse();
		//quat.rotate(offpos_body, pos.toVector3() - eyepos);
		offpos_body = RotateVectorByQuat(quat, pos.toVector3() - eyepos);

		pos = offpos_body;
	}

	m_CurrentCameraConfig.setCameraPosition(pos);
}

Rainbow::Vector3f PlayerControl::getCameraConfigLookDir()
{
	Rainbow::Vector3f dir = m_CurrentCameraConfig.getCameraLookDir();

	if (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_CAMERA_MOTION) == CAR_FOLLOWED_CAMERA)
	{
		if (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_CONFIG_SET) == CCG_FLATVIEW)
		{
			dir += Rainbow::Vector3f(m_CurrentCameraConfig.InitPlayerYaw + 180.0f, m_CurrentCameraConfig.InitPlayerPitch, 0.0f);
		}
		else
		{
			dir += Rainbow::Vector3f(getLocoMotion()->m_RotateYaw + 180.0f, getLocoMotion()->m_RotationPitch, 0.0f);
		}
	}
	dir.x = dir.x - int(dir.x / 360.0f) * 360.0f;

	return dir;
}

void PlayerControl::setCameraConfigLookDir()
{
	Rainbow::Vector3f dir(m_pCamera->m_RotateYaw, m_pCamera->m_RotatePitch, 0.0f);

	// 摄像机跟人都转动的情况下，自定义相机的朝向保存的是人和摄像机的转动偏移量
	if (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_CAMERA_MOTION) == CAR_FOLLOWED_CAMERA)
	{
		if (m_CurrentCameraConfig.getOption(CAMERA_OPTION_INDEX_CONFIG_SET) == CCG_FLATVIEW)
		{
			dir -= Rainbow::Vector3f(m_CurrentCameraConfig.InitPlayerYaw + 180.0f, m_CurrentCameraConfig.InitPlayerPitch, 0.0f);
		}
		else
		{
			dir -= Rainbow::Vector3f(getLocoMotion()->m_RotateYaw + 180.0f, getLocoMotion()->m_RotationPitch, 0.0f);
		}
	}
	dir.x = dir.x - int(dir.x / 360.0f) * 360.0f;

	m_CurrentCameraConfig.setCameraLookDir(dir);
}

void PlayerControl::setCameraConfigPositionEx(int x, int y, int z)
{
	WCoord pos(x, y, z);
	m_CurrentCameraConfig.setCameraPosition(pos);
}

void PlayerControl::setCameraConfigLookDirEx(int Yaw, int Pitch)
{
	Rainbow::Vector3f dir(m_pCamera->m_RotateYaw, m_pCamera->m_RotatePitch, 0.0f);
	dir.x = dir.x - int(dir.x / 360.0f) * 360.0f;
	m_CurrentCameraConfig.setCameraLookDir(dir);
}

void PlayerControl::setCameraFov(float fov)
{
	//教育版相机视角伸缩
	m_pCamera->setFov(fov);
	if (m_pCamera->getEngineCamera())
	{
		m_pCamera->getEngineCamera()->SetVerticalFieldOfView(fov);
	}
}


void PlayerControl::showUI(bool b)
{
	if (!GetClientInfoProxy()->isMobile())
	{
		bool sightmode = !b;
		if (m_PCCtrl->isSightMode() != sightmode)
		{
			m_PCCtrl->setSightModel(sightmode);
			if (GetIClientGameManagerInterface()->getICurGame()->isInGame())
			{
				GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->ResetOperateState();
			}
		}
	}
}

void PlayerControl::lerpRotationTo(float targetYaw, float duration)
{
	m_LerpRotation = true;
	m_LerpRotationDuration = duration;
	m_TargetYaw = targetYaw;
	m_OriginYaw = getLocoMotion()->m_RotateYaw;
	m_LerpRotationStartMarker = 0;
}

void PlayerControl::OnPlayerEyeEnterWater()
{
	m_PlayerSounder = GetMusicManager().PlaySound2DControl("sounds/env/underwater.ogg", 1.0, true);
}

void PlayerControl::OnPlayerEyeOutOfWater()
{
	OGRE_RELEASE(m_PlayerSounder);
}

bool PlayerControl::canShowCameraModel()
{
	if (!canShowCameraModelExUGC())
		return false;

	int viewMode = m_ViewMode;
	if (isInSpectatorMode() && getSpectatorType() == SPECTATOR_TYPE_FOLLW)
	{
		viewMode = m_ViewMode_ToSpectator;
	}
	// 新沙盒节点地图强制隐藏
	if (GetWorldManagerPtr()->isNewSandboxNodeGame())
	{
		return false;
	}
	if (GetAdventureGuideMgrProxy()->isHideHand())
	{
		return false;
	}
	//是否直接隐藏首部模型
	if (IsHideHandModel())
		return false;

	//隐藏UI的时候不要隐藏手臂
	if (getShowHandState())
		return !isRestInBed() && !isSleeping() && m_CameraModel && m_CameraModel->getMoveDirModeView() == viewMode && !getUsingEmitter();
	else
		return !isOffLine() && !GetGameUIPtr()->isUIHide() && !isSleeping() && !isRestInBed() && m_CameraModel && m_CameraModel->getMoveDirModeView() == viewMode && !getUsingEmitter();
		//return !GetGameUIPtr()->isUIHide() && !isSleeping() && !isRestInBed() && m_CameraModel && m_CameraModel->getMoveDirModeView() == viewMode && !getUsingEmitter();
}

float PlayerControl::getOverLookAngleToScreen()
{
	if (m_pCamera)
		return m_pCamera->m_RotateYaw;

	return 0;
}

void PlayerControl::getPointToScreen(float& x, float& y, float& z, ClientActor* actor, int offset/* =0 */)
{
	WCoord pos;

	if (actor) //判断空指针
	{
		pos = actor->getPosition() + WCoord(0, actor->getLocoMotion()->m_BoundHeight + offset, 0);
	}
	else
	{
		pos = getPosition() + WCoord(0, getLocoMotion()->m_BoundHeight + offset, 0);
	}

	Rainbow::Vector3f viewpos = m_pCamera->getEngineCamera()->WorldToViewportPoint(pos.toVector3());
	x = viewpos.x * WIN_DEF_SCRW;
	y = viewpos.y * WIN_DEF_SCRH;
	z = viewpos.z;
}

void PlayerControl::getPointToScreen(float& x, float& y, int posx, int posy, int posz)
{
	Rainbow::UILib::FrameManager* frameManager = GetGameUIPtr()->GetFrameManagerPtr();
	const int screenWidth = frameManager->GetScreenWidth();
	const int screenHeight = frameManager->GetScreenHeight();

	Rainbow::Vector3f viewpos = m_pCamera->getEngineCamera()->WorldToViewportPoint(WCoord(posx, posy, posz).toVector3());
	x = viewpos.x * frameManager->GetScreenWidth() / frameManager->GetScreenScale();
	y = viewpos.y * frameManager->GetScreenHeight() / frameManager->GetScreenScale();
}

float PlayerControl::getAngleToScreen(int posx, int posy, int posz)
{
	float yaw;
	auto dir = (getPosition() - WCoord(posx, posy, posz)).toVector3();
	dir = MINIW::Normalize(dir);
	Direction2PitchYaw(&yaw, 0, dir);

	return (WrapAngleTo180(yaw) - WrapAngleTo180(getLocoMotion()->m_RotateYaw)) + 180;
}

void PlayerControl::setCameraRotate(int y, int posx, int posy, int posz)
{
	float yaw;
	auto dir = (getPosition() - WCoord(posx, posy, posz)).toVector3();
	dir = MINIW::Normalize(dir);
	Direction2PitchYaw(&yaw, 0, dir);

	m_pCamera->setRotate(yaw, (float)y);
}

void PlayerControl::updateGameCamera(float dtime)
{
	if (m_pCamera == NULL) return;
	if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		if (GAMERECORD_INTERFACE_EXEC(isEdit(), false) && GAMERECORD_INTERFACE_EXEC(isPause(), false))
		{
			return;
		}

		Rainbow::Vector3f pos;
		float yaw;
		float pitch;
		if (GAMERECORD_INTERFACE_EXEC(getRecordCameraPos(pos, yaw, pitch), false))
		{
			return;
		}

		m_pCamera->m_RotateYaw = getLocoMotion()->m_RotateYaw + 180;
		m_pCamera->m_RotatePitch = getLocoMotion()->m_RotationPitch;
	}

	//if (updateGameCameraExUGC(dtime))
	//	return;

	//特用于过场模式或冒险强制新手引导控制转向
	if ((!m_EnableInput || !GetAdventureGuideMgrProxy()->canPlayerTurn()) && m_LerpRotation)
	{
		float deltaYaw = dtime / m_LerpRotationDuration * (m_TargetYaw - m_OriginYaw) / 180;
		LOG_INFO("deltaYaw %f", deltaYaw);
		getCamera()->rotate(deltaYaw, 0);

		m_LerpRotationStartMarker += dtime;

		if (m_LerpRotationStartMarker > m_LerpRotationDuration)
		{
			m_LerpRotationStartMarker = 0;
			m_LerpRotation = false;
		}
	}

	if ((isSleeping() || isRestInBed() || isUseHearth()) && m_ViewMode == 0)
	{
		Rainbow::Camera* pcamera = m_pCamera->getEngineCamera();
		WCoord eyepos;
		Rainbow::Vector3f dir;
		if (isUseHearth())
		{
			HearthBlockMaterial::getEyePosInHearth(m_pWorld, getUsingHearthPos(), eyepos, dir);
		}
		else
		{
			WCoord tmpPos = getPosition();
			BedLogicHandle::getEyePosInBed(m_pWorld, tmpPos, eyepos, dir);
			setPosition(tmpPos);
		}
		if (pcamera) {
			Vector3f eyeWorldPos = eyepos.toWorldPos().toVector3();
			Vector3f lookPos = eyeWorldPos + dir * 1.f;
			pcamera->LookAt(eyeWorldPos, lookPos, Rainbow::Vector3f::yAxis);
			m_pCamera->m_RotateYaw = getLocoMotion()->m_RotateYaw;
			m_pCamera->m_RotatePitch = getLocoMotion()->m_RotationPitch;
		}
	}
	else
	{
		bool isCanMove = true;
		auto temperatureComponent = getTemperatureComponent();
		if (temperatureComponent)
		{
			isCanMove = temperatureComponent->PlayerCameraCanMove();
		}

		//Rainbow::Vector3f lookdir(m_pCamera->getLookDir());
		//lookdir.y = 0;
		//lookdir = MINIW::Normalize(lookdir);
		//lookdir = lookdir * 0.0f;
		 
		//Rainbow::Vector3f eyepos = getBody()->getBindPointPos(0) + Rainbow::Vector3f(lookdir.x, getEyeHeight() + 10.0f, lookdir.z);
		Rainbow::Vector3f offset(0, getEyeHeight() + 10.0f, 0);
		Rainbow::Vector3f eyepos = getBody()->getBindPointPos(0, &offset);

		if (!isSleeping() && !isUseHearth() && isCanMove)
		{
			m_pCamera->setPosition(WorldPos::fromVector3(eyepos));
		}


		if (g_pPlayerCtrl && g_pPlayerCtrl->isInSpectatorMode() && g_pPlayerCtrl->getSpectatorType() == SPECTATOR_TYPE_FOLLW)
		{
			/*float stepx = 5*0.75*0.75*3*180.0f/WIN_DEF_SCRW;
			float stepy = 5*0.75*0.75*3*90.0f/WIN_DEF_SCRH;
			float diff = getLocoMotion()->m_RotateYaw - m_pCamera->m_RotateYaw;
			if(abs(diff) < stepx)
			{
				m_pCamera->m_RotateYaw = getLocoMotion()->m_RotateYaw;
			}
			else if(diff > 0)
			{
				m_pCamera->m_RotateYaw += stepx;
			}
			else
			{
				m_pCamera->m_RotateYaw -= stepx;
			}

			diff = getLocoMotion()->m_RotationPitch - m_pCamera->m_RotatePitch;
			if(abs(diff) < stepy)
			{
				m_pCamera->m_RotatePitch = getLocoMotion()->m_RotationPitch;
			}
			else if(diff > 0)
			{
				m_pCamera->m_RotatePitch += stepy;
			}
			else
			{
				m_pCamera->m_RotatePitch -= stepy;
			}*/
			m_pCamera->m_RotateYaw = getLocoMotion()->m_RotateYaw;
			m_pCamera->m_RotatePitch = getLocoMotion()->m_RotationPitch;
		}
		//m_pCamera->update(dtime, m_pWorld);
	}
}

Rainbow::Vector3f PlayerControl::getLookDir()
{
	return getLocoMotion()->getLookDir();
}

bool PlayerControl::isSightMode()
{
	if (GetClientInfoProxy()->isMobile())
	{
		if (m_TouchCtrl == NULL) return false;
		return m_TouchCtrl->isSightMode();
	}
	else
	{
		if (m_PCCtrl == NULL) return false;
		return m_PCCtrl->isSightMode();
	}
}

void PlayerControl::setSightMode(bool b, bool isIgnoreMobile/*=true*/)
{
	if (GetClientInfoProxy()->isMobile() && !isIgnoreMobile)  //老的行为只是改变PC端的SightMode, 需要兼容老的行为, 否则移动端会各种准心模式问题
	{
		if (m_TouchCtrl == NULL) return;
		if (m_TouchCtrl)
			m_TouchCtrl->setSightModel(b);
	}
	else
	{
		if (m_PCCtrl == NULL) return;
		if (m_PCCtrl)
			m_PCCtrl->setSightModel(b);
	}

	auto curgame = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame);
	if (curgame && curgame->isInGame())
		//if (stricmp(GetIClientGameManagerInterface()->getICurGame()->getTypeName(), "SurviveGame") == 0)
	{
		curgame->ResetOperateState();
	}
}

void PlayerControl::changeGameraZoomInOut(bool isridingrocket)
{
	if (isridingrocket)
	{
		if (getViewMode() != CAMERA_TPS_OVERLOOK)
		{
			if (!getGunLogical()->getZoom())
			{
				m_pCamera->setZoomInOut(m_pCamera->getFov() + 13, 3, 2);
			}
		}
	}
	else
	{
		if (!getGunLogical()->getZoom())
		{
			m_pCamera->disableZoom();
		}
	}
}