
#include <stdio.h>
#include "UGCBluePrint.h"
#include "WorldManager.h"
#include "UGCBluePrint_generated.h"
#include "BlockMaterialMgr.h"
#include "ClientActor.h"
#include "OgreWCoord.h"
#include "OgreUtils.h"
#include "BlockDefCsv.h"
#include "genCustomModel.h"
#include "StringDefCsv.h"
#include "container_buildblueprint.h"
#include "container_peristele.h"
#include "container_railknot.h"
#include "PlayerControl.h"
#include "world_types.h"
#include "SceneEditorRotateTool.h"
#include "File/FileManager.h"
#include "ClientActorHelper.h"
#include "ClientAccount.h"
#include "OgreStringUtil.h"
#include "EffectManager.h"
#include "ActorBody.h"
#include "OgreEntity.h"
#include <fcntl.h>
#include "SandboxIdDef.h"
#include "ModPackMgr.h"
#if PLATFORM_WIN
#include <Windows.h>
#else
#define O_BINARY (0)
#include <unistd.h>
#endif

using namespace MINIW;
using namespace Rainbow;

// 后续版本需要扩展时打开
 #define USER_CONTAINER_DATA

static const char* OFFICIAL_PATH = "data/ugcres/vbp/official";
static const char* RES_PATH = "data/ugcres/vbp/res/%d";

extern WorldContainer* CreateWorldContainerFromChunkContainer(const FBSave::ChunkContainer* pChunkContainer);

static bool UGCIgnoreOptBlock(int id, int bitFlag)
{
	auto def = BlockDefCsv::getInstance()->get(id);
	if (def && def->BluePrintFlag & bitFlag)
		return true;

	return false;
}

static int UGCConvertConsumeBlock(int id)
{
	auto def = BlockDefCsv::getInstance()->get(id);
	if (def && def->ConvertID > 0)
		return def->ConvertID;

	return id;
}


UGCBluePrint::UGCBluePrint()
{
	m_Start = WCoord(0, 0, 0);
	m_Dim = WCoord(1, 1, 1);
	m_sAuthName = "";
	m_sKey = "";
	m_sName = "";
	m_iType = UGCBluePrintType::OfficialBluePrint;
	m_loader = false;
	m_ActorBody = nullptr;
	m_texture = nullptr;
}

UGCBluePrint::UGCBluePrint(WCoord start, WCoord dim)
{
	SetAreaInfo(start, dim);
	m_sAuthName = "";
	m_sKey = "";
	m_sName = "";
	m_iType = UGCBluePrintType::CustomBluePrint;
	m_loader = false;
	m_ActorBody = nullptr;
	m_texture = nullptr;
}


UGCBluePrint::~UGCBluePrint()
{
	ENG_DELETE(m_ActorBody);
	m_texture = nullptr;
}

std::string UGCBluePrint::GetBluePrintDir(long long owid, UGCBluePrintType type)
{
	if ( type == UGCBluePrintType::OfficialBluePrint)
	{
		return OFFICIAL_PATH;
	}
	else if ( type == UGCBluePrintType::CustomBluePrint )
	{
		char dir[256];
		snprintf(dir, sizeof(dir) - 1, "data/w%lld/vbp", owid);
		return dir;
	}
	else if ( type == UGCBluePrintType::ResBluePrint && g_pPlayerCtrl != nullptr )
	{
		char dir[256];
		snprintf(dir, sizeof(dir) - 1, RES_PATH, g_pPlayerCtrl->getUin());
		return dir;
	}

	return "";
}

std::string UGCBluePrint::GetPackageDir(std::string fileName)
{
	std::string dir = fileName;
	const char* pFind = strrchr(fileName.c_str(), '.');
	if (pFind != nullptr)
	{
		std::string fileSuffix = pFind;
		if (fileSuffix == UGCBluePrint_File_Suffix)
		{
			dir = std::string(fileName.c_str(), pFind);
		}		  
	}

	return dir;
}

std::string UGCBluePrint::GetFullFilePath(long long owid, const std::string& filename, UGCBluePrintType type)
{
	std::string packageDir = UGCBluePrint::GetPackageDir(filename);

	char path[256] = { 0 };
	snprintf(path, sizeof(path), "%s/%s/%s%s", GetBluePrintDir(owid, type).c_str(), packageDir.c_str(), filename.c_str(), UGCBluePrint_File_Suffix);
	return path;
}

void UGCBluePrint::MakeFullFilePath(long long owid, const std::string& filename, UGCBluePrintType type)
{
	char path[256] = { 0 };
	std::string packageDir = UGCBluePrint::GetPackageDir(filename);

	switch (type)
	{
	case OfficialBluePrint:
		snprintf(path, sizeof(path)-1, "%s/%s/", OFFICIAL_PATH, packageDir.c_str());
		break;
	case CustomBluePrint:
		snprintf(path, sizeof(path), "data/w%lld/vbp/%s/", owid, packageDir.c_str());
		break;
	case ResBluePrint:
		if ( g_pPlayerCtrl != nullptr )
		{
			char tmp[256] = { 0 };
			snprintf(tmp, sizeof(tmp) - 1, RES_PATH, g_pPlayerCtrl->getUin());
			snprintf(path, sizeof(path) - 1, "%s/%s/", tmp, packageDir.c_str());
		}
		break;
	default:
		return;
	}

	if (!GetFileManager().IsFileExistWritePath(path))
	{
		GetFileManager().CreateWritePathDir(path);
	}
}

//不能加载云服的蓝图
bool UGCBluePrint::Load(long long owid, const std::string& filename, UGCBluePrintType type)
{
	char path[256] = { 0 };
	std::string packageDir = UGCBluePrint::GetPackageDir(filename);
	snprintf(path, sizeof(path)-1, "%s/%s/%s%s", GetBluePrintDir(owid, type).c_str(), packageDir.c_str(), filename.c_str(), UGCBluePrint_File_Suffix);

	int buflen = 0;
	void *buf = ReadWholeFile(path, buflen);
	if (buf == NULL) return false;

	m_sKey = filename;
	m_loader = LoadData(buf, buflen);
	free(buf);
	return m_loader;
}

bool UGCBluePrint::Load(const std::string& path)
{
	int buflen = 0;
	void* buf = ReadWholeFile(path.c_str(), buflen);
	if (buf == NULL)
	{
		LOG_INFO("FindOrLoadOfficial::Load buf == NULL");
		return false;
}

	m_loader = LoadData(buf, buflen);
	free(buf);
	return m_loader;
}

bool UGCBluePrint::LoadByStdioPath(const std::string& path)
{
	int datalen = 0;
	FileAutoClose fp(path, O_RDONLY | O_BINARY);
	if (fp.isNull())
		return false;


	datalen = fp.fileSize();
	if (datalen == 0) return false;

	void* buf = malloc(datalen);
	if (buf == NULL) return false;

	fp.seek(0, SEEK_SET);
	if (!fp.read(buf, datalen))
	{
		free(buf);
		return false;
	}

	if (buf == NULL)
	{
		LOG_INFO("FindOrLoadOfficial::Load buf == NULL");
		return false;
	}

	m_loader = LoadData(buf, datalen);
	free(buf);
	return m_loader;
}

bool UGCBluePrint::LoadData(void *buf, int buflen)
{
	//flatbuffers::Verifier verifier((const uint8_t *)buf, buflen);
	//if (!FBSave::VerifyUGCBluePrintBuffer(verifier))
	//{
	//	LOG_INFO("FindOrLoadOfficial::LoadData1");
	//	return false;
	//}

	const FBSave::UGCBluePrint* blueprint = FBSave::GetUGCBluePrint(buf);
	if (blueprint == NULL)
	{
		LOG_INFO("FindOrLoadOfficial::LoadData2");
		return false;
	}

	m_iType = blueprint->type();
	m_Materials.clear();

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPBlockData>>> bpblocksoffset0 = 0;
	std::vector<flatbuffers::Offset<FBSave::UGCBPBlockData>> bpblocks0;
	if (blueprint->normalblocks())
	{
		for (int i = 0; i < (int)blueprint->normalblocks()->size(); i++)
		{
			auto src = blueprint->normalblocks()->Get(i);
			bpblocks0.push_back(FBSave::CreateUGCBPBlockData(m_Builder, src->block(), src->blockex(), src->relativepos()));
			Block block(src->block(), src->blockex());
			int blockid = block.getResID();
			if (blockid > 0 && blockid != 4095)
			{
				AddMaterial(blockid);
			}
		}
		bpblocksoffset0 = m_Builder.CreateVector(bpblocks0);
	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPBlockData>>> bpblocksoffset1 = 0;
	std::vector<flatbuffers::Offset<FBSave::UGCBPBlockData>> bpblocks1;
	if (blueprint->otherblocks())
	{
		for (int i = 0; i < (int)blueprint->otherblocks()->size(); i++)
		{
			auto src = blueprint->otherblocks()->Get(i);
			bpblocks1.push_back(FBSave::CreateUGCBPBlockData(m_Builder, src->block(), src->blockex(), src->relativepos()));

			Block block(src->block(), src->blockex());
			int blockid = block.getResID();
			if (blockid > 0 && blockid != 4095)
			{
				AddMaterial(blockid);
			}
		}
		bpblocksoffset1 = m_Builder.CreateVector(bpblocks1);
	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPChunkContainer>>> containersoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::UGCBPChunkContainer>> containers;
	if (blueprint->containers())
	{
#ifdef USER_CONTAINER_DATA
		containers.reserve(blueprint->containers()->size());

		for (size_t i = 0; i < blueprint->containers()->size(); i++)
		{

			auto src = blueprint->containers()->Get(i);
			WorldContainer *obj = CreateWorldContainerFromChunkContainer(src->container());
			if (obj)
			{
				obj->load(src->container()->container());
				containers.push_back(FBSave::CreateUGCBPChunkContainer(m_Builder, obj->save(m_Builder), src->relativepos()));
				AddMaterial(obj);
				ENG_DELETE(obj);
			}
		}
#endif // USER_CONTAINER_DATA
	}
	containersoffset = m_Builder.CreateVector(containers);

	if (blueprint->authorname())
		m_sAuthName = blueprint->authorname()->c_str();

	m_iAuthUin = blueprint->authuin();
	//if (blueprint->start())
	//	m_Start = Coord3ToWCoord(blueprint->start());

	if (blueprint->dim())
		m_Dim = Coord3ToWCoord(blueprint->dim());

	if (blueprint->skey())
		m_sKey = blueprint->skey()->c_str();

	if (blueprint->bpname())
		m_sName = blueprint->bpname()->c_str();

	auto dim = WCoordToCoord3(m_Dim);
	auto bp = CreateUGCBluePrint(m_Builder, m_iType, m_iAuthUin, m_iWid, &dim, m_Builder.CreateString(m_sKey),
		m_Builder.CreateString(m_sAuthName), m_Builder.CreateString(m_sName), bpblocksoffset0, bpblocksoffset1, containersoffset);
	m_Builder.Finish(bp);
	LOG_INFO("FindOrLoadOfficial::LoadData3");
	return true;
}

bool UGCBluePrint::Save(long long owid, const std::string& filename, UGCBluePrintType type, bool rewrite/* =false */)
{
	MakeFullFilePath(owid, filename, type);

	char path[256] = { 0 };
	std::string packageDir = UGCBluePrint::GetPackageDir(filename);
	snprintf(path, sizeof(path) - 1, "%s/%s/%s%s", GetBluePrintDir(owid, type).c_str(), packageDir.c_str(), filename.c_str(), UGCBluePrint_File_Suffix);
	if (rewrite)
	{
		flatbuffers::FlatBufferBuilder tempBuilder;
		const FBSave::UGCBluePrint *blueprint = FBSave::GetUGCBluePrint(m_Builder.GetBufferPointer());
		if (blueprint == NULL)
		{
			return false;
		}

		m_Materials.clear();
		flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPBlockData>>> bpblocksoffset0 = 0;
		std::vector<flatbuffers::Offset<FBSave::UGCBPBlockData>> bpblocks0;
		if (blueprint->normalblocks())
		{
			// normal block 记录在voxel头部
			for (int i = 0; i < (int)blueprint->normalblocks()->size(); i++)
			{
				auto src = blueprint->normalblocks()->Get(i);
				bpblocks0.push_back(FBSave::CreateUGCBPBlockData(tempBuilder, src->block(), src->blockex(), src->relativepos()));
				Block block(src->block(), src->blockex());
				int blockid = block.getResID();
				if (blockid > 0 && blockid != 4095)
				{
					AddMaterial(blockid);
				}
			}

			bpblocksoffset0 = tempBuilder.CreateVector(bpblocks0);
		}


		flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPBlockData>>> bpblocksoffset1 = 0;
		std::vector<flatbuffers::Offset<FBSave::UGCBPBlockData>> bpblocks1;
		if (blueprint->otherblocks())
		{
			for (int i = 0; i < (int)blueprint->otherblocks()->size(); i++)
			{
				auto src = blueprint->otherblocks()->Get(i);
				bpblocks1.push_back(FBSave::CreateUGCBPBlockData(tempBuilder, src->block(), src->blockex(), src->relativepos()));

				Block block(src->block(), src->blockex());
				int blockid = block.getResID();
				if (blockid > 0 && blockid != 4095)
				{
					AddMaterial(blockid);
				}
			}
			bpblocksoffset1 = tempBuilder.CreateVector(bpblocks1);
		}

		flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPChunkContainer>>> containersoffset = 0;
		std::vector<flatbuffers::Offset<FBSave::UGCBPChunkContainer>> containers;
		if (blueprint->containers())
		{
#ifdef USER_CONTAINER_DATA
			containers.reserve(blueprint->containers()->size());

			for (size_t i = 0; i < blueprint->containers()->size(); i++)
			{

				auto src = blueprint->containers()->Get(i);
				WorldContainer *obj = CreateWorldContainerFromChunkContainer(src->container());
				if (obj)
				{
					obj->load(src->container()->container());
					containers.push_back(FBSave::CreateUGCBPChunkContainer(tempBuilder, obj->save(tempBuilder), src->relativepos()));
					AddMaterial(obj);
					ENG_DELETE(obj);
				}
			}
#endif
		}
		containersoffset = tempBuilder.CreateVector(containers);

		auto dim = WCoordToCoord3(m_Dim);
		auto bp = CreateUGCBluePrint(tempBuilder, m_iType, m_iAuthUin, m_iWid, &dim, tempBuilder.CreateString(m_sKey),
			tempBuilder.CreateString(m_sAuthName), tempBuilder.CreateString(m_sName), bpblocksoffset0, bpblocksoffset1, containersoffset);
		tempBuilder.Finish(bp);
		return GetFileManager().SaveToWritePath(path, tempBuilder.GetBufferPointer(), tempBuilder.GetSize());
	}
	else
	{
		return GetFileManager().SaveToWritePath(path, m_Builder.GetBufferPointer(), m_Builder.GetSize());
	}
}

bool UGCBluePrint::Save2Vbp(VoxelModel* pModel, const std::string& sFileName, const std::string& bluePrintName)
{
	if (!pModel || sFileName.empty())
		return false;

	UGCBluePrintType outType = UGCBluePrintType::CustomBluePrint;
	long long owid = 0;
	if (g_pPlayerCtrl)
	{
		owid = g_pPlayerCtrl->getOWID();
	}

	MakeFullFilePath(owid, sFileName, outType);

	char path[256] = { 0 };
	std::string packageDir = UGCBluePrint::GetPackageDir(sFileName);
	snprintf(path, sizeof(path) - 1, "%s/%s/%s%s", GetBluePrintDir(owid, outType).c_str(), packageDir.c_str(), sFileName.c_str(), UGCBluePrint_File_Suffix);

	flatbuffers::FlatBufferBuilder tempBuilder;
	FBSave::Coord3 start(0, 0, 0);
	FBSave::Coord3 dim = WCoordToCoord3(pModel->getDim());
	std::vector<flatbuffers::Offset<FBSave::UGCBPBlockData>> bpblocks;
	int iDimX = dim.x();
	int iDimY = dim.y() < CHUNK_BLOCK_Y ? dim.y() : CHUNK_BLOCK_Y;
	int iDimZ = dim.z();
	for (int y = 0; y < iDimY; y++)
	{
		for (int z = 0; z < iDimZ; z++)
		{
			for (int x = 0; x < iDimX; x++)
			{
				unsigned int data = pModel->getData(x, y, z);
				if (data == 0)
					continue;

				FBSave::Coord3 pos(x, y, z);
				bpblocks.push_back(FBSave::CreateUGCBPBlockData(tempBuilder, Block::toBlockOriginData(data), Block::toBlockDataEx(data), &pos));
			}
		}
	}
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPBlockData>>> bpblocksoffset = tempBuilder.CreateVector(bpblocks);

	UGCBluePrintType type = UGCBluePrintType::OfficialBluePrint;
	SetKey(sFileName);


	auto bp = CreateUGCBluePrint(tempBuilder, type, 1000, 0, &dim, tempBuilder.CreateString(m_sKey),
		tempBuilder.CreateString(GetClientAccountMgr().getAccountName()), tempBuilder.CreateString(bluePrintName), bpblocksoffset, 0, 0);
	tempBuilder.Finish(bp);

	return GetFileManager().SaveToWritePath(path, tempBuilder.GetBufferPointer(), tempBuilder.GetSize());
}

bool UGCBluePrint::SaveObj2Vbp(const char* sObjPath, const std::string& sFileName, std::string& sModelName)
{
	if (!sObjPath || sFileName.empty())
		return false;

	AutoRefPtr<DataStream> fp = GetFileManager().OpenFileWritePath(sObjPath, true);
	if (!fp) return false;

	UGCBluePrintType outType = UGCBluePrintType::CustomBluePrint;
	long long owid = 0;
	if (g_pPlayerCtrl)
	{
		owid = g_pPlayerCtrl->getOWID();
	}

	MakeFullFilePath(owid, sFileName, outType);

	char path[256] = { 0 };
	std::string packageDir = UGCBluePrint::GetPackageDir(sFileName);
	snprintf(path, sizeof(path) - 1, "%s/%s/%s%s", GetBluePrintDir(owid, outType).c_str(), packageDir.c_str(), sFileName.c_str(), UGCBluePrint_File_Suffix);

	flatbuffers::FlatBufferBuilder tempBuilder;
	FBSave::Coord3 start(0, 0, 0);
	std::vector<flatbuffers::Offset<FBSave::UGCBPBlockData>> bpblocks;

	const int iLineSize = 128;
	char current_line[iLineSize];
	std::vector<std::string> vRet;
	int x, y, z, block;
	sModelName = "default";
	WCoord stCood(0, 0, 0);
	while (fp->ReadLine(current_line, iLineSize) > 0)
	{
		std::vector<core::string> vRet = Rainbow::StringUtil::split(current_line, " ");
		if (vRet.size() >= 5)
		{
			if (strcmp("v", vRet[0].c_str()) != 0)
				continue;

			fromString(vRet[4], block);
			if (block <= 0)
				continue;

			fromString(vRet[1], x);
			fromString(vRet[2], z);
			fromString(vRet[3], y);
			if (x > stCood.x)
				stCood.x = x;

			if (y > stCood.y)
				stCood.y = y;

			if (z > stCood.z)
				stCood.z = z;

			FBSave::Coord3 pos(x, y, z);
			bpblocks.push_back(FBSave::CreateUGCBPBlockData(tempBuilder, block, 0, &pos));
		}
		else if(vRet.size() >= 2)
		{
			if (strcmp("n", vRet[0].c_str()) != 0)
				continue;

			sModelName = vRet[1];
		}
	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPBlockData>>> bpblocksoffset = tempBuilder.CreateVector(bpblocks);

	UGCBluePrintType type = UGCBluePrintType::CustomBluePrint;
	SetKey(sFileName);

	FBSave::Coord3 dim = WCoordToCoord3(stCood);
	auto bp = CreateUGCBluePrint(tempBuilder, type, ClientAccountMgr::GetInstance().getUin(), owid, &dim, tempBuilder.CreateString(m_sKey),
		tempBuilder.CreateString(ClientAccountMgr::GetInstancePtr()->getAccountName()), tempBuilder.CreateString(sModelName), bpblocksoffset, 0, 0);
	tempBuilder.Finish(bp);

	return GetFileManager().SaveToWritePath(path, tempBuilder.GetBufferPointer(), tempBuilder.GetSize());
}

bool UGCBluePrint::SetBluePrintData(World *pworld)
{
	std::vector<UGCBlockData>normalBlocks;
	std::vector<UGCBlockData>otherBlocks;
	std::vector<UGCBluePrintContainer>Containers;

	m_Materials.clear();
	m_Builder.Clear();

	bool ret = false;
	for (int y = m_Dim.y; y >= 0; y--)
	{
		for (int x = 0; x <= m_Dim.x; x++)
		{
			for (int z = 0; z <= m_Dim.z; z++)
			{
				UGCBlockData block;
				block.relativepos = WCoord(x, y, z);

				WCoord pos(m_Start.x + x, m_Start.y + y, m_Start.z + z);
				if (pworld->getChunk(pos) == NULL)
					pworld->syncLoadChunk(pos, 1);

				Block srcBlock = pworld->getBlock(pos);
				int blockid = srcBlock.getResID();
				if (blockid == 0 || blockid == 4095)
					continue;

				ret = true;
				if (BlockMaterial::isNormalCube(blockid))
				{
					block.data = srcBlock.getAll();
					normalBlocks.push_back(block);
				}
				else
				{
					block.data = srcBlock.getAll();
					otherBlocks.push_back(block);
				}

				AddMaterial(blockid);

				BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
				if (mtl && mtl->hasContainer())
				{
					WorldContainer *srcContainer = pworld->getContainerMgr()->getContainer(pos);
					if (srcContainer == NULL) continue;

					UGCBluePrintContainer container;
					container.relativepos = WCoord(x, y, z);
					container.container = srcContainer;
					Containers.push_back(container);

					AddMaterial(srcContainer);
				}
				//是自定义id,我们要记录一些prefabId
				if (blockid > CUSTOM_BLOCKID_MIN && ModPackMgr::GetInstancePtr())
				{
					m_PrefabMap[blockid] = ModPackMgr::GetInstancePtr()->GetResIdByCfgId(CustomModType::Mod_Block, blockid).c_str();
				}
			}
		}
	}
	if (!ret)
		return false;


	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPBlockData>>> bpblocksoffset0 = 0;
	std::vector<flatbuffers::Offset<FBSave::UGCBPBlockData>> bpblocks0;
	auto iter0 = normalBlocks.begin();
	for (; iter0 != normalBlocks.end(); iter0++)
	{
		auto relativePos = WCoordToCoord3(iter0->relativepos);
		bpblocks0.push_back(FBSave::CreateUGCBPBlockData(m_Builder, iter0->data.getOriginData(), iter0->data.getDataEx(), &relativePos));
	}
	bpblocksoffset0 = m_Builder.CreateVector(bpblocks0);

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPBlockData>>> bpblocksoffset1 = 0;
	std::vector<flatbuffers::Offset<FBSave::UGCBPBlockData>> bpblocks1;
	auto iter1 = otherBlocks.begin();
	for (; iter1 != otherBlocks.end(); iter1++)
	{
		auto relativePos = WCoordToCoord3(iter1->relativepos);
		bpblocks1.push_back(FBSave::CreateUGCBPBlockData(m_Builder, iter1->data.getOriginData(), iter1->data.getDataEx(), &relativePos));
	}
	bpblocksoffset1 = m_Builder.CreateVector(bpblocks1);


	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPChunkContainer>>> containersoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::UGCBPChunkContainer>> containers;

#ifdef USER_CONTAINER_DATA
	auto iter2 = Containers.begin();
	for (; iter2 != Containers.end(); iter2++)
	{
		auto relativePos = WCoordToCoord3(iter2->relativepos);
		flatbuffers::Offset<FBSave::ChunkContainer> container;

		if (!iter2->container->getNeedClear())
		{
			container = iter2->container->save(m_Builder);
			containers.push_back(FBSave::CreateUGCBPChunkContainer(m_Builder, container, &relativePos));
		}
	}
#endif
	containersoffset = m_Builder.CreateVector(containers);

	//保存id映射表
	flatbuffers::Offset < flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPPrefabMapInfo>>> prefabOffset = 0;
	{
		std::vector<flatbuffers::Offset<FBSave::UGCBPPrefabMapInfo>> prefabArr;
		prefabArr.reserve(m_PrefabMap.size());
		for (const auto& prefab : m_PrefabMap)
		{
			prefabArr.push_back(FBSave::CreateUGCBPPrefabMapInfo(m_Builder, prefab.first, m_Builder.CreateString(prefab.second.c_str())));
		}
		prefabOffset = m_Builder.CreateVector(prefabArr);
	}

	//auto containervec = CreateContainerVec(m_Builder, Containers);
	m_iWid = g_WorldMgr ? g_WorldMgr->getWorldId() : 0;
	auto dim = WCoordToCoord3(m_Dim);
	auto bp = CreateUGCBluePrint(m_Builder, m_iType, m_iAuthUin, m_iWid, &dim, m_Builder.CreateString(m_sKey),
		m_Builder.CreateString(m_sAuthName), m_Builder.CreateString(m_sName), bpblocksoffset0, bpblocksoffset1, containersoffset, prefabOffset);
	m_Builder.Finish(bp);

	m_loader = true;
	return true;
}

bool UGCBluePrint::BlockHasDirrection(int blockId)
{
	BlockDef* pBlockdef = BlockDefCsv::getInstance()->get(blockId);
	if(!pBlockdef)
		return false;

	//颜色方块等没有方向
	if (pBlockdef->PlaceDir == 0)
		return false;
	
	if (pBlockdef->Type == "slab")
		return false;

	return true;
}

void UGCBluePrint::PreBuildBluePrint(WCoord start, std::vector<UGCBlocksData>& preblocks)
{
	const FBSave::UGCBluePrint* blueprint = FBSave::GetUGCBluePrint(m_Builder.GetBufferPointer());
	if (blueprint == NULL)
		return;


	auto normalblocks = blueprint->normalblocks();
	int normalSize = normalblocks ? normalblocks->size() : 0;
	for (int i = 0; i < normalSize; i++)
	{
		auto src = normalblocks->Get(i);
		Block block(src->block(), src->blockex());

		WCoord relativePos = Coord3ToWCoord(src->relativepos());
		WCoord srcRelativePos = relativePos;
		WCoord pos(start.x + relativePos.x, start.y + relativePos.y, start.z + relativePos.z);


		UGCBlocksData data;
		data.pos = pos.toVector3();
		data.blockdata = block.getAll();

#ifdef USER_CONTAINER_DATA

		if (block.getResID() == BLOCK_PERISTELE || block.getResID() == BLOCK_TOP_PERISTELE)
		{
			for (size_t i = 0; i < blueprint->containers()->size(); i++)
			{
				auto con = blueprint->containers()->Get(i);
				if (con && Coord3ToWCoord(con->relativepos()) == srcRelativePos)
				{
					WorldContainer* obj = CreateWorldContainerFromChunkContainer(con->container());
					if (obj)
					{
						obj->load(con->container()->container());
						/*PeristeleContainer* container = dynamic_cast<PeristeleContainer*>(obj);
						if (container)
						{
							data.specialblockcolor = container->getColor();
						}*/
					}

					break;
				}
			}
		}

#endif


		preblocks.push_back(data);
	}

	auto otherblocks = blueprint->otherblocks();
	int othersize = otherblocks ? otherblocks->size() : 0;
	for (int i = 0; i < othersize; i++)
	{
		auto src = otherblocks->Get(i);
		Block block(src->block(), src->blockex());

		WCoord relativePos = Coord3ToWCoord(src->relativepos());
		WCoord srcRelativePos = relativePos;
		WCoord pos(start.x + relativePos.x, start.y + relativePos.y, start.z + relativePos.z);


		UGCBlocksData data;
		data.pos = pos.toVector3();
		data.blockdata = block.getAll();
#ifdef USER_CONTAINER_DATA

		if (block.getResID() == BLOCK_PERISTELE || block.getResID() == BLOCK_TOP_PERISTELE)
		{
			for (size_t i = 0; i < blueprint->containers()->size(); i++)
			{
				auto con = blueprint->containers()->Get(i);
				if (con && Coord3ToWCoord(con->relativepos()) == srcRelativePos)
				{
					WorldContainer* obj = CreateWorldContainerFromChunkContainer(con->container());
					if (obj)
					{
						obj->load(con->container()->container());
						/*PeristeleContainer* container = dynamic_cast<PeristeleContainer*>(obj);
						if (container)
						{
							data.specialblockcolor = container->getColor();
						}*/
					}
					break;
				}
			}
		}
#endif
		preblocks.push_back(data);
	}
}

WCoord UGCBluePrint::ConvertWcoordByRotate(int rotatetype, WCoord pos)
{
	WCoord temp = pos;
	if (rotatetype == ROTATE_90)
	{
		temp.x = -pos.z;
		temp.z = pos.x;
	}
	else if (rotatetype == ROTATE_180)
	{
		temp.x = -pos.x;
		temp.z = -pos.z;
	}
	else if (rotatetype == ROTATE_270)
	{
		temp.x = pos.z;
		temp.z = -pos.x;
	}
	else if (rotatetype == MIRROR_0)
	{
		temp.x = -pos.x;
	}
	else if (rotatetype == MIRROR_180)
	{
		temp.z = -pos.z;
	}
	else if (rotatetype == MIRROR_270)
	{
		temp.x = -pos.z;
		temp.z = -pos.x;
	}
	else if (rotatetype == MIRROR_90)
	{
		temp.x = pos.z;
		temp.z = pos.x;
	}

	return temp;
}

void UGCBluePrint::PlayBuildSound(World *pworld, int blockid, WCoord pos)
{
	const BlockDef *def = BlockDefCsv::getInstance()->get(blockid);
	if (!def)
		return;

	WCoord centerpos = BlockCenterCoord(pos);

	const char *placesound = def->PlaceSound;
	if (placesound[0] == 0) placesound = def->DigSound;
	if (placesound[0] == 0) placesound = "blockd.grass";

	pworld->getEffectMgr()->playSound(centerpos, placesound, GSOUND_PLACE);
}

ActorBody *UGCBluePrint::GetActorBody()
{
	if (!LoadFinish())
		return nullptr;

	if ( m_ActorBody == nullptr )
	{
		m_ActorBody = CreateActorBody();
		if (m_ActorBody) 
		{
			m_ActorBody->setIsInUI(true);
		}
	}

	return m_ActorBody;
}

void UGCBluePrint::DeleteActorBody()
{
	ENG_DELETE(m_ActorBody);
	m_texture = nullptr;
}

ActorBody* UGCBluePrint::CreateActorBody()
{
	std::vector<WCoord> vpos;
	vpos.clear();
	std::vector<unsigned int> vcolor;
	vcolor.clear();
	GetPreBlocks(vpos, vcolor);

	Rainbow::Model* model = GenCustomModelManager::GetInstance().genItemModel(vpos, vcolor, BLUE_PRINT_MESH, false);
	if (!model)
		return nullptr;

	ActorBody* pActorBody = ENG_NEW(ActorBody)(NULL);
	pActorBody->setPlayerIndex(-1);
	pActorBody->setHeadBoneID(-1);
	Entity* entity = Entity::Create();
	entity->Load(model);
	pActorBody->setEntity(entity);
	return pActorBody;
}

void UGCBluePrint::GetPreBlocks(std::vector<WCoord> &vpos, std::vector<unsigned int> &vcolor)
{
	const FBSave::UGCBluePrint *blueprint = FBSave::GetUGCBluePrint(m_Builder.GetBufferPointer());
	if (blueprint == NULL)
		return;

	auto normalblocks = blueprint->normalblocks();
	int normalSize = normalblocks ? normalblocks->size() : 0;
	for (int i = 0; i < normalSize; i++)
	{
		auto src = normalblocks->Get(i);
		Block block(src->block(), src->blockex());
		WCoord relativePos = Coord3ToWCoord(src->relativepos());

		int blockId = block.getResID();
		if (blockId <= 0)
			continue;

		const BlockDef *def = BlockDefCsv::getInstance()->get(blockId);
		if (!def || def->MiniColor <= 0)
			continue;

		unsigned int color = ((def->MiniColor & 0xff) << 16) | (def->MiniColor & 0xff00) | ((def->MiniColor & 0xff0000) >> 16);
		BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(blockId);
		if (mtl && mtl->isColorableBlock())
		{
			auto blockcolor = mtl->getBlockColor(block.getData());
			color = Rainbow::ColorQuad(blockcolor.r, blockcolor.g, blockcolor.b).c;
		}

		color = (color << 4) | def->CustommodelAlpha;
		color = (color << 4) | def->CustommodelLight;
		vcolor.push_back(color);
		vpos.push_back(relativePos);
	}

	auto otherblocks = blueprint->otherblocks();
	int othersize = otherblocks ? otherblocks->size() : 0;
	for (int i = 0; i < othersize; i++)
	{
		auto src = otherblocks->Get(i);
		Block block(src->block(), src->blockex());
		int blockId = block.getResID();
		if (blockId <= 0)
			continue;

		const BlockDef *def = BlockDefCsv::getInstance()->get(blockId);
		if (!def || def->MiniColor <= 0)
			continue;

		unsigned int color = ((def->MiniColor & 0xff) << 16) | (def->MiniColor & 0xff00) | ((def->MiniColor & 0xff0000) >> 16);
		BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(blockId);
		if (mtl && mtl->isColorableBlock())
		{
			auto blockcolor = mtl->getBlockColor(block.getData());
			color = Rainbow::ColorQuad(blockcolor.r, blockcolor.g, blockcolor.b).c;
		}

		color = (color << 4) | def->CustommodelAlpha;
		color = (color << 4) | def->CustommodelLight;
		vcolor.push_back(color);

		WCoord relativePos = Coord3ToWCoord(src->relativepos());
		vpos.push_back(relativePos);
	}
}

void UGCBluePrint::GetGridUserdataStr(std::string &str)
{
	jsonxx::Object sheetData;

	sheetData << "uin" << m_iAuthUin;
	sheetData << "authorname" << m_sAuthName;
	sheetData << "sheetname" << m_sName;  //图纸名称
	sheetData << "nickname" << "";
	sheetData << "filename" << m_sKey;
	sheetData << "dimx" << m_Dim.x;
	sheetData << "dimy" << m_Dim.y;
	sheetData << "dimz" << m_Dim.z;

	str = sheetData.json();
}

void UGCBluePrint::AddMaterial(int id)
{
	for (size_t i = 0; i < m_Materials.size(); i++)
	{
		if (m_Materials[i].itemid == id)
		{
			m_Materials[i].num++;
			return;
		}
	}

	UGCBluePrintMaterial material;
	material.itemid = id;
	material.num = 1;
	material.durable = -1;
	material.enchantnum = 0;

	m_Materials.push_back(material);
}

void UGCBluePrint::AddMaterial(WorldContainer *container)
{
	int gridNum = container->getGridNum();
	int baseIndex = container->getBaseIndex();

	for (int i = 0; i < gridNum; i++)
	{
		BackPackGrid *grid = container->index2Grid(baseIndex + i);
		if (grid == NULL || grid->isEmpty())
			continue;

		bool hasMaterial = false;
		for (size_t j = 0; j < m_Materials.size(); j++)
		{
			if (m_Materials[j].itemid != grid->getItemID())
				continue;

			//if(m_Materials[j].durable >= 0 && m_Materials[j].durable != grid->getDuration())
			if (m_Materials[j].durable >= 0)
				continue;
			//if(!isSameEnchant(grid, &m_Materials[j]))
			if (m_Materials[j].enchantnum > 0)
				continue;

			m_Materials[j].num += grid->getNum();
			hasMaterial = true;
			break;
		}

		if (!hasMaterial)
		{
			UGCBluePrintMaterial material;
			material.itemid = grid->getItemID();
			material.num = grid->getNum();
			material.durable = grid->getDuration();
			material.enchantnum = grid->getNumEnchant();
			memset(material.enchants, 0, sizeof(material.enchants));
			memcpy(material.enchants, grid->getEnchants(), sizeof(int)*material.enchantnum);
			material.runedata = grid->getRuneData();
			m_Materials.push_back(material);
		}
	}
}

int UGCBluePrint::GetMaterialsNum()
{
	return (int)m_Materials.size();
}

UGCBluePrintMaterial *UGCBluePrint::GetMaterialInfo(int index)
{
	if (index >= 0 && index < GetMaterialsNum())
	{
		return &m_Materials[index];
	}

	return NULL;
}

void UGCBluePrint::SetDescInfo(const WCoord &start, const WCoord &dim, const std::string& authname, int authuin, const std::string& skey, UGCBluePrintType type)
{
	SetAreaInfo(start, dim);
	if (type ==  UGCBluePrintType::OfficialBluePrint)
	{
		m_sAuthName = StringDefCsv::getInstance()->get(3522);
		m_iAuthUin = 1000;
	}
	else
	{
		m_sAuthName = authname;
		m_iAuthUin = authuin;
	}

	m_sKey = skey;
	m_iType = type;
}

//保证start是立方体区域左下角的方块坐标，dim是立方体区域右上角的方块坐标减去start的向量
void UGCBluePrint::SetAreaInfo(const WCoord &start, const WCoord &dim)
{
	m_Start = start;
	m_Dim = dim;
}

void UGCBluePrint::Save4Insider(World* pworld, const std::string& filename, const WCoord& startPos, const WCoord& dim)
{
	std::vector<UGCBlockData> normalBlocks;
	std::vector<UGCBlockData> otherBlocks;
	std::vector<UGCBluePrintContainer> containerVec;

	bool ret = false;
	for (int y = dim.y; y >= 0; y--)
	{
		for (int x = 0; x <= dim.x; x++)
		{
			for (int z = 0; z <= dim.z; z++)
			{
				UGCBlockData block;
				block.relativepos = WCoord(x, y, z);

				WCoord pos(startPos.x + x, startPos.y + y, startPos.z + z);
				if (pworld->getChunk(pos) == NULL)
					pworld->syncLoadChunk(pos, 1);

				Block srcBlock = pworld->getBlock(pos);
				int blockid = srcBlock.getResID();
				if (blockid == 0 || blockid == 4095)
					continue;

				ret = true;
				if (BlockMaterial::isNormalCube(blockid))
				{
					block.data = srcBlock.getAll();
					normalBlocks.push_back(block);
				}
				else
				{
					block.data = srcBlock.getAll();
					otherBlocks.push_back(block);
				}
				//是自定义id,我们要记录一些prefabId
				if (blockid > CUSTOM_BLOCKID_MIN && ModPackMgr::GetInstancePtr())
				{
					m_PrefabMap[blockid] = ModPackMgr::GetInstancePtr()->GetResIdByCfgId(CustomModType::Mod_Block, blockid).c_str();
				}
				BlockMaterial* blockMat = g_BlockMtlMgr.getMaterial(srcBlock.getResID());
				bool hasContainer = blockMat == NULL ? false : blockMat->hasContainer();
				if (!hasContainer)
					continue;
				
				WorldContainer* srcContainer = pworld->getContainerMgr()->getContainer(pos);
				if (srcContainer == NULL) continue;

				UGCBluePrintContainer container;
				container.relativepos = WCoord(x, y, z);
				container.container = srcContainer;
				containerVec.push_back(container);
			}
		}
	}
	if (!ret)
		return;
	flatbuffers::FlatBufferBuilder tempBuilder;

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPBlockData>>> bpblocksoffset0 = 0;
	std::vector<flatbuffers::Offset<FBSave::UGCBPBlockData>> bpblocks0;
	
	auto normalIter = normalBlocks.begin();
	for (; normalIter != normalBlocks.end(); normalIter++)
	{
		auto relativePos = WCoordToCoord3(normalIter->relativepos);
		bpblocks0.push_back(FBSave::CreateUGCBPBlockData(tempBuilder, normalIter->data.getOriginData(), normalIter->data.getDataEx(), &relativePos));
	}

	bpblocksoffset0 = tempBuilder.CreateVector(bpblocks0);

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPBlockData>>> bpblocksoffset1 = 0;
	std::vector<flatbuffers::Offset<FBSave::UGCBPBlockData>> bpblocks1;

	auto otherIter = otherBlocks.begin();
	for (; otherIter != otherBlocks.end(); otherIter++)
	{
		auto relativePos = WCoordToCoord3(otherIter->relativepos);
		bpblocks1.push_back(FBSave::CreateUGCBPBlockData(tempBuilder, otherIter->data.getOriginData(), otherIter->data.getDataEx(), &relativePos));
	}

	bpblocksoffset1 = tempBuilder.CreateVector(bpblocks1);


	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPChunkContainer>>> bpcontainersoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::UGCBPChunkContainer>> bpcontainers;

#ifdef USER_CONTAINER_DATA
	auto containerIter = containerVec.begin();
	for (; containerIter != containerVec.end(); containerIter++)
	{
		auto relativePos = WCoordToCoord3(containerIter->relativepos);
		flatbuffers::Offset<FBSave::ChunkContainer> container;

		if (!containerIter->container->getNeedClear())
		{
			container = containerIter->container->save(tempBuilder);
			bpcontainers.push_back(FBSave::CreateUGCBPChunkContainer(tempBuilder, container, &relativePos));
		}
	}
#endif
	bpcontainersoffset = tempBuilder.CreateVector(bpcontainers);

	//保存id映射表
	flatbuffers::Offset < flatbuffers::Vector<flatbuffers::Offset<FBSave::UGCBPPrefabMapInfo>>> prefabOffset = 0;
	{
		std::vector<flatbuffers::Offset<FBSave::UGCBPPrefabMapInfo>> prefabArr;
		prefabArr.reserve(m_PrefabMap.size());
		for (const auto& prefab : m_PrefabMap)
		{
			prefabArr.push_back(FBSave::CreateUGCBPPrefabMapInfo(tempBuilder, prefab.first, tempBuilder.CreateString(prefab.second.c_str())));
		}
		prefabOffset = tempBuilder.CreateVector(prefabArr);
	}

	long long worldid = g_WorldMgr ? g_WorldMgr->getWorldId() : 0;
	string skey("bp4insider_key");
	string sname("bp4insider_name");
	string nickname = g_WorldMgr->getWorldOwnerNickName();
	int authuin = g_WorldMgr->getWorldOwnerUin();
	FBSave::Coord3 dimCoord = WCoordToCoord3(dim);

	auto bp = CreateUGCBluePrint(tempBuilder, UGCBluePrintType::CustomBluePrint, authuin, worldid, &dimCoord, tempBuilder.CreateString(skey),
		tempBuilder.CreateString(nickname), tempBuilder.CreateString(sname), bpblocksoffset0, bpblocksoffset1, bpcontainersoffset, prefabOffset);
	tempBuilder.Finish(bp);

	char path[256] = { 0 };
	snprintf(path, sizeof(path) - 1, "voxel/%s_%d_%d_%d%s", filename.c_str(), dim.x+1, dim.y+1, dim.z+1, UGCBluePrint_File_Suffix);

	GetFileManager().SaveToWritePath(path, tempBuilder.GetBufferPointer(), tempBuilder.GetSize());
}
