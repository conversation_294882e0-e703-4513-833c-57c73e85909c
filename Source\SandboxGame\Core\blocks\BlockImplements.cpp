/**
* file : BlockImplements
* func : 方块类型注册
*/
#include "BlockImplements.h"

#include "BlockBasic.h"
#include "BlockCactus.h"
#include "BlockDukui.h"
#include "BlockChest.h"
#include "BlockTreeFruit.h"
#include "BlockColorHerbs.h"
#include "BlockMushroom.h"
#include "BlockSapling.h"
#include "BlockCraftingTable.h"
#include "BlockLightMushroom.h"
#include "BlockDoor.h"
#include "BlockFence.h"
#include "BlockSmelter.h"
#include "BlockGrass.h"
#include "BlockGrayHerbs.h"
#include "BlockGrayLeaf.h"
#include "BlockGrayLeafPopulus.h"
#include "BlockLadder.h"
#include "BlockLever.h"
#include "BlockLog.h"
#include "BlockMineStone.h"
#include "BlockMelonStem.h"
#include "BlockElectricUnit_Comparator.h"
#include "BlockElectricUnit_Repeater.h"
#include "BlockEnergyLight.h"
#include "BlockSlab.h"
#include "BlockCrackSlab.h"
#include "BlockSnow.h"
#include "BlockStair.h"
#include "BlockTorch.h"
#include "BlockEnergyTorch.h"
#include "BlockTrapDoor.h"
#include "BlockWire.h"
#include "BlockWall.h"
#include "BlockSandStoneColumn.h"
#include "BlockFluidFlow.h"
#include "BlockFluidStill.h"
#include "BlockCropper.h"
#include "BlockVine.h"
#include "BlockPyramid.h"
#include "BlockLilyPad.h"
#include "BlockWaterLily.h"
#include "BlockLadder.h"
#include "BlockCottonrug.h"
#include "BlockGlass.h"
#include "BlockFire.h"
#include "BlockRailNew.h"
#include "BlockHorizontalArcPlate.h"
#include "BlockTNT.h"
#include "BlockEnchantTable.h"
#include "BlockCake.h"
#include "BlockRepairTable.h"
#include "BlockMobSpawner.h"
#include "BlockIronFence.h"
#include "BlockGlassPane.h"
#include "BlockFenceGate.h"
#include "BlockBed.h"
#include "BlockButton.h"
#include "BlockSand.h"
#include "BlockHydarmBase.h"
#include "BlockHydarmMoving.h"
#include "BlockHydarmExten.h"
#include "BlockSpringBase.h"
#include "BlockSpringExten.h"
#include "BlockPressurePlate.h"
#include "BlockPortal.h"
#include "BlockJar.h"
#include "BlockChestSimple.h"
#include "BlockGravel.h"
#include "BlockSigns.h"
#include "BlockArrowSigns.h"
#include "BlockDragonCup.h"
#include "BlockFirework.h"
#include "BlockFunnel.h"
#include "BlockEmitter.h"
#include "BlockSticker.h"
#include "BlockFlag.h"
#include "BlockMusic.h"
#include "BlockHorseEgg.h"
#include "BlockDragonEgg.h"
#include "BlockFurniture.h"
#include "BlockTeamSpawnPoint.h"
#include "BlockTeamPrePoint.h"
#include "BlockStar.h"
#include "BlockChestInitial.h"
#include "BlockLamp.h"
#include "BlockAirWall.h"
#include "BlockIce.h"
#include "BlockFlowerPot.h"
#include "BlockPlaque.h"
#include "BlockPiecePlaque.h"
#include "BlockPhotoFrame.h"
#include "BlockChrismasTree.h"
#include "BlockSeaPlant.h"
#include "BlockCoral.h"
#include "BlockReplicator.h"
#include "BlockModelHerb.h"
#include "BlockMechaBlock.h"
#include "BlockBookshelf.h"
#include "BlockHardwire.h"
#include "BlockDirectionCommond.h"
#include "BlockChickenNest.h"
#include "BlockReborn.h"
#include "BlockRangeHydarm.h"
#include "BlockFlowerGrow.h"
#include "BlockCrystalEmitter.h"
#include "BlockSensor.h"
#include "BlockElectricUnit_Arithmatic.h"
#include "BlockElectricResist.h"
#include "BlockModelLeaf.h"
#include "BlockWindows.h"
#include "BlockBookcase.h"
#include "BlockLilyModel.h"
#include "BlockBamboo.h"
#include "BlockBreakable.h"
#include "BlockFlatPiece.h"
#include "BlockLuker.h"
#include "BlockClipTrap.h"
#include "BlockTotem.h"
#include "BlockCatapult.h"
#include "BlockCrackSlab.h"
#include "BlockGlassBottle.h"
#include "BlockHiveFull.h"
#include "BlockHiveEmpty.h"
#include "BlockCollider.h"
#include "BlockGoal.h"
#include "BlockAlienAir.h"
#include "BlockAlienCloud.h"
#include "BlockAlienTotem.h"
#include "BlockArcPlate.h"
#include "BlockVerticalArcPlate.h"
#include "BlockSoil.h"
#include "BlockFurnaceOxygen.h"
#include "BlockRockOil.h"
#include "BlockOxygenJar.h"
#include "BlockElectricUnit_Radio.h"
#include "BlockRainbowGrass.h"
#include "BlockElectricUnit_interpreter.h"
#include "BlockMutantLog.h"
#include "BlockMutantMushroom.h"
#include "BlockStarMushroom.h"
#include "BlockStoneCore.h"
#include "BlockGiantBattleTotem.h"
#include "BlockGiantTotem.h"
#include "BlockReflectMirror.h"
#include "BlockGiantStone.h"
#include "BlockPollution.h"
#include "BlockMeasureDistance.h"
#include "BlockBlueprint.h"
#include "BlockRegionReplicator.h"
#include "BlockBuildBluePrint.h"
#include "BlockModelCraft.h"
#include "BlockCustom.h"
#include "BlockSelectMobSpawner.h"
#include "BlockScreen.h"
#include "BlockTransfer.h"
#include "BlockTransferCore.h"
#include "BlockWorkshop.h"
#include "BlockBookEditorTable.h"
#include "BlockBookCabinet.h"
#include "BlockActorModel.h"
#include "BlockWheel.h"
#include "BlockModelEditor.h"
#include "BlockActioner.h"
#include "BlockBasketFrame.h"
#include "BlockThruster.h"
#include "BlockBonfire.h"
#include "BlockFullyCustom.h"
#include "BlockPhysicsParts.h"
#include "BlockSuspension.h"
#include "BlockJointArmPrismatic.h"
#include "BlockJointTRevolute.h"
#include "BlockWing.h"
#include "BlockEmpennage.h"
#include "BlockJointSpherical.h"
#include "BlockSThruster.h"
#include "BlockRopeHead.h"
#include "BlockRopeTail.h"
#include "BlockRope.h"
#include "BlockFuelTank.h"
#include "BlockSensorValue.h"
#include "BlockSensorDistance.h"
#include "BlockClaw.h"
#include "BlockWorksite.h"
#include "BlockTombStone.h"
#include "BlockVillageTotem.h"
#include "BlockVillagerFlag.h"
#include "BlockImportModel.h"
#include "BlockMoss.h"
#include "BlockPlantAshFarmland.h"
#include "BlockAltar.h"
#include "BlockWeatherForecast.h"
#include "BlockOneQuarter.h"
#include "BlockBanana.h"
#include "BlockThicket.h"
#include "BlockArbor.h"
#include "BlockGodStatue.h"
#include "BlockPoison.h"
#include "BlockMusicBox.h"
#include "BlockGravitySystem.h"
#include "BlockWeatherStone.h"
#include "BlockSimpleBathtub.h"
#include "BlockComputer.h"
#include "BlockSimpleCupboard.h"
#include "BlockSimpleDesk.h"
#include "BlockSimpleTable.h"
#include "BlockSimpleSofa.h"
#include "BlockSimpleWindow.h"
#include "BlockSimpleGroundTile.h"
#include "BlockSimpleWallBrick.h"
#include "BlockHonorFrame.h"
#include "BlockHearth.h"
#include "BlockHalfCabinet.h"
#include "BlockSimpleStair.h"
#include "BlockPot.h"
#include "BlockSulphurRock.h"
#include "BlockBranch.h"
#include "BlockMycelium.h"
#include "BlockKeyPedestal.h"
#include "BlockKeyOfFruit.h"
#include "BlockKeyOfBrokenSword.h"
#include "BlockKeyOfStoneEye.h"
#include "BlockStarStationTransfer.h"
#include "BlockAltarStela.h"
#include "BlockFeedTrough.h"
#include "BlockRuneOre.h"
#include "BlockBigChest.h"
#include "BlockClitter.h"
#include "BlockCoreSacredTree.h"
#include "BlockRevivalStatue.h"
#include "BlockPeristele.h"
#include "BlockStarCloud.h"
#include "BlockCoagulation.h"
#include "BlockShrub.h"
#include "BlockMysteriousBrick.h"
#include "BlockMiniClub.h"
#include "OgreScriptLuaVM.h"
#include "BlockMonsterSummoner.h"
#include "BlockTalkingStatue.h"
#include "BlockPiano.h"
#include "BlockCloudPortal.h"
#include "BlockBirdNest.h"
#include "BlockRadiation.h"
#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "BlockFuseTable.h"
#include "BlockPopulus.h"
#include "BlockGrayLeafPopulus.h"
#include "BlockShellbed.h"
#include "BlockShellLight.h"
#include "BlockReef.h"
#include "BlockNewCoral.h"
#include "BlockNewCoralLarva.h"
#include "BlockNewCoralBleaching.h"
#include "BlockPoseidonStatue.h"
#include "BlockPoseidonStatueBad.h"
#include "BlockGlowStick.h"
#include "BlockShoreFlower.h"
#include "BlockGiantScallops.h"
#include "BlockBells.h"
#include "BlockBooth.h"
#include "BlockFishFrame.h"
#include "BlockSmallTorch.h"
#include "BlockWholeTriangle.h"
#include "BlockHorizontalTriangleSlab.h"
#include "BlockVerticalTriangleSlab.h"
#include "BlockVerticalSlab.h"
#include "BlockSawtooth.h"
#include "BlockCoconut.h"
#include "BlockVaryVerticalSlab.h"
#include "BlockPlaceGlassBottle.h"
#include "BlockDriftBottle.h"
#include "BlockModelContainer.h"
#include "BlockRudderModelContainer.h"
#include "BlockFloatBucket.h"
#include "BlockGrayLeafMode.h"
#include "BlockSolidSand.h"
#include "BlockStake.h"//示例代码
#include "BlockColorPalette.h"
#include "BlockPlutonicRock.h"
#include "BlockColorFlower.h"

#include "BlockTreeOctagon.h"
#include "BlockTreeObj.h"
// #include "BlockSnowObj.h"
#include "BlockFlowerModel.h"
#include "BlockOstrichEgg.h"
#include "BlockSandFlow.h"
#include "BlockSandStillFlow.h"
#include "BlockCurseStone.h"
#include "BlockVisualizer.h"
#include "BlockMural.h"
#include "BlockHammer.h"
#include "BlockCanvas.h"
#include "BlockVenomFlow.h"
#include "BlockVenomStill.h"
#include "BlockOscillator.h"
#include "BlockFluidStillWater.h"
#include "BlockFluidFlowWater.h"
#include "BlockFluidStillLava.h"
#include "BlockFluidFlowLava.h"
#include "BlockFluidStillHoney.h"
#include "BlockFluidFlowHoney.h"

#include "BlockWirelessLever.h"
#include "BlockWirelessButton.h"
#include "BlockWirelessPressurePlate.h"
#include "BlockRayWire.h"
#include "BlockStarEnergyLightBeam.h"
#include "BlockWirelessNonGate.h"
#include "BlockWirelessAndGate.h"
#include "BlockWirelessOrGate.h"
#include "BlockElectricSplitter.h"
#include "BlockDelayElement.h"
#include "BlockResister.h"
#include "BlockCollectingPipe.h"
#include "BlockCurtain.h"
#include "BlockTransportPipe.h"
#include "BlockElectricCounter.h"
#include "BlockWirelessEmitter.h"
#include "BlockDetectionPipe.h"
#include "BlockWirelessComparator.h"
#include "BlockWirelessArithmatic.h"
#include "BlockWirelessHydarm.h"
#include "BlockWirelessRangeHydarm.h"
#include "BlockWirelessHydarmExten.h"
#include "BlockWirelessHydarmMoving.h"
#include "BlockDoubleBed.h"
#include "BlockVillageTotemIce.h"
#include "BlockTerritory.h"
#include "BlockMultiWall.h"
#include "BlockMultiSlab.h"
#include "BlockMultiStair.h"
#include "BlockMultiTriangle.h"
#include "BlockMultiBranch.h"
#include "BlockVillagerFlagBuilding.h"
#include "BlockIceCrystalShroom.h"
#include "BlockIceCrystalFern.h"
#include "BlockIcicle.h"
#include "BlockVillagerFlagIce.h"
#include "BlockNewLeaf.h"
#include "BlockSnowDrift.h"
#include "BlockStretchForzen.h"
#include "BlockBlanket.h"
#include "BlockIcicle.h"
#include "BlockIceReward.h"
#include "BlockSchoolFence.h"
#include "BlockPenguinNest.h"
#include "BlockTriangularPrism.h" //三棱柱
#include "BlockWeaponRack.h"
#include "BlockDummy.h"
#include "BlockManualEmitter.h"
#include "BlockFusionCage.h"
#include "BlockCobbleStone.h"
#include "BlockMonsterStatue.h"
#include "BlockBaseTable.h"
#include "BlockVoidMushroom.h"
#include "BlockVoidMushroomCap.h"
#include "BlockVoidPlants.h"
#include "BlockStove.h"
#include "BlockVacantFlower.h"
#include "BlockNewPot.h"
#include "BlockInkLotus.h"
#include "BlockAlphaModel.h"
#include "BlockStarStationCargo.h"
#include "BlockUmbrella.h"
#include "BlockTallGrassModel.h"
#include "BlockModBase.h"
#include "BlockPolaroidPhotoFrame.h"
#include "BlockDecomposition.h"
#include "BlockResearch.h"
#include "BlockWaterStorage.h"

//#include "ExampleGrass.h"

/* 绑定方块材质创建函数 */
#define RegisterNewBlockFunc(TName, TBlockClass) \
AddNewBlockMaterial<TBlockClass>(TName);
// end RegisterNewBlockFunc

/* 绑定方块材质创建函数，并绑定脚本 */
#define RegisterNewBlockBindScript(TName, TBlockClass, TScript) \
AddNewBlockMaterial<TBlockClass>(TName, TScript);
// end RegisterNewBlockBindScript

#define RegisterContact(B) #B

#define MonsterStatueRegister(X, Y, Z) \
	RegisterNewBlockFunc(RegisterContact(statusx##X##y##Y##z##Z##SpecialGOLD), BlockMonsterStatueSpecialx##X##y##Y##z##Z##GOLD);\
	RegisterNewBlockFunc(RegisterContact(statusx##X##y##Y##z##Z##SpecialSILIVER), BlockMonsterStatueSpecialx##X##y##Y##z##Z##SILIVER);\
	RegisterNewBlockFunc(RegisterContact(statusx##X##y##Y##z##Z##SpecialSTONE), BlockMonsterStatueSpecialx##X##y##Y##z##Z##STONE);

#define VoidPlantModelRegister(name) \
RegisterNewBlockFunc(RegisterContact(voidModel##name), BlockVoidModel##name)
#define VoidPlantHerbRegister(name) \
RegisterNewBlockFunc(RegisterContact(voidHerbSeed##name), BlockVoidHerbSeed##name)

BlockMaterial* BlockImplements::CreateNewBlockObject(const char* name, int resid)
{
	
	if (!name || !name[0])
		return nullptr;

#ifdef _DEBUG
	// 特殊id处理，临时
	auto itertemp = m_temp_block.find(resid);
	if (itertemp != m_temp_block.end())
	{
		name = itertemp->second.c_str();
	}
#endif

	auto iter = m_NewFuncs.find(name);
	if (iter == m_NewFuncs.end())
	{
		LogStringMsg("CreateNewBlockObject is null:%s", name);
		//assert(false);todo  暂时屏蔽
		return nullptr;
	}

	return (iter->second)(resid);
}

void BlockImplements::InitAllNewBlockFunc()
{
#ifdef _DEBUG
	/*
	* 定义指定ID的道具材质
	* 临时使用
	* m_temp_block 测试用，不需要去改策划表格。
	*/
	//m_temp_block.insert(make_pair(BLOCK_GRASS, "grass2"));
	//RegisterNewBlockBindScript("grass2", GrassBlockMaterial, "sandboxengine/demo/block/BlockGrass.lua");
	//m_temp_block.insert(make_pair(BLOCK_GRASS, "grass3"));
	//RegisterNewBlockBindScript("grass3", ExampleGrass, "sandboxengine/demo/block/BlockGrass.lua");
	//测试石块阶梯
	//m_temp_block.insert(make_pair(BLOCK_STONECUBESTAIR, "stair2"));
	//RegisterNewBlockBindScript("stair2", NewStairMaterial, "sandboxengine/demo/block/ExampleStair.lua");
	
	//m_temp_block.insert(make_pair(BLOCK_GRASS, "grass1"));
	//g_BlockMtlMgr.RegisterNewBlockMaterial<ExampleGrass>("grass1", "sandboxengine/demo/block/ExampleGrass1.lua");
#endif

	RegisterNewBlockFunc("unload", UnloadBlockMaterial);
	RegisterNewBlockFunc("air", AirBlockMaterial);
	RegisterNewBlockFunc("basic", BasicBlockMaterial);
	RegisterNewBlockFunc("basic_model", ModelBlockMaterial);
	RegisterNewBlockFunc("snow", SnowBlockMaterial);
	RegisterNewBlockFunc("log", LogBlockMaterial);
	RegisterNewBlockFunc("bluefruit", BlockBlueFruit);
	
	RegisterNewBlockFunc("crafting", CraftingBlockMaterial);
	RegisterNewBlockFunc("smelter", SmelterBlockMaterial);
	RegisterNewBlockFunc("airrefiner", FurnaceOxygenBlockMaterial);	//氧气灶, 类型名"airrefiner"
	RegisterNewBlockFunc("grass", GrassBlockMaterial);
// 	RegisterNewBlockFunc("flowfluid", FlowFluidMaterial);
// 	RegisterNewBlockFunc("stillfluid", StillFluidMaterial);
	RegisterNewBlockFunc("grayherbs", GrayHerbMaterial);
	RegisterNewBlockFunc("colorherbs", ColorHerbMaterial);
	RegisterNewBlockFunc("wall", WallMaterial);
	RegisterNewBlockFunc("sandstonecolumn", SandStoneColumnMaterial);
	RegisterNewBlockFunc("fence", FenceMaterial);
	RegisterNewBlockFunc("stair", StairMaterial);
	RegisterNewBlockFunc("torch", TorchMaterial);
	RegisterNewBlockFunc("door", DoorMaterial);
	RegisterNewBlockFunc("cropper", CropperMaterial);
	RegisterNewBlockFunc("melonstem", MelonStemMaterial);
	RegisterNewBlockFunc("mushroom", MushroomMaterial);
	RegisterNewBlockFunc("sapling", SaplingMaterial);
	RegisterNewBlockFunc("cactus", CactusMaterial);
	RegisterNewBlockFunc("dukui", DukuiMaterial);
	RegisterNewBlockFunc("treefruit", TreeFruitMaterial);
	RegisterNewBlockFunc("chest", ChestMaterial);
	RegisterNewBlockFunc("bigchest", BigChestMaterial);
	RegisterNewBlockFunc("chest_password", ChestPasswordMaterial);
	RegisterNewBlockFunc("slab", SlabMaterial);
	RegisterNewBlockFunc("crackslab", CrackSlabMaterial);
	RegisterNewBlockFunc("egenerator", EnergyTorchMaterial);
	RegisterNewBlockFunc("trapdoor", TrapDoorMaterial);
	RegisterNewBlockFunc("lever", LeverMaterial);
	RegisterNewBlockFunc("wire", BlockWire);
	RegisterNewBlockFunc("repeater", RepeaterMaterial);
	RegisterNewBlockFunc("comparator", ComparatorMaterial);
	RegisterNewBlockFunc("elight", EnergyLightMaterial);
	RegisterNewBlockFunc("vine", VineMaterial);
	RegisterNewBlockFunc("sticker", BlockSticker);
	RegisterNewBlockFunc("lilypad", LilyPadMaterial);
	RegisterNewBlockFunc("waterlily", WaterLilyMaterial);
	RegisterNewBlockFunc("ladder", LadderMaterial);
	RegisterNewBlockFunc("cottonrug", CottonrugMaterial);
	RegisterNewBlockFunc("glass", GlassMaterial);
	RegisterNewBlockFunc("fire", FireBlockMaterial);
	RegisterNewBlockFunc("rail", BlockRailKnot);
	RegisterNewBlockFunc("bangalore", BlockBangalore);
	RegisterNewBlockFunc("enchanting", BlockEnchantTable);
	RegisterNewBlockFunc("cake", BlockCake);
	RegisterNewBlockFunc("anvil", BlockRepairTable);
	RegisterNewBlockFunc("mobspawner", BlockMobSpawner);
	RegisterNewBlockFunc("ironfence", BlockIronFence);
	RegisterNewBlockFunc("glasspane", BlockGlassPane);
	RegisterNewBlockFunc("fencegate", BlockFenceGate);
	RegisterNewBlockFunc("bed", BlockBed);
	RegisterNewBlockFunc("button", BlockButton);
	RegisterNewBlockFunc("sand", BlockSand);
	RegisterNewBlockFunc("piston", BlockHydarmBase);
	RegisterNewBlockFunc("pistonmoving", BlockHydarmMoving);
	RegisterNewBlockFunc("pistonexten", BlockHydarmExten);
	RegisterNewBlockFunc("spring", BlockSpringBase);
	RegisterNewBlockFunc("springexten", BlockSpringExten);
	RegisterNewBlockFunc("pressure", BlockPressurePlate);
	RegisterNewBlockFunc("powerbasic", BlockPowerBasic);
	RegisterNewBlockFunc("scalyfruit", BlockScalyFruit);
	RegisterNewBlockFunc("portal", BlockPortal);
	RegisterNewBlockFunc("jar", BlockJar);
	RegisterNewBlockFunc("jarex", BlockJarEx);
	RegisterNewBlockFunc("lostJar", BlockDeathJar);
	RegisterNewBlockFunc("simplechest", BlockSimpleChest);
	RegisterNewBlockFunc("lockchest", BlockLockedChest);
	RegisterNewBlockFunc("gravel", BlockGravel);
	RegisterNewBlockFunc("notice", BlockSigns);
	RegisterNewBlockFunc("arrownotice", BlockArrowSigns);
	RegisterNewBlockFunc("dragoncup", BlockDragonCup);
	RegisterNewBlockFunc("firework", BlockFirework);
	RegisterNewBlockFunc("funnel", BlockFunnel);
	RegisterNewBlockFunc("emitter", BlockEmitter);
	RegisterNewBlockFunc("flag", BlockFlag);
	RegisterNewBlockFunc("musicloop", BlockMusicLoop);
	RegisterNewBlockFunc("musicuncoil", BlockMusicUncoil);
	RegisterNewBlockFunc("musicnote", BlockMusicNote);
	RegisterNewBlockFunc("musictone", BlockMusicTone);
	RegisterNewBlockFunc("horseegg", BlockHorseEgg);
	RegisterNewBlockFunc("dragonegg", BlockDragonEgg);
	RegisterNewBlockFunc("furniture", BlockFurniture);
	RegisterNewBlockFunc("chair", BlockChair);
	RegisterNewBlockFunc("shafa", BlockShafa);
	RegisterNewBlockFunc("bigshafa", BlockBigShafa);
	RegisterNewBlockFunc("birdnest", BlockBirdNest);
	RegisterNewBlockFunc("shoreflower", ShoreFlower);
	RegisterNewBlockFunc("shoreflowergrow", ShoreFlowerGrow);
	RegisterNewBlockFunc("teamspawn", BlockTeamSpawnPoint);
	RegisterNewBlockFunc("teamprepoint", BlockTeamPrePoint);
	RegisterNewBlockFunc("star", BlockStar);
	RegisterNewBlockFunc("initchest", BlockChestInitial);
	RegisterNewBlockFunc("lamp", BlockLamp);
	RegisterNewBlockFunc("candle", BlockCandle);
	RegisterNewBlockFunc("sparkler", BlockSparkler);
	RegisterNewBlockFunc("airwall", BlockAirWall);
	RegisterNewBlockFunc("ice", BlockIce);
	RegisterNewBlockFunc("flowerpot", BlockFlowerPot);
	RegisterNewBlockFunc("plaque", BlockPlaque);
	RegisterNewBlockFunc("bigplaque", BlockBigPlaque);
	RegisterNewBlockFunc("pieceplaque", BlockPiecePlaque);
	RegisterNewBlockFunc("photoframe", BlockPhotoFrame);
	RegisterNewBlockFunc("chrismastree", BlockChrismasTree);
	RegisterNewBlockFunc("chrismasbox", BlockChrismasBox);
	RegisterNewBlockFunc("modelherb", BlockModelHerb);
	RegisterNewBlockFunc("seaplant", SeaPlantMaterial);
	RegisterNewBlockFunc("coral", CoralMaterial);
	RegisterNewBlockFunc("replicator", BlockReplicator);
	RegisterNewBlockFunc("mechamarker", BlockMechaMarker);
	RegisterNewBlockFunc("mechaslider", BlockMechaSlider);
	RegisterNewBlockFunc("mecharotate", BlockMechaRotater);
	RegisterNewBlockFunc("bookshelf", BookshelfMaterial);
	RegisterNewBlockFunc("hardwire", BlockHardwire);
	RegisterNewBlockFunc("direction", BlockDirectionCommond);
	RegisterNewBlockFunc("bomb", BlockBomb);
	RegisterNewBlockFunc("chickennest", BlockChickenNest);
	RegisterNewBlockFunc("feedpan", BlockFeedPan);
	RegisterNewBlockFunc("reborn", BlockReborn);
	RegisterNewBlockFunc("logcropper", LogCropperBlockMaterial);
	RegisterNewBlockFunc("rangepiston", BlockRangeHydarm);
	RegisterNewBlockFunc("pineapple", PineApple);
	RegisterNewBlockFunc("flowergrow", FlowerGrowMaterial);
	RegisterNewBlockFunc("crystalemitter", CrystalEmitter);
	RegisterNewBlockFunc("sensorblock", BlockSensor);
	RegisterNewBlockFunc("arithmatic", BlockArithmatic);
	RegisterNewBlockFunc("eresist", BlockElectricResist);
	RegisterNewBlockFunc("grayleaf", GrayLeafMaterial);
	RegisterNewBlockFunc("grayleaffast", GrayLeafMaterialFast);
	RegisterNewBlockFunc("modelleaf", BlockModelLeaf);
	RegisterNewBlockFunc("windows", WindowsMaterial);
	RegisterNewBlockFunc("bookcase", BookcaseMaterial);
	RegisterNewBlockFunc("lilymodel", LilyModelMaterial);
	RegisterNewBlockFunc("bamboo", BlockBamboo);
	RegisterNewBlockFunc("bambooshoot", BlockBambooShoot);
	RegisterNewBlockFunc("rice", BlockRice);
	RegisterNewBlockFunc("breakable", BlockBreakable);
	RegisterNewBlockFunc("leafpile", BlockLeafPile);
	RegisterNewBlockFunc("luker", BlockLuker);
	RegisterNewBlockFunc("cliptrap", BlockClipTrap);
	RegisterNewBlockFunc("totem", BlockTotem);
	RegisterNewBlockFunc("catapult", BlockCatapult);
	RegisterNewBlockFunc("glassbottle", BlockGlassBottle);
	RegisterNewBlockFunc("hivefull", BlockHiveFull);
	RegisterNewBlockFunc("hiveempty", BlockHiveEmpty);
	RegisterNewBlockFunc("collider", BlockCollider);
	RegisterNewBlockFunc("colliderex", BlockColliderEx);
	RegisterNewBlockFunc("goalblock", BlockGoal);
	RegisterNewBlockFunc("alienair", BlockAlienAir);
	RegisterNewBlockFunc("aliencloud", BlockAlienCloud);
	RegisterNewBlockFunc("alientotem", BlockAlienTotem);
	RegisterNewBlockFunc("soil", BlockSoil);
	RegisterNewBlockFunc("rockoil", RockOilBlockMaterial);
	RegisterNewBlockFunc("oxygenjar", OxygenJarMaterial);
	RegisterNewBlockFunc("radioproducer", BlockRadioProducer);
	RegisterNewBlockFunc("radioreceiver", BlockRadioReceiver);
	RegisterNewBlockFunc("rainbowgrass", RainbowGrassMaterial);
	RegisterNewBlockFunc("bells", BlockBellsMaterial);
	RegisterNewBlockFunc("placeBottle", BlockPlaceGlassBottle);
	RegisterNewBlockFunc("driftBottle", BlockDriftBottle);
	RegisterNewBlockFunc("floatBucket", BlockFloatBucket);
	RegisterNewBlockFunc("colorflower", ColorFlowerMaterial);

	RegisterNewBlockFunc("radiointerpreter", BlockElectricInterpreter);
	RegisterNewBlockFunc("mutantlog", BlockMutantLog);
	RegisterNewBlockFunc("mutantmushroom", BlockMutantMushroom);
	RegisterNewBlockFunc("starmushroom", StarMushroomMaterial);
	RegisterNewBlockFunc("stonecore", BlockStoneCore);
	RegisterNewBlockFunc("giantbattletotem", BlockGiantBattleTotem);
	RegisterNewBlockFunc("gianttotem", BlockGiantTotem);
	RegisterNewBlockFunc("reflectmirror", BlockReflectMirror);
	RegisterNewBlockFunc("giantstone", BlockGiantStone);
	RegisterNewBlockFunc("pollution", BlockPollution);
	RegisterNewBlockFunc("measuredist", MeasureDistanceMaterial);
	RegisterNewBlockFunc("blueprint", BlueprintBlockMaterial);
	RegisterNewBlockFunc("regionreplicator", BlockRegionReplicator);
	RegisterNewBlockFunc("buildblueprint", BlockBuildBluePrint);
	RegisterNewBlockFunc("modelcraft", BlockModelCraft);
	RegisterNewBlockFunc("custombasic", BlockCustom);
	RegisterNewBlockFunc("selectbobspawner", BlockSelectMobSpawner);
	RegisterNewBlockFunc("screen", BlockScreen);
	RegisterNewBlockFunc("transfer", BlockTransfer);
	RegisterNewBlockFunc("transfercore", BlockTransferCore);
	RegisterNewBlockFunc("workshop", BlockWorkshop);
	RegisterNewBlockFunc("bookeditortable", BookEditorBlockMaterial);
	RegisterNewBlockFunc("bookcabinet", BlockBookCabinet);
	RegisterNewBlockFunc("actormodel", BlockActorModel);
	RegisterNewBlockFunc("wheel", BlockWheel);
	RegisterNewBlockFunc("modeleditor", BlockModelEditor);
	RegisterNewBlockFunc("actioner", BlockActioner);
	RegisterNewBlockFunc("basket", BlockBasketFrame);
	RegisterNewBlockFunc("fullycustomblock", BlockFullyCustom);
	RegisterNewBlockFunc("propeller", BlockThruster);
	RegisterNewBlockFunc("physicsparts", BlockPhysicsParts);
	RegisterNewBlockFunc("driverseat", BlockDriverSeat);
	RegisterNewBlockFunc("suspend", BlockSuspension);
	RegisterNewBlockFunc("arm_prismatic", BlockJointArmPrismatic);
	RegisterNewBlockFunc("t_revolute", BlockJointTRevolute);
	RegisterNewBlockFunc("wing", BlockWing);
	RegisterNewBlockFunc("empennage", BlockEmpennage);
	RegisterNewBlockFunc("spherical", BlockJointSpherical);
	RegisterNewBlockFunc("spacepropeller", BlockSThruster);
	RegisterNewBlockFunc("rope_header", BlockRopeHeader);
	RegisterNewBlockFunc("rope_tail", BlockRopeTail);
	RegisterNewBlockFunc("rope", BlockRope);
	RegisterNewBlockFunc("fuel_tank", BlockFuelTank);
	RegisterNewBlockFunc("sensorvalue", BlockSensorValue);
	RegisterNewBlockFunc("sensordistance", BlockSensorDistance);
	RegisterNewBlockFunc("claw", BlockClaw);
	RegisterNewBlockFunc("worksite", BlockWorksite);
	RegisterNewBlockFunc("bonfire", BlockBonfire);
	RegisterNewBlockFunc("tombstone", BlockTombTone);
	RegisterNewBlockFunc("villagetotem", BlockVillageTotem);
	RegisterNewBlockFunc("villagerflag", BlockVillagerFlag);
	RegisterNewBlockFunc("importmodel", BlockImportModel);
	RegisterNewBlockFunc("moss", MossBlockMaterial);
	RegisterNewBlockFunc("mosshuge", MossHugeBlockMaterial);
	RegisterNewBlockFunc("plantash", PlantAshFarmlandBlockMaterial);
	RegisterNewBlockFunc("altar", AltarMaterial);
	RegisterNewBlockFunc("sleepingbag", SleepingBag);
	RegisterNewBlockFunc("weatherforecast", BlockWeatherForecast);
	RegisterNewBlockFunc("onequarter", BlockOneQuarter);
	RegisterNewBlockFunc("bananawood", BlockBananaWood);
	RegisterNewBlockFunc("bananasapling", BananaSapling);
	RegisterNewBlockFunc("bananaleaf", BlockBananaLeaf);
	RegisterNewBlockFunc("banana", BlockBanana);
	RegisterNewBlockFunc("thicket", BlockThicket);//Arbor
	RegisterNewBlockFunc("arbor", BlockArbor);
	RegisterNewBlockFunc("statue", BlockGodStatue);
	RegisterNewBlockFunc("statuebase", BlockGodStatueBase);
	RegisterNewBlockFunc("poison", PoisonBlockMaterial);
	RegisterNewBlockFunc("arborleaf", BlockArborLeaf);
	RegisterNewBlockFunc("arborleafstar", BlockArborSpecialLeaf);
	RegisterNewBlockFunc("arborleafM", BlockArborLeafM);
	RegisterNewBlockFunc("arborleafstarM", BlockArborSpecialLeafM);
	RegisterNewBlockFunc("arborwoodstar", BlockArborSpecialWood);

	RegisterNewBlockFunc("musicbox", BlockMusicBox);
	RegisterNewBlockFunc("gravitysystem", BlockGravitySystem);
	RegisterNewBlockFunc("weatherstone", BlockWeatherStone);
	RegisterNewBlockFunc("simplebathtub", BlockSimpleBathtub);
	
	RegisterNewBlockFunc("simpledesk", BlockSimpleDesk);
	RegisterNewBlockFunc("simpletable", BlockSimpleTable);
	RegisterNewBlockFunc("simplesofa", BlockSimpleSofa);
	RegisterNewBlockFunc("simplewindow", BlockSimpleWindow);
	RegisterNewBlockFunc("simpledoor", BlockSimpleDoor);
	RegisterNewBlockFunc("simpletile", BlockSimpleGoundTile);
	RegisterNewBlockFunc("wallbrick", BlockSimpleWallBrick);
	RegisterNewBlockFunc("cupboard", BlockSimpleCupBoard);
	RegisterNewBlockFunc("radiation", BlockRadiation);

	RegisterNewBlockFunc("honorframe", BlockHonorFrame);
	RegisterNewBlockFunc("hearth", HearthBlockMaterial);
	RegisterNewBlockFunc("rug", BlockRug);
	RegisterNewBlockFunc("picture", BlockPicture);
	RegisterNewBlockFunc("halfcabinet", BlockHalfCabinet);
	
	RegisterNewBlockFunc("fridge", BlockFridge);
	RegisterNewBlockFunc("simplestair", BlockSimpleStair);

	RegisterNewBlockFunc("foursquarepicture", BlockFourSizeSquarePicture);
	RegisterNewBlockFunc("minestone", BlockMineStone);
	RegisterNewBlockFunc("minestonediffmtl", BlockMineStoneDiffMtl);
	RegisterNewBlockFunc("minestonerune", BlockMineStoneRune);
	RegisterNewBlockFunc("twoblockrug", BlockTwoBlockRug);
	RegisterNewBlockFunc("pot", BlockPot);
	RegisterNewBlockFunc("keypedestal", BlockKeyPedestal);
	RegisterNewBlockFunc("keyoffruit", BlockKeyOfFruit);
	RegisterNewBlockFunc("keyofbrokensword", BlockKeyOfBrokenSword);
	RegisterNewBlockFunc("keyofstoneeye", BlockKeyOfStoneEye);
	RegisterNewBlockFunc("sulphurrock", SulphurRockBlockMaterial);
	RegisterNewBlockFunc("branch", BlockBranch);
	RegisterNewBlockFunc("starstationtransferconsole", BlockStarStationTransferConsole);
	RegisterNewBlockFunc("starstationtransfercabin", BlockStarStationTransferCabin);
	RegisterNewBlockFunc("mycelium", BlockMycelium);
	RegisterNewBlockFunc("myceliummodel", BlockMyceliumModel);
	RegisterNewBlockFunc("blockaltarstela", BlockAltarStela);
	RegisterNewBlockFunc("trough", BlockFeedTrough);
	RegisterNewBlockFunc("runeore", BlockRuneOre);
	RegisterNewBlockFunc("clitter", BlockClitter);
	RegisterNewBlockFunc("core_sacredtree", BlockCoreSacredTree);
	RegisterNewBlockFunc("RevivalStatue", BlockRevivalStatue);
	RegisterNewBlockFunc("peristele", BlockPeristele);
	RegisterNewBlockFunc("starcloud", BlockStarCloud);
	RegisterNewBlockFunc("coagualtion", BlockCoagulation);
	RegisterNewBlockFunc("shrub", BlockShrub);
	RegisterNewBlockFunc("mysteriousbrick", BlockMysteriouBrick);

	RegisterNewBlockFunc("piano", BlockPiano); 
	RegisterNewBlockFunc("festivelantern", BlockFestiveLantern);
	RegisterNewBlockFunc("miniclub", BlockMiniClub);
	RegisterNewBlockFunc("cloudportal", BlockCloudPortal);   // 云服跨房间传送方块


	RegisterNewBlockFunc("reef", BlockReef);
	RegisterNewBlockFunc("newcoral", BlockNewCoral);
	RegisterNewBlockFunc("newcorallarva", BlockNewCoralLarva);
	RegisterNewBlockFunc("newcoralbleaching", BlockNewCoralBleaching);
	RegisterNewBlockFunc("poseidonstatue", BlockPoseidonStatue);
	RegisterNewBlockFunc("giantscallops", BlockGiantScallops);
	RegisterNewBlockFunc("poseidonstatuebad", BlockPoseidonStatueBad);
	RegisterNewBlockFunc("shellbed", BlockShellbed);
	RegisterNewBlockFunc("shelllight", BlockShellLight);
	RegisterNewBlockFunc("oceantable", BlockOceanTable);
	RegisterNewBlockFunc("seaweed", SeaWeedMaterial);
    RegisterNewBlockFunc("waterweed", WaterWeedMaterial);
    RegisterNewBlockFunc("redalga", RedAlgaMaterial);
    RegisterNewBlockFunc("glowsticksalgaeseed", GlowSticksAlgaeSeedMaterial);
    RegisterNewBlockFunc("glowsticksalgae", GlowSticksAlgaeMaterial);
    RegisterNewBlockFunc("glowstick", GlowStickMaterial);
	RegisterNewBlockFunc("fishframe", BlockFishFrame);
	RegisterNewBlockFunc("smalltorch", SmallTorchMaterial);



	RegisterNewBlockFunc("trianglewholeslab", WholeTriangleMaterial);
	RegisterNewBlockFunc("horizontaltrianglehalfslab", HorizontalTriangleHalfSlabMaterial);
	RegisterNewBlockFunc("horizontaltriangleslab", HorizontalTriangleSlabMaterial);
	RegisterNewBlockFunc("verticaltrianglehalfslab", VerticalTriangleHalfSlabMaterial);
	RegisterNewBlockFunc("verticaltriangleslab", VerticalTriangleSlabMaterial);
	RegisterNewBlockFunc("verticalslab", VerticalSlabMaterial);
	RegisterNewBlockFunc("varyverticalslab", VerticalVarySlabMaterial);
	RegisterNewBlockFunc("colorpalette", BlockColorPalette);


	RegisterNewBlockFunc("triangularPrism", TriangularPrismMaterial);

	//SandboxResult homeresult = SandboxEventDispatcherManager::GetGlobalInstance().
	//	Emit("Homeland_RegisterNewBlock", SandboxContext(nullptr));
	RegisterNewBlockFunc("modelcontainer", BlockModelContainer);
	RegisterNewBlockFunc("ruddermodelcontainer", BlockRudderModelContainer);
	
	RegisterNewBlockFunc("sawtooth", BlockSawtooth);
	RegisterNewBlockFunc("coconutwood", BlockCoconutWood);
	RegisterNewBlockFunc("coconutsapling", CoconutSapling);
	RegisterNewBlockFunc("coconut", BlockCoconut);
	RegisterNewBlockFunc("coconutleaf", BlockCoconutLeaf);

	RegisterNewBlockFunc("grayleafmodel", GrayLeafModelMaterial);
	RegisterNewBlockFunc("solidsand", BlockSolidSand);
	RegisterNewBlockFunc("stake", StakeMaterial);
	RegisterNewBlockFunc("plutonicrock", BlockPlutonicRock);

	RegisterNewBlockFunc("octagon", OctagonBlockMaterial);
// 	RegisterNewBlockFunc("snowobj", SnowObjBlockMaterial);
	RegisterNewBlockFunc("whitemums", BlockWildCorn);
	RegisterNewBlockFunc("newcorn", BlockNewCorn);
	RegisterNewBlockFunc("cactussmallseed", CactusSmallSeed);
	RegisterNewBlockFunc("cactusseed", CactusSeed);
	RegisterNewBlockFunc("cactusbranch", CactusBranch);
	RegisterNewBlockFunc("cactusflower", CactusFlower);
	RegisterNewBlockFunc("cactusfruit", CactusFruit);
	RegisterNewBlockFunc("grayleafpopulus", GrayLeafMaterialPopulus);
	RegisterNewBlockFunc("modelleafpopulus", BlockModelLeafPopulus);
	RegisterNewBlockFunc("logpopulus", BlockLogPopulusMaterial);
	RegisterNewBlockFunc("branchpopulus", BlockBranchPopulus);
	RegisterNewBlockFunc("populustears", BlockPopulusTears);
	RegisterNewBlockFunc("populusflower", BlockPopulusFlower);
	RegisterNewBlockFunc("populussapling", BlockSaplingPopulus);
	RegisterNewBlockFunc("visualizer", BlockVisualizer);
	RegisterNewBlockFunc("mural", BlockMural);
	RegisterNewBlockFunc("hammer", HammerMaterial);
	RegisterNewBlockFunc("canvas", BlockCanvas);
	RegisterNewBlockFunc("fusetable", BlockFuseTable);
	RegisterNewBlockFunc("staticsandflow", StillFlowSandMaterial);
	RegisterNewBlockFunc("oscillator", BlockOscillator);
	RegisterNewBlockFunc("stillvenom", StillVenomMaterial);
	RegisterNewBlockFunc("flowvenom", FlowVenomMaterial);
	RegisterNewBlockFunc("objtree", ObjTreeMaterial);
	RegisterNewBlockFunc("ostrichegg", BlockOstrichEgg);
	RegisterNewBlockFunc("sandflow", FlowSandMaterial);	
	RegisterNewBlockFunc("cursestone", BlockCurseStone);
	RegisterNewBlockFunc("cursestoneactivated", BlockCurseStoneActivated);
	RegisterNewBlockFunc("giantscallops", BlockGiantScallops);
	RegisterNewBlockFunc("poseidonstatuebad", BlockPoseidonStatueBad);
	RegisterNewBlockFunc("oceantable", BlockOceanTable);
	RegisterNewBlockFunc("colorpalette", BlockColorPalette);
	RegisterNewBlockFunc("stillfluidwater", StillFluidWaterMaterial);
	RegisterNewBlockFunc("flowfluidwater", FlowFluidWaterMaterial);
	RegisterNewBlockFunc("stillfluidlava", StillFluidLavaMaterial);
	RegisterNewBlockFunc("flowfluidlava", FlowFluidLavaMaterial);
	RegisterNewBlockFunc("stillfluidhoney", StillFluidHoneyMaterial);
	RegisterNewBlockFunc("flowfluidhoney", FlowFluidHoneyMaterial);
	RegisterNewBlockFunc("lightmushroom", BlockLightMushroom);
	RegisterNewBlockFunc("fluorescentlamp", BlockFluorescentLamp);
	RegisterNewBlockFunc("doublebed", BlockDoubleBed);
	RegisterNewBlockFunc("booth", BlockBooth);
	RegisterNewBlockFunc("curtain", BlockCurtain);
	RegisterNewBlockFunc("blanket", BlockBlanket);
	RegisterNewBlockFunc("snowmanlight", BlockSnowManLight);
	RegisterNewBlockFunc("xparentwindows", XparentWindowsMaterial);
	RegisterNewBlockFunc("icetable", BlockIceTable);
	RegisterNewBlockFunc("sanriobed", BlockSanrioBed);


	//新的 无线电路系统
	RegisterNewBlockFunc("wireless_lever", BlockWirelessLever);
	RegisterNewBlockFunc("wireless_button", BlockWirelessButton);
	RegisterNewBlockFunc("wireless_pressure", BlockWirelessPressurePlate);
	RegisterNewBlockFunc("wireless_rayblock", BlockRayWire);
	RegisterNewBlockFunc("wireless_starenergy", BlockStarEnergyLightBeam);
	RegisterNewBlockFunc("wireless_nongate", BlockWirelessNonGate);
	RegisterNewBlockFunc("wireless_andgate", BlockWirelessAndGate);
	RegisterNewBlockFunc("wireless_orgate", BlockWirelessOrGate);
	RegisterNewBlockFunc("wireless_electricsplitter", BlockElectricSplitter);
	RegisterNewBlockFunc("keydoor", BlockKeyDoor);
	RegisterNewBlockFunc("socdoor", BlockSocDoor);
	RegisterNewBlockFunc("socautodoor", BlockSocAutoDoor);
	RegisterNewBlockFunc("socdoubledoor", BlockSocDoubleDoor);
	RegisterNewBlockFunc("wireless_collecting_pipe", BlockCollectingPipe);
	RegisterNewBlockFunc("wireless_transport_pipe", BlockTransportPipe);
	RegisterNewBlockFunc("wireless_counter", BlockElectricCounter);
	RegisterNewBlockFunc("wireless_delay", BlockDelayElement);
	RegisterNewBlockFunc("wireless_Resister", BlockResister);
	RegisterNewBlockFunc("wireless_emitter", BlockWirelessEmitter);
	RegisterNewBlockFunc("wireless_detection_pipe", BlockDetectionPipe);
	RegisterNewBlockFunc("wireless_comparator", BlockWirelessComparator);
	RegisterNewBlockFunc("wireless_arithmatic", BlockWirelessArithmatic);
	RegisterNewBlockFunc("wireless_hydarm", BlockWirelessHydarm);
	RegisterNewBlockFunc("wireless_rangehydarm", BlockWirelessRangeHydarm);
	RegisterNewBlockFunc("wireless_extenhydarm", BlockWirelessHydarmExten);
	RegisterNewBlockFunc("wireless_movinghydarm", BlockWirelessHydarmMoving);

	RegisterNewBlockFunc("villagetotemice", BlockVillageTotemIce);
	RegisterNewBlockFunc("villagerflagbuilding", BlockVillagerFlagBuilding);
	RegisterNewBlockFunc("iceCrystalShroomSeedling", BlockIceCrystalShroomSeedling);
	RegisterNewBlockFunc("iceCrystalShroomDark", BlockIceCrystalShroomDark);
	RegisterNewBlockFunc("iceCrystalShroomLight", BlockIceCrystalShroomLight);
	RegisterNewBlockFunc("iceCrystalFernBud", BlockIceCrystalFernBud);
	RegisterNewBlockFunc("iceCrystalFernSeedling", BlockIceCrystalFernSeedling);
	RegisterNewBlockFunc("iceCrystalFern", BlockIceCrystalFern);
	RegisterNewBlockFunc("icicle", BlockIcicle);
	RegisterNewBlockFunc("hardicicle", BlockHardIcicle);
	RegisterNewBlockFunc("villagerflagice", BlockVillagerFlagIce);
	RegisterNewBlockFunc("newleaf", BlockNewLeaf);
	RegisterNewBlockFunc("newleaffast", BlockNewLeafFast);
	RegisterNewBlockFunc("fruitmodel", FruitModelMaterial);
	//雪堆可融化
	RegisterNewBlockFunc("snowdrift", BlockSnowDrift);
	RegisterNewBlockFunc("stretchforzen", BlockStretchForzen);
	RegisterNewBlockFunc("icereward", BlockIceReward);
	RegisterNewBlockFunc("centerdoor", BlockCenterDoor);
	//运动场围栏
	RegisterNewBlockFunc("schoolfence", BlockSchoolFence);
	//企鹅蛋
	RegisterNewBlockFunc("penguinNest", BlockPenguinNest);
	//弧板
	RegisterNewBlockFunc("arcplate", ArcPlateMaterial);
	//薄弧板
	RegisterNewBlockFunc("horizontalarchalfplate", HorizontalArcHalfPlateMaterial);
	//大薄弧板
	RegisterNewBlockFunc("horizontalarcplate", HorizontalArcPlateMaterial);
	//竖薄弧板
	RegisterNewBlockFunc("verticalarchalfplate", VerticalArcHalfPlateMaterial);
	//大竖薄弧板
	RegisterNewBlockFunc("verticalarcplate", VerticalArcPlateMaterial);
	//棱锥
	RegisterNewBlockFunc("pyramid", PyramidMaterial);
	//装备架
	RegisterNewBlockFunc("weaponrack", BlockWeaponRack);
	//假人
	RegisterNewBlockFunc("dummy", BlockDummy);
	//手持发射器
	RegisterNewBlockFunc("manualEmitter", BlockManualEmitter);
	//融合器
	RegisterNewBlockFunc("fusioncage", BlockFusionCage);
	//鹅卵石
	RegisterNewBlockFunc("cobblestone", CobbleStoneMaterial);
	//雕像
	MonsterStatueRegister(1, 1, 1)
	MonsterStatueRegister(1, 2, 1)
	MonsterStatueRegister(2, 2, 2)
	MonsterStatueRegister(3, 3, 3)
	//比较特殊的雕像方块
	RegisterNewBlockFunc("statusx3y3z3BOSS", BlockMonsterStatueSpecialx3y3z3BOSS);
	RegisterNewBlockFunc("pileTop", BlockPileTop);
	RegisterNewBlockFunc("pileBottom", BlockPileBottom);
	RegisterNewBlockFunc("pileCenter", BlockPileCenter);
	RegisterNewBlockFunc("pileLayer", BlockPile);
	RegisterNewBlockFunc("monsterStatueBottom", BlockMonsterStatueBottom);
	RegisterNewBlockFunc("monsterStatueLayer",  BlockMonsterStatueLayer);
	RegisterNewBlockFunc("monsterStatueFoundation", BlockMonsterStatueFoundation);
	//基础桌子
	RegisterNewBlockFunc("baseTable", BlockBaseTable);
	//虚空荧光菇根
	RegisterNewBlockFunc("voidmushroom", BlockVoidMushroom);
	//虚空荧光菇伞
	RegisterNewBlockFunc("voidmushroomcap", BlockVoidMushroomCap);
	//各种虚空果实
	VoidPlantModelRegister(Cherry)
	VoidPlantModelRegister(Larch)
	VoidPlantModelRegister(Arbor)
	//VoidPlantModelRegister(Rare)
	VoidPlantModelRegister(Cedar)
	VoidPlantModelRegister(Walnut)
	VoidPlantModelRegister(Peach)
	VoidPlantModelRegister(WhiteYang)
	RegisterNewBlockFunc("voidDragonFlower", BlockVoidDragonFlower);
	VoidPlantHerbRegister(DragonFlower);
	RegisterNewBlockFunc("voidBellFlower", BlockVoidBellFlower);
	VoidPlantHerbRegister(BellFlower);
	
	//虚空瓜
	RegisterNewBlockFunc("voidmelon", BlockVoidMelon);
	//秘银炉子
	RegisterNewBlockFunc("stove", BlockStoveMaterial);
	RegisterNewBlockFunc("newpot", BlockNewPot);
	//虚空之花
	RegisterNewBlockFunc("Voidflowers", BlockVacantFlower);

	//水墨荷花
	RegisterNewBlockFunc("inklotus", BlockInkLotus);

	//带alpha的model
	RegisterNewBlockFunc("basic_model_alpha", BlockAlphaModel);
	//四格高门
	RegisterNewBlockFunc("highdoor", BlockHighDoor);
	VoidPlantModelRegister(StarFlower)
	VoidPlantModelRegister(Ruolan)
	VoidPlantModelRegister(DragonFlower)
	VoidPlantModelRegister(DragonTree)
	VoidPlantModelRegister(Hyacinth)
	VoidPlantModelRegister(YellowFlower)
	VoidPlantModelRegister(Maguey)
	VoidPlantModelRegister(WhiteFlower)
	VoidPlantModelRegister(Rosebush)
	VoidPlantModelRegister(WinterFlower)
	RegisterNewBlockFunc("voidModelBiAnflower", BlockVoidModelBiAnflower)
	RegisterNewBlockFunc("inkScreen", BlockInkScreen)
	RegisterNewBlockFunc("starstationcargo", BlockStarStationCargo)
	RegisterNewBlockFunc("umbrella", BlockUmbrella);
	RegisterNewBlockFunc("tall_grass", BlockTallGrassModel);
	RegisterNewBlockFunc("simplepicture", BlockSimplePicture);
	RegisterNewBlockFunc("modbase", BlockModBase);
	RegisterNewBlockFunc("computer", BlockComputer);
	RegisterNewBlockFunc("monstersummoner", BlockMonsterSummoner);
	RegisterNewBlockFunc("talkingstatue", BlockTalkingStatue);
	RegisterNewBlockFunc("polaroidframe", BlockPolaroidPhotoFrame);


	RegisterNewBlockFunc("WaterStorage", BlockWaterStorage);
	//领地
	RegisterNewBlockFunc("territory", BlockTerritory);

	RegisterNewBlockFunc("decomposition", BlockDecompositionMaterial);
	RegisterNewBlockFunc("research", BlockResearch);

	RegisterNewBlockFunc("multiwall", BlockMultiWall);
	RegisterNewBlockFunc("multiceiling", BlockMultiSlab);//天花板
	RegisterNewBlockFunc("multistair", BlockMultiStair);//2X2楼梯
	RegisterNewBlockFunc("multitriangle", BlockMultiTriangle);//2X2斜板
	RegisterNewBlockFunc("multibranch", BlockMultiBranch);//1X2立杆
}
