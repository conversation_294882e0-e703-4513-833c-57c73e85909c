#include "WorldWaterStorage.h"
#include "LuaInterfaceProxy.h"
#include "SandboxIdDef.h"
#include "world.h"
#include <WorldManager.h>
#include "BlockScene.h"
#include <BlockMaterialMgr.h>
#include "TerritoryManager.h"
#include "container_territory.h"

const int CollectWaterTickToSecond = 5;
//-----------------------------------------------------WorldWaterStorage--------------------------------------------------------------------------
/**********************************************************************************************
类    名：WorldWaterStorage
功    能：蓄水方块container
********************************************************************************************* */
WorldWaterStorage::WorldWaterStorage()
{
}

WorldWaterStorage::WorldWaterStorage(const WCoord& blockpos) :ErosionContainer(blockpos, 0)
{
}

void WorldWaterStorage::initCollectData(World* pworld)
{
	auto biome = pworld->getBiome(m_BlockPos.x, m_BlockPos.z);
	if (biome)
	{
		m_nOnceTickMax = biome->WaterStorageCollectOnceTime * CollectWaterTickToSecond;
		m_nOnceVolume = biome->WaterStorageCollectOnceVolume;
	}
	auto blockid = pworld->getBlockID(m_BlockPos);
	auto pitemdef = GetDefManagerProxy()->getItemDef(blockid);
	if (pitemdef)
	{
		m_nWaterVolumeMax = atoi(pitemdef->para.c_str());
	}
}

bool WorldWaterStorage::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerWaterStorage*>(srcdata);
	loadErosionContainer(src->basedata());

	m_nCurWaterVolume = src->watervolume();

	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldWaterStorage::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerErosion(builder);

	auto actor = FBSave::CreateContainerWaterStorage(builder, basedata, m_nCurWaterVolume);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerWaterStorage, actor.Union());
}

void WorldWaterStorage::enterWorld(World* pworld)
{
	ErosionContainer::enterWorld(pworld);
	initCollectData(pworld);
	registerUpdateTick();
	if (GetWorldManagerPtr())
	{
		
	}
	//updateEffect();
	pworld->markBlockForUpdate(m_BlockPos, true);
}

void WorldWaterStorage::leaveWorld() {
	WorldContainer::leaveWorld();
	//stopEffect();
}

void WorldWaterStorage::updateTick()
{
	ErosionContainer::updateTick();
	if (m_nOnceTickMax <= m_nTickCounter)
	{
		m_nTickCounter = 0;
		addWater(m_nOnceVolume);
	}
	else m_nTickCounter++;
	//m_desertSleepCount = -1;
}

void WorldWaterStorage::addWater(int value)
{
	m_nCurWaterVolume += value;
	if (value < 0)
	{
		m_nCurWaterVolume = std::max(0, (int)m_nCurWaterVolume);
	}
	else
	{
		m_nCurWaterVolume = std::min((int)m_nWaterVolumeMax, (int)m_nCurWaterVolume);
	}
}

//void WorldWaterStorage::updateEffect() {
//	if (m_World)
//	{
//		int blockdata = m_World->getBlockData(m_BlockPos);
//		if ((blockdata & 4) != 0)
//		{
//			return;
//		}
//		WCoord effectPos = getEffectPos(blockdata);
//		int maxage = Rainbow::MAX_INT;
//		m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_agonal.ent", effectPos, maxage);
//
//	}
//}

//void WorldWaterStorage::stopEffect()
//{
//	if (m_World)
//	{
//		int blockdata = m_World->getBlockData(m_BlockPos);
//		stopEffectByBlockdata(blockdata);
//	}
//}

//void WorldWaterStorage::stopEffectByBlockdata(int blockdata)
//{
//	if ((blockdata & 4) != 0)
//	{
//		return;
//	}
//	WCoord effectPos = getEffectPos(blockdata);
//	m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_agonal.ent", effectPos);
//}

//WCoord WorldWaterStorage::getEffectPos(int blockdata)
//{
//	int placedir = blockdata & 3;
//	WCoord effectPos = NeighborCoord(m_BlockPos, ReverseDirection(placedir));
//	if (placedir == DIR_NEG_X)
//	{
//		effectPos = BlockCenterCoord(effectPos) + WCoord(30, 100, 0);
//	}
//	else if (placedir == DIR_POS_X)
//	{
//		effectPos = BlockCenterCoord(effectPos) + WCoord(-30, 100, 0);
//	}
//	else if (placedir == DIR_NEG_Z)
//	{
//		effectPos = BlockCenterCoord(effectPos) + WCoord(0, 100, 30);
//	}
//	else if (placedir == DIR_POS_Z)
//	{
//		effectPos = BlockCenterCoord(effectPos) + WCoord(0, 100, -30);
//	}
//	return effectPos;
//}