﻿#include "WorldEventCsv.h"
#include "OgreUtils.h"
#include "OgreStringUtil.h"
#include <vector>
using namespace Rainbow;
using MINIW::CSVParser;
using namespace MINIW;

IMPLEMENT_LAZY_SINGLETON(WorldEventCsv)

WorldEventCsv::WorldEventCsv()
{
}

WorldEventCsv::~WorldEventCsv()
{
    onClear();
}

void WorldEventCsv::onClear()
{
    m_WorldEventTable.clear();
}

const char* WorldEventCsv::getName()
{
    return "WorldEvent";
}

const char* WorldEventCsv::getClassName()
{
    return "WorldEventCsv";
}

void WorldEventCsv::onParse(MINIW::CSVParser& parser)
{
    m_WorldEventTable.clear();
    parser.SetTitleLine(1);
    const auto total = parser.GetNumLines();

    for (int i = 2; i < total; ++i)
    {
        if (parser[i]["ID"].IsValid()) {
            WorldEventDef def;
            def.ID = parser[i]["ID"].Int();
            if (def.ID == 0) continue;
            def.Name = parser[i]["Name"].Str();
            def.EventType = parser[i]["EventType"].Str();
            def.Interval = parser[i]["Interval"].Int();
            def.FirstTime = parser[i]["FirstTime"].Int();
            def.Speed = parser[i]["Speed"].Float();
            if (def.Speed == 0.0f) {
                def.Speed = 1000.0f;
            }
   
            def.Enable = std::string(parser[i]["Enable"].Str()) == "false" ? false : true;
                       
            std::string pos = parser[i]["Pos"].Str(); //[1;{100;100};{5000;5000}]
            if (!ParsePositionString(pos, def.dropType, def.drop_pos1, def.drop_pos2)) {
                LOG_WARNING("Failed to parse position for event ID %d, using default values", def.ID); 
            }
            
            def.PlanceModelId = parser[i]["PlanceModel"].Int(); 
            def.TreasureId = parser[i]["Treasure"].Int();
            def.Harm = parser[i]["Harm"].Int();
            def.Description = parser[i]["Description"].Str();
            def.boardcast_msg = parser[i]["boardcast_msg"].Str();
            if (def.boardcast_msg == "") {
                //def.boardcast_msg = "空投飞机即将到达，请注意接收物资!";
                def.boardcast_msg = "Airdrop plane is about to arrive, please pay attention to receive supplies!";
            }


            def.MaxHeight = parser[i]["MaxHeight"].Int();
            if (def.MaxHeight == 0) {
                def.MaxHeight = 100;
            }

            def.MaxSizeX = parser[i]["MaxSize"].Int();
            if (def.MaxSizeX == 0) {
                def.MaxSizeX = 2500;
            }
            def.MaxSizeZ = def.MaxSizeX;

            def.IsDebug = std::string(parser[i]["IsDebug"].Str()) == "true"? true : false;

            def.TimeOut = parser[i]["TimeOut"].Int();
            if (def.TimeOut == 0){
                def.TimeOut = 600;
            }

            def.ChestModel = std::string(parser[i]["ChestModel"].Str());
     
            // 添加到数据表
            m_WorldEventTable.AddRecord(def.ID, def);
        }
    }
}

int WorldEventCsv::getNum()
{
    return m_WorldEventTable.GetRecordSize();
}

WorldEventDef* WorldEventCsv::getById(int id)
{
    load();
    WorldEventDef* def = m_WorldEventTable.GetRecord(id);
    if(!def)
    {
        return nullptr;
    }
    return def;
}
WorldEventDef* WorldEventCsv::getByIndex(int i)
{
    load();
    WorldEventDef* def = m_WorldEventTable.GetRecordByIndex(i);
    if (!def)
    {
        return nullptr;
    }
    return def;
}




std::vector<WorldEventDef*> WorldEventCsv::getAllEvents()
{
    load();
    std::vector<WorldEventDef*> result;
    int size = getNum();

    for (int i = 0; i < size; i++)
    {
        WorldEventDef* def = m_WorldEventTable.GetRecordByIndex(i);
        if (def)
        {
            result.push_back(def);
        }
    }

    return result;
}

std::string WorldEventCsv::getEventToJson(int id)
{
    load();
    WorldEventDef* def = getById(id);
    if (!def)
    {
        return "{}";
    }

    jsonxx::Object obj;
    obj << "ID" << def->ID;
    obj << "EventType" << def->EventType;
    obj << "Name" << def->Name;
    obj << "FirstTime" << def->FirstTime;
    obj << "Cooldown" << def->Interval;
    obj << "Priority" << def->Priority;
    obj << "PlanceModelId" << def->PlanceModelId;
    obj << "TreasureId" << def->TreasureId;
    obj << "Description" << def->Description;

    return obj.json();
}

//[1;{100;100};{5000;5000}]
bool WorldEventCsv::ParsePositionString(const std::string& posStr, int& outDropType, DropPos& outDropPos1, DropPos& outDropPos2) {
    // 设置默认值
    outDropType = 0;
    outDropPos1.x = outDropPos1.y = outDropPos1.z = 0;
    outDropPos2.x = outDropPos2.y = outDropPos2.z = 0;

    if (posStr.empty()) {
        LOG_WARNING("Empty position string");
        return false;
    }
    
    try {
        // 确保字符串格式正确，至少有[]括号
        if (posStr.length() < 2 || posStr.front() != '[' || posStr.back() != ']') {
            LOG_WARNING("Invalid position format, expected [...] but got: %s", posStr.c_str());
            return false;
        }
        
        std::vector<std::string> posVec;
        // 去掉首尾的[]再分割
        Rainbow::StringUtil::split(posVec, posStr.substr(1, posStr.length() - 2), ";");
        
        // 确保有足够的元素
        if (posVec.size() < 3) {
            LOG_WARNING("Invalid position format, expected at least 3 elements but got %d: %s", 
                       static_cast<int>(posVec.size()), posStr.c_str());
            return false;
        }
        
        // 解析类型
        try {
            outDropType = std::stoi(posVec[0]);
        } catch (const std::exception& e) {
            LOG_WARNING("Failed to parse dropType: %s, error: %s", posVec[0].c_str(), e.what());
            return false;
        }
        
        // 解析第一个坐标
        if (posVec[1].length() < 2 || posVec[1].front() != '{' || posVec[1].back() != '}') {
            LOG_WARNING("Invalid format for first position: %s", posVec[1].c_str());
            return false;
        }
        
        std::string coordStr1 = posVec[1].substr(1, posVec[1].length() - 2);
        // 注意这里使用分号分隔
        if (sscanf(coordStr1.c_str(), "%d %d", &outDropPos1.x, &outDropPos1.z) != 2) {
            LOG_WARNING("Failed to parse first position: %s", coordStr1.c_str());
            return false;
        }
        
        // 解析第二个坐标
        if (posVec[2].length() < 2 || posVec[2].front() != '{' || posVec[2].back() != '}') {
            LOG_WARNING("Invalid format for second position: %s", posVec[2].c_str());
            return false;
        }
        
        std::string coordStr2 = posVec[2].substr(1, posVec[2].length() - 2);
        // 注意这里使用分号分隔
        if (sscanf(coordStr2.c_str(), "%d %d", &outDropPos2.x, &outDropPos2.z) != 2) {
            LOG_WARNING("Failed to parse second position: %s", coordStr2.c_str());
            return false;
        }
        
        return true;
    } catch (const std::exception& e) {
        LOG_WARNING("Exception while parsing position: %s, error: %s", posStr.c_str(), e.what());
        return false;
    }
}
