
#ifndef __CAMERA_MODEL_H__
#define __CAMERA_MODEL_H__

#include "OgrePrerequisites.h"
#include "SandboxEngine.h"
#include "AssetPipeline/Asset.h"
#include "BaseClass/RefObject.h"
#include "Graphics/Texture2D.h"
#ifdef ENABLE_PLAYER_CMD_COMMAND
#include "SandboxCallback.h"
#endif

namespace Rainbow {
	class EventContent;
	class Model;
}

class BaseItemMesh;
class GameCamera;


class EXPORT_SANDBOXENGINE CameraModel;
class CameraModel //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	CameraModel(int playerindex, int mutatemob=0, const char *customjson= "", const char *importmodelkey=nullptr, std::function<void(bool)> fun = nullptr);
	~CameraModel();

	void onEnterWorld(World *pworld);
	void onLeaveWorld();

	void resetAnim(int seqId);
	bool hasAnimSeq(int seqId);
	bool hasAnimPlaying(int seqId);
	bool hasAnimPlayEnd(int seqId);
	bool playHandAnim(int seqId, int loopMode = -1, float speed = 1.f, int layer = -1, float crossfade = -1.0f);
	void stopHandAnim(int seqId ,bool isreset =false);
	void stopHandAnim( bool isreset = false);
	bool SetHandAnimSpeed(int seqId, float speed);
	bool SetToolAnimSpeed(int seqId, float speed);
	bool addAnimFrameEvent(int seqId, int keyFrame, const std::string& eventName, long long objId);
	void clearAnimFrameEvent(long long objid = -1);
	void clearPendingAnimEvents(int seqId = -1);

	bool isShow()
	{
		return m_showModel;
	}
	void show(bool b);

	void setCurTool_byGrid(int itemid, const char *iconname, BackPackGrid* grid);
	void UpdateToolPos();

	void setCurDorsum(int itemid);
	void resetShakeTime();
	void update(float dtime, const Rainbow::WorldPos &eyepos, const Rainbow::Quaternionf &cam_rot);
	void tick();

	void playWeaponMotion(const char *motion, bool reset_play=true, int motion_class=0, float scale = 1.0f);
	void stopWeaponMotion(int motion_class); 

	void showMoveDir(bool b);
	void setMoveTarget(const WCoord &pos, int modeview=0);
	bool isShowMoveDir();
	int getMoveDirModeView();

	void playItemAnim(int anim, int loopMode = -1, float speed = 1.f, int layer = -1, float crossfade = -1.0f);
	void stopItemAnim(int anim);
	void stopAllItemAnim();
	int getPlayerIndex() {return m_PlayerIndex;}
	int getMutatemob() {return m_mutatemob;}
	const char* getCustomjson() {return m_customjson.c_str();}
	// Tool模型是否在播放动作
	bool hasToolAnimPlaying(int anim);

	void playItemMotion(const char *motion, bool reset_play=true, int motion_class=0, float scale = 1.0f);
	void stopItemMotion(int motion_class); 
	void switchItemMode(int index);

	float getCurrentShakeTime();
	float getCurrentShakeCycle();

	void applyBodyColor(unsigned int color);
	
	void updateToolModelTexture(int textureIndex = 0);//更改手持道具的模型贴图 code-by:liwentao

	//家园引导任务
	void showHomeTaskDir(bool b);
	bool isShowHomeTaskDir();
	void setHomeTarget(const WCoord &pos);
	void updateHomeTask(float dtime, const Rainbow::WorldPos &eyepos, const Rainbow::Quaternionf &cam_rot);

	//播放动画
	bool PlayActInHand(int actid, int playmode);

	bool IsHandModelLoaded()
	{
		return !m_HandModelIsLoading;
	}

	//目前只有【新枪】在用，因为它是异步加载
	bool IsWeaponModelLoaded()
	{
		return !m_WeaponModelIsLoading;
	}
	void SetWeaponModelIsLoading(bool b)
	{
		m_WeaponModelIsLoading = b;
	}

	bool m_EnbleHandShake;
	bool m_EnbleHandShakeLastFrame;
	Rainbow::Model *m_HandModel;
	Rainbow::Entity* m_HandEntity; // 持有手部模型的实体
	
	GameCamera* m_GameCamera;
	float m_ArmPitch;
	float m_ArmYaw;

	float m_PrePitch;
	float m_PreYaw;

	float m_RenderPitch;
	float m_RenderYaw;
	//tolua_end

	//临时方案，新枪实时换手部模型
	bool ChangeModelShow(bool normal, int itemid = 0);
	Rainbow::SharePtr<Rainbow::Asset> m_HandModelDataNormal{ nullptr };
	bool m_isHandModelDataNormal{ false };

	BaseItemMesh* m_ToolModel;

	void AttachToScene(Rainbow::GameScene* scene);

	void StartHandPosOffset(Rainbow::Vector3f offsetTarget, float time);
	void StopHandPosOffset(bool immediate = false);
	bool GetAdsMode() { return m_adsMode; }
	void SetAdsMode(bool b) { m_adsMode = b; }
	void HandPosOffsetDebugMode(bool b, float offsetUnit = 1.f);

	bool UsePrefabHandModel() { return m_bIsPrefabHand; }

	void SetHandPos(Rainbow::Vector3f pos);
	void SetHandScale(float scale);
	void SetHandBind(bool b);
	void SetGMYawPitch(float yaw, float pitch);

private:
	void setCurTool(int itemid, const char *iconname=NULL ,const char *userdatastr=NULL, bool hasRuneEffect = false, int userdataInt = 0);//int enchant_num=0, const int *enchants=NULL
	void AsyncLoadHandData();
	void OnHandModelResLoaded(const Rainbow::EventContent* data);
	void OnHandModelResLoadFaild(const Rainbow::EventContent* data);
	void UnRegistHandModelResEvent();
	void InitHandModelData();
	void ShowInner(bool b);
	void UpdateHandPosOffset(float dtime, Rainbow::Vector3f& offset);

private:
	Rainbow::Model *m_MoveDirective;
	int m_MoveDirViewMode;

	World *m_World;
	bool m_showModel;

	bool m_IsBlock = false;
	ItemInHandDef* m_ItemInHandDef = nullptr;

	Rainbow::Vector3f m_BaseOffset; // 手持偏移位置
	Rainbow::Vector3f m_BlockOffset; // 手持方块时偏移位置
	Rainbow::Vector3f* m_pItemOffset = nullptr; // 手持道具时偏移位置
	Rainbow::Vector3f* m_GMOffset = nullptr; // GM设置

	float m_GMScale;
	bool m_GMBind = true;
	float m_GMYaw = 0;
	float m_GMPitch = 0;

	WCoord m_MoveTarget;

	Rainbow::Vector3f m_ShakeVector;
	float m_CurrentShakeTime;
	Rainbow::Vector3f m_ShakePower;
	float m_ShakeCycle;
	float m_OrignalShakeCycle;

	float m_StartShakeLerpDuration;
	float m_EndShakeLerpDuration;
	float m_LerpStartMark;
	float m_LerpTimeAccumulate;

	Rainbow::Entity *m_DorsumEntity;

	int m_PlayerIndex;
	int m_mutatemob;
	std::string m_customjson; 
	std::string m_importModelKey;
	bool m_bIsImportModel;
	std::function<void(bool)> m_OnHandModelResLoadedCallback;
	Rainbow::Model *m_HomeDirective;
	bool m_IsShowDirective;

	Rainbow::SharePtr<Rainbow::Asset>			m_HandModelData;
	Rainbow::SharePtr<Rainbow::RefObject>		m_HandModelAnimData;
	Rainbow::SharePtr<Rainbow::RefObject>		m_HandModelAnimData1;
	Rainbow::SharePtr<Rainbow::Texture2D>		m_HandModelTex;
	int  m_HandAnimId = -1;
	BaseItemMesh* m_ToolModel_left;
	bool m_HandModelIsLoading;
	bool m_WeaponModelIsLoading;
	bool m_bIsPrefabHand;

	Rainbow::Vector3f m_HandPosOf_begin;
	Rainbow::Vector3f m_HandPosOf_end;
	float m_HandPosOf_accumTime;//累积时间
	float m_HandPosOf_totalTime;//总时间
	bool m_adsMode; //瞄准模式
	float m_lastCameraYaw;
	float m_lastCameraPitch;
	float m_curSlideHorValue;
	float m_curSlideVerValue;
	int8_t m_LOrR;
	int8_t m_DOrU;

#ifdef ENABLE_PLAYER_CMD_COMMAND
	MNSandbox::Callback m_userInputEventCallBack{ MNSandbox::Callback() };
	Rainbow::Vector3f m_eyePosOffDebugMode{ Rainbow::Vector3f::zero };
	float m_eyePosOffUnit{ 1.f };
	static bool debug_handPosOffset;
#endif
public:
	static bool IsDebugHandPosOffset();
	static bool debug_lookMyWeaponModel; //观看武器的模型
	static int ToolModelAnchorid; //主武器默认挂点

	float px = 0.f, py = 0.f, pz = 0.f;
	float rx = 0.f, ry = 0.f, rz = 0.f;
	float hscx = 0.4f, hscy = 0.4f, hscz = 0.4f;
}; //tolua_exports

#endif