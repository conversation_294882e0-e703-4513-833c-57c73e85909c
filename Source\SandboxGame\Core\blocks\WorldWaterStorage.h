#ifndef __CONTAINER_WATER_STORAGE_H__
#define __CONTAINER_WATER_STORAGE_H__

#include "container_world.h"
#include "container_erosion.h"


class WorldWaterStorage : public ErosionContainer//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	WorldWaterStorage();
	WorldWaterStorage(const WCoord& blockpos);
	virtual ~WorldWaterStorage() {};
	virtual int getObjType() const override
	{
		return OBJ_TYPE_EROSION_CONTAINER;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerWaterStorage;
	}

	virtual bool load(const void* srcdata);
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual void enterWorld(World* pworld) override;
	virtual void leaveWorld() override;
	virtual void updateTick() override;
	void addWater(int value);
	int getCurWaterVolume() { return m_nCurWaterVolume; }

	//void updateEffect();
	//void stopEffect();
	//void stopEffectByBlockdata(int blockdata); //
	//tolua_end
	void initCollectData(World* pworld);
private:
	//WCoord getEffectPos(int blockdata);

public:
private:
	unsigned short m_nCurWaterVolume = 0;
	unsigned short m_nTickCounter = 0;
	unsigned short m_nOnceTickMax = 0;
	unsigned short m_nOnceVolume = 0; 
	unsigned int m_nWaterVolumeMax = 0;

};//tolua_exports

#endif 