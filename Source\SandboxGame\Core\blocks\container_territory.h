#pragma once

#include "container_world.h"
#include "OgreBlock.h"
#include "OgreBezierCurve.h"
#include "TerritoryBoundingBox.h"

namespace Rainbow
{
	class EventContent;
	class GameObject;
}

/**********************************************************************************************
��    ����TerritoryContainer
��    �ܣ�ͼ��container �����Ƶ�
********************************************************************************************* */
class TerritoryContainer : public WorldStorageBox//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	TerritoryContainer();
	TerritoryContainer(const WCoord& blockpos);
	virtual ~TerritoryContainer();
	virtual int getObjType() const
	{
		return OBJ_TYPE_BOX;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerVillageTotemIce;
	}

	virtual bool load(const void* srcdata);
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual void enterWorld(World* pworld);
	virtual void leaveWorld();
	virtual void updateTick() override;
	virtual void updateDisplay(float dtime);
	WCoord BlockCenterCoordWithEvenOddOffset(const WCoord& grid);
	std::vector<WCoord> GetFlagPoint();
	bool HasFlagPoint(const WCoord& point);
	bool AddFlagPoint(const std::vector<WCoord>& points);
	bool AddFlagPoint(const WCoord& point);
	bool RemoveFlagPoint(const WCoord& point);
	bool ClearFlagPoint();


	std::vector<WORLD_ID> GetVillagersID();
	bool HasVillagersID(const WORLD_ID& villagerid);
	bool AddVillagerID(const std::vector<WORLD_ID>& villagerid);
	bool AddVillagerID(const WORLD_ID& villagerid);
	bool RemoveVillagerID(const WORLD_ID& villagerid);
	void SetUin(int uin) 
	{
		m_OwnerUin = uin;
	}
	int GetUin() 
	{
		return m_OwnerUin;
	}
	bool GetHaveAward() 
	{
		return m_haveAward;
	}
	void SetHaveAward(bool award);
	bool ClearVillagersID();
	void SwitchRender(World* pworld)
	{
		m_pworld = pworld;
		m_render = !m_render;
	}
	//tolua_end
	const char* getContainerName() const override {
		return "TerritoryContainer";
	}
	void UpdateEffect(World* pworld);

	void OnTriggerEnter(const Rainbow::EventContent* touch);
	void OnTriggerExit(const Rainbow::EventContent* touch);
	bool IsModfiy(int userID);
	struct ConfigInfo
	{
		int m_Uuid; //	uuid 
		//World m_WorldPos; // ��������  �����Ѿ���
		//int m_OwnerId; //ӵ����   m_OwnerUin �����Ѿ���
		int authorizeId[8];//��Ȩ����� ���8��
 	};

	// 新增领地边界盒相关方法
	bool IsPointInTerritoryBounds(const Rainbow::Vector3f& point) const;
	void SetTerritoryBounds(const Rainbow::Vector3f& center, const Rainbow::Vector3f& halfExtents);
	TerritoryBoundingBox GetTerritoryBounds() const;
	void ShowBoundingBox(bool show);

	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player);
	// 新增材料扣除接口
	bool DeductMaterial(const std::string& materialStr); // 扣除指定材料，格式："itemid|count"
	
	// 新增批量操作方法
	int GetAvailableMaterialCount(const std::string& materialStr); // 获取可用材料数量
	bool DeductMaterialBatch(const std::string& materialStr, int cycles); // 批量扣除材料

	// 添加通知附近方块的方法
	void NotifyNearbyBlocksOnDestroy();
	void NotifyNearbyBlocksOnCreate();  // 新增：领地创建时通知

private:
	WCoord getEffectPos();
	std::vector<WCoord> m_flagPoint;
	std::vector<WORLD_ID> m_villagersID;
	bool m_haveAward;

	std::string WorldIdToStr(WORLD_ID id);
	WORLD_ID StrToWorldId(std::string str);
	bool m_render;

	World* m_pworld;
	void DrawLine(World* pworld, const WCoord& BlockPos, const WCoord& distBlockPos);


	void InitTrigger();
	Rainbow::GameObject* m_GameObject = nullptr;

	// 定义边界盒大小的常量（完整尺寸，不是半尺寸）
	static const Rainbow::Vector3f DEFAULT_TERRITORY_BOX_SIZE;
	
	// 边界盒相关成员
	TerritoryBoundingBox m_boundingBox;
	bool m_showBoundingBox = false;

	// 安全的材料扣除方法
	bool SafeRemoveItemByCount(int itemid, int count);
};//tolua_exports
