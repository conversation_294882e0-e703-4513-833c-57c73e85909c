#include "AirDropEvent.h"
#include "WorldManager.h"
#include "GameNetManager.h"
#include "world.h"
#include "container_world.h"

#include "AirDropCraft.h"
#include "AirDropChest.h"
#include "WorldManager.h"
#include "world.h"
#include "ActorManagerInterface.h"
#include "ClientActorManager.h"
#include "ActorBall.h"
#include "Utilities/Logs/LogAssert.h"
#include "IClientGameManagerInterface.h"

#include <random>

#include "ClientAppProxy.h"
#ifndef M_PI
#define M_PI           3.14159265358979323846  /* pi */
#endif

// 定义静态成员变量
std::vector<AirChestInfo> AirDropEvent::chestList;

AirDropEvent::AirDropEvent(const WorldEventDef& config )
    : WorldEvent(config)
{
    // 生成空投位置
    generateDropLocation();

    // 生成飞机起始和终点位置
    genStartEndPos();


}
AirDropEvent::~AirDropEvent() {
    // 清理资源
    m_aircraft.reset();
    m_dropBox.reset();
}

void AirDropEvent::SpawnAircraft() {
    // // 创建飞机实例
    m_aircraft = std::make_unique<AirDropCraft>(m_eventdef, m_start_pos, m_end_pos);

    auto drop_pos = m_drop_pos;
    m_aircraft->SetDropPoint(drop_pos);
    m_aircraft->SetVisualEffect(m_visualEffectId);
    m_aircraft->SetSoundEffect(m_soundEffectId);
 }

void AirDropEvent::SpawnDropBox() {
    // 创建空投箱实例
    Rainbow::Vector3f spawnPos = m_aircraft->GetCurrentPosition();
    spawnPos.y -= 10;
    m_dropBox = std::make_unique<AirDropChest>(spawnPos, m_drop_pos, m_eventdef.Harm);
    m_dropBox->setChestId(m_eventdef.TreasureId);
    m_dropBox->setChestModleTyle(m_eventdef.ChestModel);
    
}


static std::string ToStr(float i) {
    int v = i/100;
    return std::to_string(v);
}

void AirDropEvent::BroadcastAnnouncement() {

    if (GetIClientGameManagerInterface()) {

        auto game = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame);

        if (game) {
            std::string msg =  m_eventdef.boardcast_msg; +
                + "\ndropPos:" + ToStr(m_drop_pos.x) + "," + ToStr(m_drop_pos.y) + "," + ToStr(m_drop_pos.z)
                + "\nstartPos:" + ToStr(m_start_pos.x) + "," + ToStr(m_start_pos.y) + "," + ToStr(m_start_pos.z)
                + "\nendPos:" + ToStr(m_end_pos.x) + "," + ToStr(m_end_pos.y) + "," + ToStr(m_end_pos.z);
            game->sendChat(msg.c_str(), 1, 0); //广播空投消息
        }
    }

}

void AirDropEvent::UpdateAircraftMovement(float deltaTime) {
    if (m_aircraft) {
        m_aircraft->Update(deltaTime);
    }
}

void AirDropEvent::UpdateBoxPhysics(float deltaTime) {
    if (m_dropBox) {
        m_dropBox->Update(deltaTime);
    }
}

void AirDropEvent::OnStart() {
    LOG_INFO("AirDropEvent::OnStart: %s", m_eventType.c_str());

    // 设置初始状态
    m_state = State::INITIALIZED;
    m_stateTimer = 0.0f;
    m_isActive = true;
}

void AirDropEvent::OnUpdate(float deltaTime) {
    m_stateTimer += deltaTime;

    switch (m_state) {
        case State::INITIALIZED:
#ifdef DEDICATED_SERVER
            if (!GetClientAppProxy()->IsServerInitOk())
                return;
#endif
            if (m_stateTimer >= 5.0f) {
                BroadcastAnnouncement();
                m_state = State::ANNOUNCED;
            }
            break;

        case State::ANNOUNCED:
            SpawnAircraft(); //空投飞机
            m_state = State::AIRCRAFT_SPAWNED;
            break;

        case State::AIRCRAFT_SPAWNED:
            UpdateAircraftMovement(deltaTime);
            if (m_aircraft && m_aircraft->ReachedDropPoint()) {
                SpawnDropBox();//空投箱子
                m_state = State::BOX_DROPPED;
            }
            break;

        case State::BOX_DROPPED:
            UpdateAircraftMovement(deltaTime);
            UpdateBoxPhysics(deltaTime);
            if (m_dropBox && m_dropBox->HasLanded()) {
                m_state = State::BOX_LANDED;

                /*AirChestInfo chestInfo;
                chestInfo.pos = m_dropBox->getChestPos();
                chestInfo.event_id = m_eventdef.ID;
                chestInfo.spawn_time = time(0);

                chestList.push_back(chestInfo);*/
            }

            break;

        case State::BOX_LANDED:
            UpdateAircraftMovement(deltaTime);
            if (m_stateTimer >= m_eventdef.TimeOut || m_aircraft->IsFinished()) {
                m_state = State::COMPLETED;
            }

            break;
        case State::COMPLETED:
            m_isActive = false;
            break;
    }
}

// 生成飞机空投点位置
bool AirDropEvent::generateDropLocation() {
    // 从配置加载位置生成规则
    const auto& def = m_eventdef;
    int maxheight = def.MaxHeight * 100;


    // 根据selection_type选择生成策略
    if (def.dropType == 1) {
        // 从预定义位置中随机选择
        Rainbow::Vector3f left_top = Rainbow::Vector3f(def.drop_pos1.x,0, def.drop_pos1.z);
        Rainbow::Vector3f right_botm = Rainbow::Vector3f(def.drop_pos2.x, 0, def.drop_pos2.z);

        m_drop_pos = generateRandomPointInRectangle(left_top, right_botm);

        m_drop_pos.x *= 100;
        m_drop_pos.y = maxheight;
        m_drop_pos.z *= 100;
    }

    return true;
}

void AirDropEvent::OnEnd() {
    // 清理资源
    m_isActive = false;
    m_aircraft.reset();
    m_dropBox.reset();
}

// 在一个矩形区域内生成一个随机点
Rainbow::Vector3f AirDropEvent::generateRandomPointInRectangle(const Rainbow::Vector3f& left_top, const Rainbow::Vector3f& right_botm)
{
    // 确保坐标范围正确
    int min_x = std::min(left_top.x, right_botm.x);
    int max_x = std::max(left_top.x, right_botm.x);
    int min_z = std::min(left_top.z, right_botm.z);
    int max_z = std::max(left_top.z, right_botm.z);

    // 创建随机数生成器
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<int> dist_x(min_x, max_x);
    std::uniform_int_distribution<int> dist_z(min_z, max_z);

    // 生成随机的x和z坐标
    int random_x = dist_x(gen);
    int random_z = dist_z(gen);

    // 返回随机生成的点，y坐标设为0
    return Rainbow::Vector3f(random_x, 0, random_z);
}


void AirDropEvent::genStartEndPos()
{
    const auto& def = m_eventdef;
    int maxheight = def.MaxHeight * 100;

    m_start_pos = Rainbow::Vector3f(0, 0, 0);
    m_end_pos = Rainbow::Vector3f(0, 0, 0);

    // 获取地图边界坐标（方块坐标）
    int startX, startZ, endX, endZ;
    startX = startZ = -def.MaxSizeX * 100;
    endX = endZ = def.MaxSizeZ * 100;

    // 计算地图大小
    int mapWidth = endX - startX;
    int mapLength = endZ - startZ;

    // 定义边界附近的区域宽度（地图边缘的10%区域）
    int borderWidth = mapWidth / 10;
    int borderLength = mapLength / 10;

    // 确保边界区域至少有一定宽度
    borderWidth = std::max(borderWidth, 500);  // 至少5个方块宽
    borderLength = std::max(borderLength, 500); // 至少5个方块长

    // 随机选择一个边界（0=上, 1=右, 2=下, 3=左）
    int startBorder = rand() % 4;

    // 安全的随机数生成函数
    auto safeRandom = [](int min, int max) -> int {
        if (min >= max) return min;
        return min + (rand() % (max - min));
    };

    // 根据选择的边界生成起始点坐标
    switch (startBorder) {
    case 0: // 上边界
        m_start_pos.x = safeRandom(startX, endX);
        m_start_pos.z = safeRandom(startZ, startZ + borderLength);
        break;

    case 1: // 右边界
        m_start_pos.x = safeRandom(endX - borderWidth, endX);
        m_start_pos.z = safeRandom(startZ, endZ);
        break;

    case 2: // 下边界
        m_start_pos.x = safeRandom(startX, endX);
        m_start_pos.z = safeRandom(endZ - borderLength, endZ);
        break;

    case 3: // 左边界
        m_start_pos.x = safeRandom(startX, startX + borderWidth);
        m_start_pos.z = safeRandom(startZ, endZ);
        break;
    }

    // 确保起始点不会超出地图边界
    m_start_pos.x = std::max(startX, std::min(endX, (int)m_start_pos.x));
    m_start_pos.z = std::max(startZ, std::min(endZ, (int)m_start_pos.z));

    // 设置Y坐标（高度），通常空投会从高空开始
    m_start_pos.y = maxheight;

    // 计算从起始点到空投点的方向向量
    Rainbow::Vector3f direction;
    direction.x = m_drop_pos.x - m_start_pos.x;
    direction.z = m_drop_pos.z - m_start_pos.z;

    // 计算方向向量的长度
    float length = std::sqrt(direction.x * direction.x + direction.z * direction.z);

    // 如果长度为0（起始点和空投点重合），则随机生成一个方向
    if (length < 0.001f) {
        float angle = (rand() % 360) * M_PI / 180.0f;
        direction.x = std::cos(angle);
        direction.z = std::sin(angle);
        length = 1.0f;
    }

    // 归一化方向向量
    direction.x /= length;
    direction.z /= length;

    // 计算终点位置（在起始点和空投点的连线延长线上）
    // 延长的距离为地图对角线长度，确保终点在地图外
    float extendDistance = std::sqrt(mapWidth * mapWidth + mapLength * mapLength) * 2;
    m_end_pos.x = m_drop_pos.x + direction.x * extendDistance;
    m_end_pos.z = m_drop_pos.z + direction.z * extendDistance;

    // 确保终点不会超出地图边界
    m_end_pos.x = std::max(startX, std::min(endX, (int)m_end_pos.x));
    m_end_pos.z = std::max(startZ, std::min(endZ, (int)m_end_pos.z));
    m_end_pos.y = maxheight;

    // 如果终点被限制在地图边界内，可能不再与起始点和空投点共线
    // 在这种情况下，我们需要重新调整方向，使三点尽可能接近共线
    if (m_end_pos.x != m_drop_pos.x + direction.x * extendDistance ||
        m_end_pos.z != m_drop_pos.z + direction.z * extendDistance) {

        // 计算从空投点到地图边界的最大距离
        float maxDistToEdge = 0.0f;

        // 检查与四个边界的交点
        // 上边界
        if (std::abs(direction.z) > 0.001f) {
            float t = (startZ - m_drop_pos.z) / direction.z;
            float x = m_drop_pos.x + t * direction.x;
            if (t > 0 && x >= startX && x <= endX) {
                float dist = t * std::sqrt(direction.x * direction.x + direction.z * direction.z);
                maxDistToEdge = std::max(maxDistToEdge, dist);
            }
        }

        // 下边界
        if (std::abs(direction.z) > 0.001f) {
            float t = (endZ - m_drop_pos.z) / direction.z;
            float x = m_drop_pos.x + t * direction.x;
            if (t > 0 && x >= startX && x <= endX) {
                float dist = t * std::sqrt(direction.x * direction.x + direction.z * direction.z);
                maxDistToEdge = std::max(maxDistToEdge, dist);
            }
        }

        // 左边界
        if (std::abs(direction.x) > 0.001f) {
            float t = (startX - m_drop_pos.x) / direction.x;
            float z = m_drop_pos.z + t * direction.z;
            if (t > 0 && z >= startZ && z <= endZ) {
                float dist = t * std::sqrt(direction.x * direction.x + direction.z * direction.z);
                maxDistToEdge = std::max(maxDistToEdge, dist);
            }
        }

        // 右边界
        if (std::abs(direction.x) > 0.001f) {
            float t = (endX - m_drop_pos.x) / direction.x;
            float z = m_drop_pos.z + t * direction.z;
            if (t > 0 && z >= startZ && z <= endZ) {
                float dist = t * std::sqrt(direction.x * direction.x + direction.z * direction.z);
                maxDistToEdge = std::max(maxDistToEdge, dist);
            }
        }

        // 如果找到了有效的交点，重新计算终点
        if (maxDistToEdge > 0.0f) {
            m_end_pos.x = m_drop_pos.x + direction.x * maxDistToEdge;
            m_end_pos.z = m_drop_pos.z + direction.z * maxDistToEdge;
        }
    }
}

/**
 * 检测是否刚刚从黑夜变为白天
 * 此函数设计为每秒调用一次
 *
 * @return 1 如果刚刚天亮，0 如果没有变化或不是天亮时刻
 */
int AirDropEvent::isDaytimeStart() {
    // 静态变量，记录上一次检查时是否为夜晚
    static bool wasNighttime = true; // 初始假设为夜晚
    static bool isFirstCheck = true; // 是否是第一次检查
    if (!g_WorldMgr) return 0;

    // 获取当前世界
    World* pWorld = g_WorldMgr->getWorld(0);
    if (!pWorld) return 0;

    // 获取当前是否为白天
    bool isDay = pWorld->isDaytime();

    // 第一次检查时，只记录状态，不触发事件
    if (isFirstCheck) {
        wasNighttime = !isDay;
        isFirstCheck = false;
        return 0;
    }

    // 检测从夜晚到白天的转变
    int result = 0;
    if (wasNighttime && isDay) {
        // 刚刚从夜晚变为白天
        LOG_INFO("Day has started! Time for daytime events.");
        result = 1;
    }

    // 更新状态记录
    wasNighttime = !isDay;

    return result;
}

void AirDropEvent::ClearChestList()
{

    std::vector<AirChestInfo> del_chestList;

    // 数量为0,并且没有打开,移除
    for (auto it = chestList.begin(); it != chestList.end();) {
        auto&  chest  = *it;
        if (chest.getCount() == 0 && !chest.isOpenning()) {
            LOG_INFO("remove1 chest pos=%d,%d,%d count=0",chest.pos.x,chest.pos.y,chest.pos.z);
            del_chestList.push_back(chest);
            //RemoveChest(chest.pos);
            it = chestList.erase(it);
        }else{
            ++it;
        }
    }

    // 天亮了,宝箱清零
    if ( isDaytimeStart() ){
        for (auto it = chestList.begin(); it != chestList.end();) {
            auto&  chest  = *it;
            if ( !chest.isOpenning()) {
                LOG_INFO("remove2 chest pos=%d,%d,%d daystart", chest.pos.x, chest.pos.y, chest.pos.z);
                del_chestList.push_back(chest);
                RemoveChest(chest.pos);
                it = chestList.erase(it);
            }else{
                ++it;
            }

        }
    } 

    //通知前端宝箱删除
    if(del_chestList.size() > 0){

        LOG_INFO("AirDropEvent_ChestRemove chestList.size=%d", del_chestList.size());
        
        PB_AIRDROP_DelChest pbData;
        
        for (auto& chest : del_chestList) {
            
            ChestItem* item = pbData.add_list();
            item->set_eventid(chest.event_id);
            item->set_spawntime(chest.spawn_time);
            item->mutable_pos()->set_x(chest.pos.x);
            item->mutable_pos()->set_y(chest.pos.y);
            item->mutable_pos()->set_z(chest.pos.z);	
        }

        if (GetGameNetManagerPtr()) {
            GetGameNetManagerPtr()->sendBroadCast(PB_AIRDROP_DEL_CHEST_HC, pbData);
        }
         
    }


}
void AirDropEvent::RemoveChest(WCoord pos)
{
    if (g_WorldMgr && g_WorldMgr->getWorld(0)) {
        auto pWorld = g_WorldMgr->getWorld(0);
        bool isday = pWorld->isDaytime();

        // 1. 先获取宝箱容器
        WorldContainerMgr* containerMgr = dynamic_cast<WorldContainerMgr*>(pWorld->getContainerMgr());
        if (containerMgr) {
            // 2. 获取宝箱对象
            WorldStorageBox* box = dynamic_cast<WorldStorageBox*>(containerMgr->getContainer(pos));
            if (box) {
                // 3. 清空宝箱内容（不会掉落物品）
                box->clear();
            }

            // 4. 设置方块为空气，销毁宝箱方块
            pWorld->setBlockAir(pos);

            // 5. 销毁宝箱容器
            containerMgr->destroyContainer(pos);
        }
    }
}

int AirChestInfo::getCount() {

    if (g_WorldMgr && g_WorldMgr->getWorld(0)) {
        auto pWorld = g_WorldMgr->getWorld(0);
        // 1. 先获取宝箱容器
        WorldContainerMgr* containerMgr = dynamic_cast<WorldContainerMgr*>(pWorld->getContainerMgr());
        if (containerMgr) {
            // 2. 获取宝箱对象
            WorldStorageBox* box = dynamic_cast<WorldStorageBox*>(containerMgr->getContainer(pos));
            if (box) {
                // 计算所有物品的总数量
                int totalCount = 0;
                int maxGrids = box->getGridCount();

                for (int i = 0; i < maxGrids; i++) {
                    BackPackGrid* grid = box->index2Grid(i + STORAGE_START_INDEX);
                    if (grid && !grid->isEmpty()) {
                        totalCount += grid->getNum();
                    }
                }

                return totalCount;
            }
        }
    }

    return 0;
}

bool AirChestInfo::isOpenning() {
    if (g_WorldMgr && g_WorldMgr->getWorld(0)) {
        auto pWorld = g_WorldMgr->getWorld(0);
        // 获取容器管理器
        WorldContainerMgr* containerMgr = dynamic_cast<WorldContainerMgr*>(pWorld->getContainerMgr());
        if (containerMgr) {
            // 获取宝箱对象
            WorldStorageBox* box = dynamic_cast<WorldStorageBox*>(containerMgr->getContainer(pos));
            if (box) {
                // 检查宝箱是否正在被打开
                bool isBeingOpened = box->isOpenning();
                return isBeingOpened;
            }
        }
    }
    return false;
}