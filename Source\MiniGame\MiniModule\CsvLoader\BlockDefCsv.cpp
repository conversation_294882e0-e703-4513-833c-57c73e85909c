#include "BlockDefCsv.h"
#include "OgreUtils.h"
#include "ItemDefCsv.h"
#include "ModManager.h"
#include "Common/OgreStringUtil.h"

using MINIW::CSVParser; 
IMPLEMENT_LAZY_SINGLETON(BlockDefCsv)

extern bool gFunc_IsDomesticVer();

BlockDefCsv::BlockDefCsv() :m_MaxBlockID(0)
{ 
	clearTempDef();

	m_BlockDefTable.resize(SOC_BLOCKID_MAX, NULL);
	m_BlockValuableId.clear();
	m_BlockValuableId.reserve(3000);
}

BlockDefCsv::~BlockDefCsv() 
{ 
	onClear();
} 

void BlockDefCsv::clearTempDef()
{
	memset(m_TempDefArr, 0, sizeof(m_TempDefArr));
}

void BlockDefCsv::removeDef(int id)
{
	ENG_DELETE_LABEL(m_BlockDefTable[id], kMemConfigDef);
	m_BlockDefTable[id] = NULL;
	if (id < SOC_BLOCKID_MAX)
	{
		m_TempDefArr[id] = NULL;
	}
	removeValueId(id);
}

void BlockDefCsv::removeTempDef(int id)
{
	if (id >= 0 && id < SOC_BLOCKID_MAX)
	{
		m_TempDefArr[id] = NULL;
	}
}

void BlockDefCsv::onParse(CSVParser& parser) 
{ 
	parser.SetTitleLine(1);
	bool isDomestic = gFunc_IsDomesticVer();
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if (id == 0 && (parser[i]["ID"].Str())[0] == 0) continue;
		if (id >= SOC_BLOCKID_MAX) continue;

		int regionType = parser[i]["RegionType"].Int();
		if ((isDomestic && regionType == REGION_TYPE_UNIVERSE) || (!isDomestic && regionType == REGION_TYPE_DOMESTIC)) 
			continue;

		BlockDef *def = ENG_NEW_LABEL(BlockDef, kMemConfigDef);
		//memset(def, 0, sizeof(BlockDef));
		if(id > m_MaxBlockID) m_MaxBlockID = id;

		def->CopyID = 0;
		def->gamemod = nullptr;
		
		def->ID = id;
		def->EditType = parser[i]["EditType"].Byte();
		def->ModelIndex = parser[i]["ModelIndex"].UInt();
		def->PlaceDir = parser[i]["PlaceDir"].Byte();
		def->Replaceable = parser[i]["Replaceable"].Byte();
		def->ClickCollide = parser[i]["ClickCollide"].Byte();
		def->MoveCollide = parser[i]["MoveCollide"].Byte();
		def->BlockFlow = parser[i]["BlockFlow"].Byte();
		def->PushFlag = parser[i]["PushFlag"].Byte();
		def->GravityEffect = parser[i]["GravityEffect"].Byte();
		def->AntiExplode = parser[i]["AntiExplode"].Short();
		def->MaxHP = parser[i]["MaxHP"].Short();
		if (def->MaxHP <= 0) def->MaxHP = 100; // 默认给个100

		def->Hardness = parser[i]["Hardness"].Float();
		// 调整为减伤，万分比
		if (def->Hardness > 0)
		{
			def->Hardness = def->Hardness / 10000.0f;
		}
		def->Slipperiness = parser[i]["Slipperiness"].Float();
		def->BurnSpeed = parser[i]["BurnSpeed"].Byte();
		def->CatchFire = parser[i]["CatchFire"].Byte();
		def->CorrodeSpeed = parser[i]["CorrodeSpeed"].Int();
		def->PowerState = parser[i]["PowerState"].Byte();
		def->CoverNeighbor = parser[i]["CoverNeighbor"].Byte();
		def->LightAtten = parser[i]["LightAtten"].Byte();
		def->LightSrc = parser[i]["LightSrc"].Byte();
		def->UseNeighborLight = parser[i]["UseNeighborLight"].Byte();
		def->Height = parser[i]["Height"].Byte();
		def->IsTemplate = parser[i]["IsTemplate"].Bool();
		def->ToolLevel = parser[i]["ToolLevel"].Byte();
		def->Breakable = parser[i]["Breakable"].Bool();
		def->TriggerType = parser[i]["TriggerType"].Byte();
		def->TerrainEditor = parser[i]["TerrainEditor"].Byte();
		//官方是高清贴图，默认线性采样
		def->SampleMode = 0;

		for(int d=0; d<MAX_TOOLMINE_DROP; d++)
		{
			char colname[64];
			sprintf(colname, "ToolMineDrop%d", d+1);
			def->ToolMineDrops[d].item = parser[i][colname].UInt();
			sprintf(colname, "ToolMineProb%d", d+1);
			def->ToolMineDrops[d].odds = parser[i][colname].UInt();
		}

		def->HandMineDrops.item = parser[i]["HandMineDrop"].UInt();
		def->HandMineDrops.odds = parser[i]["HandMineProb"].UInt();

		def->PreciseDrop = parser[i]["PreciseDrop"].UInt();
		def->MineTool = parser[i]["MineTool"].Byte();

		def->DropExp = parser[i]["MineExp"].Byte();
		def->DropExpOdds = parser[i]["MineExpOdds"].UShort();


		vector<string> vRandomColors;
		vRandomColors.clear();
		std::string sLeavesColor = parser[i]["RandomColors"].Str();
		Rainbow::StringUtil::split(vRandomColors, sLeavesColor, "|");
		if (sLeavesColor.size() != 0)
		{
			for (int idx = 0; idx < MAX_BIOME_LEAVES_COLOR && idx < (int)vRandomColors.size(); idx++)
			{
				unsigned inColor = 0;
				sscanf(vRandomColors[idx].c_str(), "%x", &inColor);
				def->RandomColors[idx] = Rainbow::ColorRGBA32((inColor & 0xff0000) >> 16, (inColor & 0x00ff00) >> 8, (inColor & 0x0000ff), 255);
			}
		}
		else 
		{
			for (int idx = 0; idx < MAX_BIOME_LEAVES_COLOR; idx++)
			{
				def->RandomColors[idx] = Rainbow::ColorRGBA32::white;
			}
		}

		unsigned int mcolor = 0;
		sscanf(parser[i]["MiniColor"].Str(), "%x", &mcolor);
		def->MiniColor = ((mcolor&0xff)<<16) | (mcolor&0xff00) | ((mcolor&0xff0000)>>16);

		def->Score = parser[i]["Score"].Float();
		def->TextureGroup = parser[i]["TextureGroup"].Byte();
		def->UserData[0] = parser[i]["UserData1"].Int();

		//MyStringCpy(def->Name, sizeof(def->Name), parser[i]["Name"].Str());
		
		//名称要去item表获取 item表加载的时候加了刷新接口
		def->Name = ColumnLang(parser[i], "Name");
		//MyStringCpy(def->EnglishName, sizeof(def->EnglishName), parser[i]["ENName"].Str());
		//MyStringCpy(def->Type, sizeof(def->Type), parser[i]["Type"].Str());
		//MyStringCpy(def->Texture1.c_str(), sizeof(def->Texture1.c_str()), parser[i]["Texture1"].Str());
		//MyStringCpy(def->Texture2, sizeof(def->Texture2), parser[i]["Texture2"].Str());
		def->EnglishName = parser[i]["Key"].Str(); // BlockDef ENName -> Key
		def->Type = parser[i]["Type"].Str();
		def->Texture1 = parser[i]["Texture1"].Str();
		def->Texture2 = parser[i]["Texture2"].Str();
		
		def->WalkSound = parser[i]["WalkSound"].Str();
		def->DigSound = parser[i]["DigSound"].Str();
		def->PlaceSound = parser[i]["PlaceSound"].Str();
		def->EffectName = parser[i]["EffectName"].Str();
		def->BluePrintFlag = parser[i]["BluePrintFlag"].Byte();
		def->ConvertID = parser[i]["ConvertID"].UInt();
		def->PhyCollide = parser[i]["PhyCollide"].Byte();
		def->CustommodelAlpha = parser[i]["CustommodelAlpha"].Byte();
		def->CustommodelLight = parser[i]["CustommodelLight"].Byte();
		def->MultiGridsBlockType = parser[i]["MultiGridsBlockType"].Bool();
		def->GrowthTempRange = parser[i]["GrowthTempRange"].Byte();
		def->TempAtten = parser[i]["TempAtten"].Short();
		def->TempSrc = parser[i]["TempSrc"].Short();
		def->EnablePlaceSnow = parser[i]["EnablePlaceSnow"].UInt();
		def->CropsSign = parser[i]["CropsSign"].UShort();

		vector<string> vDepth;
		std::string sDepth = parser[i]["GrowthTime"].Str();
		Rainbow::StringUtil::split(vDepth, sDepth, "|");
		if (vDepth.size() != 0)
		{
			def->GrowthTimeNum = static_cast<short>(atoi(vDepth[0].c_str()));
			assert(def->GrowthTimeNum <= vDepth.size() - 1);
			assert(vDepth.size() - 1 <= MAX_GROWTH_TIME);
			for (int i = 1; i < vDepth.size(); ++i)
			{
				def->GrowthTime[i-1]= static_cast<short>(atoi(vDepth[i].c_str()));
			}
			
		}

		def->Tenacity = parser[i]["Tenacity"].Float(); //韧度值，用在投掷物是否能击碎和穿透
		if (parser[i]["LOD"].IsValid())
		{
			def->LOD = parser[i]["LOD"].UShort();
		}
		if (parser[i]["SupportCastShadow"].IsValid())
		{
			def->SupportCastShadow = parser[i]["SupportCastShadow"].Bool();
		}

		ENG_DELETE_LABEL(m_BlockDefTable[id], kMemConfigDef);
		m_BlockDefTable[id] = def;
		m_BlockValuableId.push_back(id);
	}
	m_BlockValuableId.reserve(m_BlockValuableId.size());
	clearTempDef();
} 
void BlockDefCsv::onClear() 
{ 
	Rainbow::DeletePointerArrayLabel(m_BlockDefTable, kMemConfigDef);
	m_MaxBlockID = 0;
	clearTempDef();
	m_BlockValuableId.clear();
} 
const char* BlockDefCsv::getName() 
{ 
    return "blockdef"; 
} 
const char* BlockDefCsv::getClassName() 
{ 
    return "BlockDefCsv"; 
} 
int BlockDefCsv::getMaxID()
{
	return m_MaxBlockID;
}
int BlockDefCsv::getNum()
{
	return (int)m_BlockDefTable.size();
}

BlockDef* BlockDefCsv::get(int id, bool takeplace/* = true*/)
{
	load(); //如果未加载则先加载 code_by:huangfubin
	// 20210826: 数据缓存优化 codeby:liusijia
	BlockDef* blockDef = nullptr;
	if (id >= 0 && id < SOC_BLOCKID_MAX)
	{
		blockDef = m_TempDefArr[id];
	}

	if (blockDef == nullptr)
	{
		blockDef = getRaw(id, takeplace);
		if (id >= 0 && id < SOC_BLOCKID_MAX)
		{
			m_TempDefArr[id] = blockDef;
		}
	}

	return blockDef;
}


BlockDef* BlockDefCsv::getRaw(int id, bool takeplace) 
{
	int tmpid = id;
	//assert(id >= 0 && id < SOC_BLOCKID_MAX);//SOC 方块ID 范围 0-4095 不支持扩展id
	if (id < 0 || id >= SOC_BLOCKID_MAX)
	{
		//临时处理。以后是否一直这样。看策划
		tmpid = 101;
		//return nullptr;
	}

	//沙盒newdef 缓存//code-by: hanyunqiang
	BlockDef* blockDef = g_BlockMtlMgr.GetBlockNewDef(tmpid);
	if (blockDef)
	{
		return blockDef;
	}
	blockDef = g_ModMgr.tryGetBlockDef(tmpid);
	if (blockDef)
	{
		return blockDef;
	}

	if (m_BlockDefTable[tmpid])
		return m_BlockDefTable[tmpid];
	else//临时判断。
	{
		return get(ITEM_DEFAULT);
	}
	
	return nullptr;
}

void BlockDefCsv::updateName(int id, const std::string& name)
{
	if(id >= 0 && id < int(m_BlockDefTable.size()))
	{
		if (m_BlockDefTable[id])
			m_BlockDefTable[id]->Name = name.c_str();
	}
	return;
}

const BlockDef *BlockDefCsv::getOrigin(int id)
{	
	assert(id >= 0 && id < SOC_BLOCKID_MAX);//SOC 方块ID 范围 0-4095 不支持扩展id
	if (id < 0 || id >= SOC_BLOCKID_MAX) return m_BlockDefTable[101];//临时处理。以后是否一直这样。看策划
	return m_BlockDefTable[id];
}


BlockDef* BlockDefCsv::addByCopy(int id, int type, int copyId)
{
	BlockDef* templateDef = get(copyId);
	BlockDef* pDef = add(id, type, templateDef);
	if (pDef)
	{
		pDef->CopyID = copyId;
	}
	return pDef;
}

BlockDef* BlockDefCsv::add(int id, int type, BlockDef* templateDef, std::string texture2, std::string name, std::string desc)
{
	//SOC 方块ID 范围 0-4095 不支持扩展id
	return nullptr;

	// if (!templateDef)
	// 	return nullptr;

	// if (id < 0 || (id >= SOC_BLOCKID_MAX && id < EX_BLOCKID_MIN) || id > EX_BLOCKID_MAX)
	// 	return nullptr;

	// int defaultId = 2000;
	// if (type == FULLY_BLOCK_MODEL)
	// 	defaultId = 1999;

	// BlockDef* templateBlockDef = templateDef;
	// BlockDef* def = ENG_NEW_LABEL(BlockDef, kMemConfigDef);
	// if (id > m_MaxBlockID) m_MaxBlockID = id;

	// def->CopyID = 0;
	// def->gamemod = nullptr;

	// def->ID = id;
	// def->EditType = templateBlockDef->EditType;
	// def->ModelIndex = templateBlockDef->ModelIndex;
	// def->PlaceDir = templateBlockDef->PlaceDir;
	// def->Replaceable = templateBlockDef->Replaceable;
	// def->ClickCollide = templateBlockDef->ClickCollide;
	// def->MoveCollide = templateBlockDef->MoveCollide;
	// def->BlockFlow = templateBlockDef->BlockFlow;
	// def->PushFlag = templateBlockDef->PushFlag;
	// def->GravityEffect = templateBlockDef->GravityEffect;
	// def->AntiExplode = templateBlockDef->AntiExplode;
	// def->Hardness = templateBlockDef->Hardness;
	// def->Slipperiness = templateBlockDef->Slipperiness;
	// def->BurnSpeed = templateBlockDef->BurnSpeed;
	// def->CatchFire = templateBlockDef->CatchFire;
	// def->CorrodeSpeed = templateBlockDef->CorrodeSpeed;
	// def->PowerState = templateBlockDef->PowerState;
	// def->CoverNeighbor = templateBlockDef->CoverNeighbor;
	// def->LightAtten = templateBlockDef->LightAtten;
	// def->LightSrc = templateBlockDef->LightSrc;
	// def->UseNeighborLight = templateBlockDef->UseNeighborLight;
	// def->Height = templateBlockDef->Height;
	// def->IsTemplate = templateBlockDef->IsTemplate;
	// def->ToolLevel = templateBlockDef->ToolLevel;
	// def->Breakable = templateBlockDef->Breakable;
	// def->CorrodeSpeed = templateBlockDef->CorrodeSpeed;

	// //官方是高清贴图，默认线性采样
	// def->SampleMode = 0;

	// for (int d = 0; d < MAX_TOOLMINE_DROP; d++)
	// {
	// 	if (d == 0)
	// 		def->ToolMineDrops[d].item = id;
	// 	else
	// 		def->ToolMineDrops[d].item = templateBlockDef->ToolMineDrops[d].item;
	// 	def->ToolMineDrops[d].odds = templateBlockDef->ToolMineDrops[d].odds;
	// }

	// def->HandMineDrops.item = id;
	// def->HandMineDrops.odds = templateBlockDef->HandMineDrops.odds;

	// def->PreciseDrop = templateBlockDef->PreciseDrop;
	// def->MineTool = templateBlockDef->MineTool;

	// def->DropExp = templateBlockDef->DropExp;
	// def->DropExpOdds = templateBlockDef->DropExpOdds;

	// def->MiniColor = templateBlockDef->MiniColor;

	// def->Score = templateBlockDef->Score;
	// def->TextureGroup = templateBlockDef->TextureGroup;
	// def->UserData[0] = templateBlockDef->UserData[0];

	// def->Name = templateBlockDef->Name;
	// def->EnglishName = templateBlockDef->EnglishName;
	// def->Type = templateBlockDef->Type;
	// def->Texture1 = templateBlockDef->Texture1;
	// def->Texture2 = templateBlockDef->Texture2;
	// def->WalkSound = templateBlockDef->WalkSound;
	// def->DigSound = templateBlockDef->DigSound;
	// def->PlaceSound = templateBlockDef->PlaceSound;
	// def->EffectName = templateBlockDef->EffectName;
	// def->BluePrintFlag = templateBlockDef->BluePrintFlag;
	// def->ConvertID = templateBlockDef->ConvertID;
	// def->CustommodelAlpha = templateBlockDef->CustommodelAlpha;
	// def->CustommodelLight = templateBlockDef->CustommodelLight;
	// def->MultiGridsBlockType = templateBlockDef->MultiGridsBlockType;
	// def->EnablePlaceSnow = templateBlockDef->EnablePlaceSnow;
	// ENG_DELETE_LABEL(m_BlockDefTable[id], kMemConfigDef);
	// m_BlockDefTable[id] = def;
	// if (id < 4096)
	// {
	// 	m_TempDefArr[id] = NULL;// 清空指定缓存
	// }

	// if (!texture2.empty())
	// 	m_BlockDefTable[id]->Texture2 = texture2.c_str();

	// if (type == IMPORT_BLOCK_MODEL)
	// 	def->Type = "importmodel";

	// ItemDefCsv::getInstance()->add(id, type, texture2, name, desc);
	// {
	// 	//没找到就加入
	// 	auto iter = std::find(m_BlockValuableId.begin(), m_BlockValuableId.end(), id);
	// 	if (iter == m_BlockValuableId.end())
	// 	{
	// 		m_BlockValuableId.push_back(id);
	// 	}
	// }
	// return m_BlockDefTable[id];
}

BlockDef *BlockDefCsv::add(int id, int type, std::string texture2/* ="" */, std::string name/* ="" */, std::string desc/* ="" */)
{
	//SOC 方块ID 范围 0-4095 不支持扩展id
	return nullptr;

	// if (id < 0 || (id >= SOC_BLOCKID_MAX && id < EX_BLOCKID_MIN) || id > EX_BLOCKID_MAX)
	// 	return NULL;

	// int defaultId = 2000;
	// if (type == FULLY_BLOCK_MODEL)
	// 	defaultId = 1999;

	// BlockDef* templateBlockDef = get(defaultId);
	// if(templateBlockDef == NULL) return NULL;
	
	// return add(id, type, templateBlockDef, texture2, name, desc);
}

int BlockDefCsv::getValueIdNum()
{
	return m_BlockValuableId.size();
}

int BlockDefCsv::getIdByIndex(int index)
{
	if (index < 0 || index >= m_BlockValuableId.size())
	{
		return -1;
	}
	return m_BlockValuableId[index];
}

BlockDef* BlockDefCsv::getDefByValueIndex(int index, bool takeplace)
{
	int id = getIdByIndex(index);
	return get(id, takeplace);
}

bool BlockDefCsv::addModBlockDef(int id, BlockDef* def)
{
	if (id < 0 || id >= m_BlockDefTable.size())
	{
		return false;
	}

	//插件会动态修改的,还是把筛选加上吧.
	auto iter = std::find(m_BlockValuableId.rbegin(), m_BlockValuableId.rend(), id);
	if (m_BlockValuableId.rend() == iter)
	{
		m_BlockValuableId.push_back(id);
	}

	//if (def)
	//{
	//	if (m_BlockDefTable[id])
	//		ENG_DELETE_LABEL(m_BlockDefTable[id], kMemConfigDef);

	//	m_BlockDefTable[id] = ENG_NEW_LABEL(BlockDef, kMemConfigDef);
	//	m_BlockDefTable[id]->copy(def);
	//}

	return true;
}

bool BlockDefCsv::removeModBlockDef(int id)
{
	if (id < 0 || id >= m_BlockDefTable.size())
	{
		return false;
	}
	/*if (!m_BlockDefTable[id])
	{
		return false;
	}
	ENG_DELETE(m_BlockDefTable[id]);
	m_BlockDefTable[id] = nullptr;*/
	removeValueId(id);
	return true;
}

void BlockDefCsv::removeValueId(int id)
{
	//if (id > 2000 && id < SOC_BLOCKID_MAX)//SOC 方块ID 范围 0-4095 不支持扩展id
	//{
	//	auto iter = std::find(m_BlockValuableId.rbegin(), m_BlockValuableId.rend(), id);
	//	if (iter != m_BlockValuableId.rend())
	//	{
	//		m_BlockValuableId.erase(std::next(iter).base());
	//	}
	//}
}