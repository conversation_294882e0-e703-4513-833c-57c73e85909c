#pragma once

#include "Math/Vector3f.h"
#include "Math/Quaternionf.h"
#include "VehicleAssembleLocoMotion.h"
#include "PlayerInputHandler.h"
#include "Misc/InputEvent.h"
#include "SandboxGame.h"
#include "KeyPressDetector.h"

class EXPORT_SANDBOXGAME PlayerInputHelper;
class PlayerInputHelper //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	PlayerInputHelper();
	bool getVehiclePhysActorPos(VehicleAssembleLocoMotion* loc, Rainbow::Vector3f& pos, Rainbow::Quaternionf& q);
	virtual void onUpdate(float dtime);
	//bool KeyBanInVehicleMode();
	void triggerPCVehicleControlKeys(const Rainbow::InputEvent& inevent);
	int getCurForwardKeyCode();
	void debugRender();
	void SwitchChannelEngine();
	void SwitchChannelWheel();
	
	// 获取按键检测器
	//tolua_end

	void tick();
	KeyPressDetector* getEKeyDetector() { return &m_eKeyDetector; }
	KeyPressDetector* getRKeyDetector() { return &m_rKeyDetector; }
	// 获取鼠标右键检测器
	KeyPressDetector* getRightClickDetector() { return &m_rightClickDetector; }
	
protected:
	int m_KeyDownMark[6];
	int m_RunKeyDownButton;

	int m_keyGuideStep;

	unsigned char m_SpeedUpTimes;
	
	// 按键检测器 (当前用于E键)
	KeyPressDetector m_eKeyDetector;
	// 按键检测器 (用于R键)
	KeyPressDetector m_rKeyDetector;
	// 按键检测器 (用于鼠标右键长按)
	KeyPressDetector m_rightClickDetector;
}; //tolua_exports
