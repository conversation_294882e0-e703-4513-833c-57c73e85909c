-- local memoryReferenceInfo = require("MemoryReferenceInfo")
-- memoryReferenceInfo.m_cConfig.m_bAllMemoryRefFileAddTime = false
curSelectGridIndex = -1;
curBattleState = 0;
curSelectGridSid = "" --用在家园 pc记录sid 服务器id 方便快捷键操作
local curPierceState = false
local curPerspectiveState = false

function CancelOperateUI(level)
	local chatinput = getglobal("ChatInputFrame")
	if chatinput:IsShown() then
		chatinput:Hide()
		return true
	end

	local craftframe = getglobal("CraftingTableFrame");
	if craftframe:IsShown() then
		craftframe:Hide();
		return true
	end

	local bpframe = getglobal("BackpackFrame");
	if bpframe:IsShown() then
		bpframe:Hide();
		return true
	end

	local StorageBoxFrame = getglobal("StorageBoxFrame");
	if StorageBoxFrame:IsShown() then
		StorageBoxFrame:Hide()
		return true
	end

	return false
end

-- 是否可以使用快捷键打开界面
function CanUseUIShortCut()
	local forbid_ui = {"itemrepairAutoGen","decompositionAutoGen"}
	local uimanager = GetInst("MiniUIManager")
	for _, uiname in pairs(forbid_ui) do
		if uimanager:IsShown(uiname) then
			return false
		end
	end
	return true
end

function AccelKey_Backpack()
	if GetClientInfo():isEditorMode() then
		return
	end
	
	if not CanUseUIShortCut() then return end

	-- 高级编辑，不显示老背包
	if IsUGCEditing() then
		return
	end
	-- 处于截图状态，不能打开背包
	if GetInst("MiniUIManager"):IsShown("SceneShotFrameAutoGen") then
		return;
	end
	
	if GetRecordPkgManager():isRecordPlaying() then
	   return;
	end
	if IsUIFrameShown("MapRuleStartSettingScreenShotFrame") then
		return;
	end
	if GetInst("ModsLibPkgManager"):IsModslibPkgScreenShotFrameShown() then
		return;
	end
	if IsUIFrameShown("DeveloperModeSet") then
		return;
	end
	if ClientGameMgr:getCurGame():isInGame() then
		--如果使用了ui库,ui库里设置了背包隐藏，则快捷键打开背包失效
		if UIEditorDef and UIEditorDef:rootNodeIsExist() then
			if not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.SHORTCUT) then
				return;
			end
		end

		if CurMainPlayer:isInSpectatorMode() == true then
			return;
		end
		if ClientCurGame:isOperateUI() then
			if CancelOperateUI(0) then 
			end
		end

		--工具模式:打开、关闭对象库
		if CurWorld and CurWorld:isGameMakerToolMode() then
			press_btn("ToolModeFrameToolFrameObjLib");
			return;
		end
		if IsInHomeLandMap and IsInHomeLandMap() then
			if GetInst("UIManager"):GetCtrl("HomelandBackpack","uiCtrlOpenList") then
				GetInst("UIManager"):Hide("HomelandBackpack")
			else
				--家园 就打开家园背包
				--先关闭其他界面
				if IsUIFrameShown("HomelandStove") then
					GetInst("UIManager"):Close("HomelandStove")
				end
				GetInst("UIManager"):Open("HomelandBackpack")
			end
		else
			if CurWorld:isGodMode() then
				local CreateBackpackFrame = getglobal("CreateBackpackFrame")
				if CreateBackpackFrame:IsShown() then
					CreateBackpackFrame:Hide();
					if GetInst("MiniUIManager"):IsShown("newItemTipsFrameAutoGen") then
						 GetInst("MiniUIManager"):GetCtrl("newItemTipsFrame"):onClose()
					end
				else
					CreateBackpackFrame:Show();
				end
			else

				if getglobal("RoleAttrFrame"):IsShown() or GetInst("MiniUIManager"):IsShown("RoleAttrFrame") then
					RoleAttrFrameCloseBtn_OnClick()
					--[[
					getglobal("RoleAttrFrame"):Hide();
					getglobal("RoleFrame"):Hide();
					getglobal("BackpackFrameMakeFrame"):Hide();
					getglobal("EnchantFrame"):Hide();
					]]
				else
					if ClientCurGame:getGameStage() == 4 then -- 游戏结束不打开界面
						return
					end
					--开发者:限制玩家打开背包
					if not checkCanOpenBackpack() then
						return;
					end
					
					AdventureMode_OperCtrlPanel(1, "AccelKey_Backpack_RoleAttrFrameShow")
				end
			end
		end
		local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
		if playermain_ctrl then
			playermain_ctrl:SwitchSelectBackPack()
		end
		--打开背包1 LYX
		OpenBackpackSandboxEvent()
	end
end
function AccelKey_Backpack_RoleAttrFrameShow()
	if GetClientInfo():isEditorMode() then
		return
	end

	do
		local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
		if playermain_ctrl then
			playermain_ctrl.player_backpackctrl:DoReset(true)
		end
		return 
	end

	if GetInst("RoleAttrManager"):IsOpenNewRoleFrame() then
		GetInst("RoleAttrManager"):OpenRoleAttrFrame()
	else
		getglobal("RoleAttrFrame"):Show();
	end 

	if AccountManager:getCurGuideLevel() == 1 and AccountManager:getCurGuideStep() == 19 then
		
	end
end

function AccelKey_SortBackpack()
	if GetClientInfo():isEditorMode() then
		return
	end

	if GetRecordPkgManager():isRecordPlaying() then
	   return;
	end
	if IsShowTakePhotoMode() then
		return
	end

	if getglobal("RoleAttrFrame"):IsShown() then
		RoleFrameBackpackSortBtn_OnClick();
	elseif GetInst("MiniUIManager"):IsShown("RoleFrame") then
		GetInst("MiniUIManager"):GetCtrl("RoleFrame"):Node_backpackBtn_SortBtnClick()
	elseif getglobal("StorageBoxFrame"):IsShown() then
		StorageBoxFrameTidyBtn_OnClick();
	end
end

--潜行
function AccelKey_Shift()
	if GetClientInfo():isEditorMode() then
		return
	end

	if not CheckPlayerActionState(ENABLE_SNEAK) then
		return
	end

	if GetRecordPkgManager():isRecordPlaying() then
	   return;
	end
	-- 这里只有在足球状态先屏蔽
	-- 额外的重力手套部分 不屏蔽
	if CurMainPlayer:getOPWay() == 1 or  CurMainPlayer:getOPWay() == 3 then 
		return 
	end

	if CurMainPlayer:getSneaking() then
		if CurMainPlayer.isNewMoveSyncSwitchOn and CurMainPlayer:isNewMoveSyncSwitchOn() then
			CurMainPlayer:changeMoveFlag(1, false);
		else
			CurMainPlayer:setSneaking(false)
		end
		--ShowGameTips(StringDefCsv:get(104), 3);
		getglobal("PlayMainFrameSneak"):DisChecked();
		BPReportSneaking(false)
	else
		if CurMainPlayer.isNewMoveSyncSwitchOn and CurMainPlayer:isNewMoveSyncSwitchOn() then
			CurMainPlayer:changeMoveFlag(1, true);
		else
			CurMainPlayer:setSneaking(true)
		end
		--ShowGameTips(StringDefCsv:get(105), 3);
		getglobal("PlayMainFrameSneak"):Checked();
		BPReportSneaking(true)
	end	
end

function AccelKey_G()
	if GetClientInfo():isEditorMode() then
		return
	end

	Log("AccelKey_G");
	if GetRecordPkgManager():isRecordPlaying() then
	   return;
	end
	if ClientGameMgr:getCurGame():isInGame() then
		local player = ClientCurGame:getMainPlayer()
		CurMainPlayer:setCurShortcut(player:getCurShortcut());
		--player:throwItem(player:getCurShortcut()+ClientBackpack:getShortcutStartIndex(), 1)
		local itemId = ClientBackpack:getGridItem(player:getCurShortcut()+ClientBackpack:getShortcutStartIndex())
		if itemId ==11806 and CurWorld and CurWorld:isGodMode() then
			CurMainPlayer:writeLetters(CurMainPlayer:getCurShortcut() + CurMainPlayer:getShortcutStartIndex(), "")
		end
		CurMainPlayer:discardItem(player:getCurShortcut()+ClientBackpack:getShortcutStartIndex(), ClientBackpack:getGridNum(player:getCurShortcut()+ClientBackpack:getShortcutStartIndex()));
	end
	--[[
	if ClientCurGame:isOperateUI() then
		if CurSelectGridIndex >= 0 then player:throwItem(CurSelectGridIndex, 1) end
	else
		player:throwItem(player:getCurShortcut()+ClientBackpack:getShortcutStartIndex(), 1)
	end]]
end

function AccelKey_Q()
	if not CanUseUIShortCut() then return end

	do
		-- body
		local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
		if playermain_ctrl then
			playermain_ctrl:SwitchSelectCraft()
		end
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end
	
	if GetRecordPkgManager():isRecordPlaying() then
	   return;
	end
	Log("AccelKey_Q");
	-- 这是什么功能？ 先注释了 2020/12/14
	-- if getglobal("DoluaUIFrame"):IsShown() then
	-- 	getglobal("DoluaUIFrame"):Hide();
    --     DoluaUIFrame_OnHide()
	-- 	if ClientGameMgr:getCurGame():isInGame() then
	-- 		ClientCurGame:setOperateUI(false);
	-- 	end
	-- else
    --     DoluaUIFrame_OnShow()
	-- 	getglobal("DoluaUIFrame"):Show();
	-- 	if ClientGameMgr:getCurGame():isInGame() then
	-- 		ClientCurGame:setOperateUI(true);
	-- 	end
	-- end

	local checker_uin = AccountManager:getUin()
	if not IsUserOuterChecker(checker_uin) then
		return
	end
	local player = ClientCurGame:getMainPlayer()
	--开启穿墙功能
	if player then
		if not curPierceState then
			player:callPierceCommand()
			curPierceState = true
		else
			player:callPierceCloseCommand()
			curPierceState = false
		end	
	end
end

-- 审核账号夜视功能
function AccelKey_K()
	if GetClientInfo():isEditorMode() then
		return
	end

	if GetRecordPkgManager():isRecordPlaying() then
	   return;
	end

	local checker_uin = AccountManager:getUin()
	if not IsUserOuterChecker(checker_uin) then
		return
	end
	local player = ClientCurGame:getMainPlayer()
	if player then
		if not player:isOpenPierceViewBright() then
			player:setOpenPierceViewBright(true)
		else
			player:setOpenPierceViewBright(false)
		end	
	end
end

function AccelKey_E()	
	if GetClientInfo():isEditorMode() then
		return
	end

	if not GamePlaySubsystem then
		return
	end
	if not curPerspectiveState then
		GamePlaySubsystem:callPerspectivePosCommon(true)
		curPerspectiveState = true
	else
		GamePlaySubsystem:callPerspectivePosCommon(false)
		curPerspectiveState = false
	end	
end


function AccelKey_Chat()
	if GetClientInfo():isEditorMode() then
		return
	end

	if ClientGameMgr:getCurGame():isInGame() then
		local socchatmain_ctrl = GetInst("MiniUIManager"):GetCtrl("socchatmain")
		if socchatmain_ctrl then
			socchatmain_ctrl:setFocus(true)
		end

		--local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
		--if playermain_ctrl then
		--	playermain_ctrl:setFocus(true)
		--end

		--local ctrl = GetInst("MiniUIManager"):GetCtrl("BG")
		--if not ctrl then
		--	GetInst("MiniUIManager"):AddPackage(
		--		{"miniui/miniworld/SOC"})
		--	GetInst("MiniUIManager"):OpenUI("BG", "miniui/miniworld/SOC","BGAutoGen",{disableOperateUI = true})
		--	ctrl = GetInst("MiniUIManager"):GetCtrl("BG")
		--end
		--GetInst("MiniUIManager"):ShowUI("BGAutoGen")
		--ctrl:setFocus(true)

		--[[
		if IsShowTakePhotoMode() then
            return
        end
		--功能限制
		if FunctionLimitCtrl:IsNormalBtnClick(FunctionType.INMAP_CHAT_MSG) then
	    	--常规
	    else
	    	--限制游戏内聊天
	    	return;
	    end

		if checkSafetyIMRoomRef and not checkSafetyIMRoomRef() then
			ShowGameTipsWithoutFilter(GetS(100274))
			return
		end
		
		if ClientCurGame:getCurOpenContanierIndex() ~= SIGNS_START_INDEX then
			if false == AccountSafetyCheck:FunCheck(AccountSafetyCheck.FunType.INMAP_CHAR, AccelKey_Chat) then
				return
			end
		end
	
		if getglobal("RoomUIFrameCenterChatEdit"):IsShown() then
			UIFrameMgr:setCurEditBox(getglobal("RoomUIFrameCenterChatEdit"));
		elseif (not ClientCurGame:isOperateUI()) or (ClientCurGame:getOperateUICount() == 1 and getglobal("SleepNoticeFrame"):IsShown()) then
			if not (RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom()) then
				local chatframe = getglobal("ChatInputFrame");
				chatframe:Show();
			end
			CancelSoundMute()
        end
		--]]
	end
end

function AccelKey_Slash()
	if GetClientInfo():isEditorMode() then
		return
	end

	AccelKey_Chat()
end


function AccelKey_ScreenShot()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	--local filePath = .. 
	if IsShowTakePhotoMode() then
		return
	end
	SnapshotPC:requestSaveSnapshot();
end

function OnSnapshotForPCFinished(data)
	--提示语 增加特定前缀标识 "OnSnapshotForPCFinished_", 后面做判断不过滤 
	--不需要发送给主机 广播给所有人
	-- ClientCurGame:sendChat("OnSnapshotForPCFinished_"..GetS(3628).." "..data);
	if GameEventQue then
		--0表示普通聊天
		local msg = "OnSnapshotForPCFinished_"..GetS(3628).." "..data
		GameEventQue:postChatEvent(0, nil, msg)
	end
end

function IsHideMainMenuFrames()
	local t_MainMenuFrames = {
					{fgui = true, name = "StoreMsgboxFrame"},
					{
						isShowFunc = function ()
							return GetInst("MessageBoxFrameMgr"):IsShown()
						end,
						hideFunc = function()
							GetInst("MessageBoxFrameMgr"):CloseUI()
						end,
					},
					"BeanConvertFrame",
					"AccountLoginFrame",
					"BindingPhoneEmailFrame",
					"ActivateAccountFrame",
					"FeedBackFrame",
					"NickModifyFrame",
					"GameSetFrame",
					"FriendUIFrame",
					"HomeChestFrameFruitInfo",
					"HomeChestFrameFriend",
					"PokedexFrame",
					"ShareArchiveInfoFrame",
					"ArchiveFilterFrame",
					"RoomFrame",
					"OutGameConfirmFrame",
					}
	
	for i=1, #(t_MainMenuFrames) do
		if type(t_MainMenuFrames[i]) == "table" then
			if t_MainMenuFrames[i].isShowFunc and t_MainMenuFrames[i].isShowFunc() then
				if t_MainMenuFrames[i].hideFunc then
					t_MainMenuFrames[i].hideFunc()
				end
				return true;
			elseif t_MainMenuFrames[i].fgui 
				and GetInst("MiniUIManager"):IsShown(t_MainMenuFrames[i].name .. 'AutoGen') then
				return true
			end
		else
			local frame = getglobal(t_MainMenuFrames[i]);
			if frame and frame:IsShown() then
				if t_MainMenuFrames[i] == "RoomFrame" then
					RoomFrameBackBtn_OnClick();
				else
					frame:Hide();
				end
				
				return true;
			end
		end
	end

	return false;
end

function OnWindowLostFocus()
	local context = MNSandbox.SandboxContext()
	context:SetData_Number("result", 1)
    SandboxLua.eventDispatcher:Emit(nil, GameEventType.LostFocus, context)
	if  ClientGameMgr:getCurGame():isInGame() and not ClientGameMgr:getCurGame():isOperateUI() then
		if CurWorld and CurWorld:getOWID() ~= NewbieWorldId then
			--[[
			getglobal("SetMenuFrame"):Show();
			ClientCurGame:setInSetting(true);
			]]
			g_IsShowVideMessageBox = true;
			--MessageBox(4, GetS(3784), nil, nil, true);
			if CurMainPlayer and CurMainPlayer:isSightMode() then
				CurMainPlayer:setSightMode(false);
			end
			GetInst("MessageBoxFrameMgr"):SetClientString( "丢失窗口焦点" );
		end
	end

	GetInst("UGCCommon"):WindowLostFocus()	
end

function SwitchSightMode()
	if ShowBallOperateTipsTime > 0 then return end

	--强制新手引导过程中PC锁定除正确键位外的快捷栏
	local guideControl = GetInst("ForceGuideStepControl")
	if guideControl and guideControl:IsGuiding() and not guideControl.m_IsShowSightMode then
		getglobal("PcGuideKeySightMode"):Hide();
		return
	end	

	--如果不是星标模式就退出星标模式
	local sightmode = getglobal("PcGuideKeySightMode");
	if CurMainPlayer and (not CurMainPlayer:isSightMode()) then
		if getglobal("PcGuideKeyTips"):IsShown() then
			getglobal("PcGuideKeyTips"):Hide();
		end
		--如果动作快捷栏显示，就不显示提示 ugc蓝图不提示
		if getglobal("CharacterActionFrame"):IsShown() or UIEditorDef:isShowingDeveloperUI() or g_SelectedUgcBluePrint then
			sightmode:Hide();
		else
			sightmode:Show();
		end
	else
		sightmode:Hide();
	end

	--视角编辑器显示的时候 准星模式需要设置不能操作UI，反之
	if GetInst("MiniUIManager"):IsShown("CameraFrameAutoGen") and CurMainPlayer then
		if CurMainPlayer:isSightMode() then
			if ClientCurGame then
				ClientCurGame:setOperateUI(false)
			end
		else
			if ClientCurGame then
				ClientCurGame:setOperateUI(true)
			end
		end
	end
end

function AccelKey_OEM_3()
	if GetClientInfo():isEditorMode() then
		return
	end

	SwitchSightMode();

	-- 高级创造，切换准星模式，发送tips关闭消息
	if UGCModeMgr and UGCModeMgr:IsEditing() then
		GetInst('SceneEditorMsgHandler'):dispatcher(SceneEditorUIDef.common.clear_shortcut_enter_event)
		HideMTipsInfo()
	end

	--地形编辑
	-- if GetInst("UIManager"):GetCtrl("MapEdit", "uiCtrlOpenList") then
	-- 	if not CurMainPlayer:isSightMode() then
	-- 		GetInst("UIManager"):GetCtrl("MapEdit"):ShowBrushModeParamView(true)
	-- 	else
	-- 		GetInst("UIManager"):GetCtrl("MapEdit"):ShowBrushModeParamView(false)
	-- 	end
	-- end
	if GetInst("MiniUIManager"):GetCtrl("MapEdit") then
		if not CurMainPlayer:isSightMode() then
			GetInst("MiniUIManager"):GetCtrl("MapEdit"):ShowBrushModeParamView(true)
		else
			GetInst("MiniUIManager"):GetCtrl("MapEdit"):ShowBrushModeParamView(false)
		end
	end
end

_G.IgnoreEsc = false;
function AccelKey_Escape(isDead, isFromGuide)
	if GetClientInfo():isEditorMode() then
		return
	end

	if UGCMapResourceInterface and type(UGCMapResourceInterface.IsPolaroidUIVisible) == "function" and UGCMapResourceInterface:IsPolaroidUIVisible() then
		return;
	end

	local resMainFrame = GetInst("MiniUIManager"):GetCtrl("ResourceBagMain")
	if resMainFrame and resMainFrame:IsBagShown() then
		GetInst('SceneEditorMsgHandler'):dispatcher(SceneEditorUIDef.common.hide_resource_bag)
		return
	end

	-- 逻辑编辑器打开的时候屏蔽掉开发工具快捷
	if GetInst("MiniUIManager"):IsShown("VisualCodeMainViewAutoGen") then
		GetInst('MiniUIManager'):GetCtrl("VisualCodeMainView"):BackBtnCallBack()
		return
	end

	if IgnoreEsc then
		return;
	end
	if ClientCurGame == nil then
		return;
	end

	if isAbroadEvn() and gIsHideExitUI() and ClientCurGame:isInGame() then
		print("OGC接口关闭了设置按钮的显示")
		return;
	end

	--LLDO:录像系统窗口
	VideoRecordEscCloseFrame();
	
	if GetRecordPkgManager():isRecordPlaying() then
		return;
	end

	--加载界面时，忽略esc功能
	if IsUIFrameShown("LoadingFrame") then
		return
	end

	if GetInst("MiniUIGuideMgr") and GetInst("MiniUIGuideMgr"):IsInterruptESC() then
		return
	end
	
	-- 加载公告展示时
	if IsUIFrameShown("GameNoticeFrame") then
		return
	end

	if GetInst("MiniUIManager"):IsShown("GMUIAutoGen") then
		GetInst("MiniUIManager"):CloseUI("GMUIAutoGen")
		return
	end

	if GetInst("MiniUIManager"):IsShown("decompositionAutoGen") then
		GetInst("MiniUIManager"):CloseUI("decompositionAutoGen")
		return
	end

	if GetInst("MiniUIManager"):IsShown("researchpageAutoGen") then
		GetInst("MiniUIManager"):HideUI("researchpageAutoGen")
		return
	end

	if GetInst("MiniUIManager"):IsShown("territoryAutoGen") then
		GetInst("MiniUIManager"):CloseUI("territoryAutoGen")
		return
	end

	if GetInst("MiniUIManager"):IsShown("waterstorageAutoGen") then
		GetInst("MiniUIManager"):CloseUI("waterstorageAutoGen")
		return
	end

	-- 好友列表
	if  GetInst("MiniUIManager"):IsShown("BattleEndFriendList") then
		GetInst("BattleEndFriendListGData"):ClearCache()
		GetInst("MiniUIManager"):CloseUI("BattleEndFriendListAutoGen", true)
		return
	end

	if GetInst("MiniUIManager") then
		if GetInst("MiniUIManager"):OnAccelKey_Escape() then
			return
		end
	end
	
	
	if IsUIFrameShown("ChatEmojiFrameMask") then
		getglobal("ChatEmojiFrameMask"):Hide();
		return;
	end

	if IsUIFrameShown("ChatBackFrameMask") then
		getglobal("ChatBackFrameMask"):Hide();
		return;
	end

	--关闭设置密码界面
	if IsUIFrameShown("ActivateAccountFrame") then
		ActivateAccountFrameCloseBtn_OnClick()
		return
	end
	
	-- 关闭新UI界面，并且只响应一个
	local CloseAddReturnNewUI = {"ScrollingPopTextAutoGen", "SelectFriendAutoGen", 
	"MakeCommentAutoGen", "main_player_freeAutoGen", "GameSprayPaintAutoGen", "FunctionSetAutoGen", "npcDialogueFrameAutoGen", 
	"ArchiveInfoDetail", "StoreSkinDetailPageAutoGen", "weaponGainAutoGen", "weaponSkinDetailAutoGen", "main_birthday_party_boxAutoGen",
	"StoreActionDetailAutoGen", "StoreMountsDetailspageAutoGen", "StoreMountsDetailspageNewAutoGen", "TopPurchaseInMapAutoGen",
	"TriggerAdInMapAutoGen", "PotFrameAutoGen", "MiniUIRiddlesMainAutoGen"}

	-- 关闭但不return
	local JustCloseNewUI = {"itemrepairAutoGen"}
	-- 关闭新UI界面
	local uimanager = GetInst("MiniUIManager")
	local haveui_close = false
	if uimanager then
		for _, uiname in pairs(CloseAddReturnNewUI) do
			if uimanager:IsShown(uiname) then
				uimanager:CloseUI(uiname)
				return
			end
		end

		for _, uiname in pairs(JustCloseNewUI) do
			if uimanager:IsShown(uiname) then
				uimanager:CloseUI(uiname)
				haveui_close = true
			end
		end
	end

	
	-- 关闭分享
	if uimanager then
		if uimanager:GetCtrl("MapDescShare") then
			uimanager:CloseUI("MapDescShareAutoGen")
			if uimanager:IsShown("ExitGameMenu") then
				uimanager:CloseUI("ExitGameMenuAutoGen")
			end
			if IsUIFrameShown("MiniWorksShareMapFrame") then
				getglobal("MiniWorksShareMapFrame"):Hide();
			end
			return
		end
	end
	
	--关闭帮助窗口
	if IsUIFrameShown("CommonHelp") then
		GetInst("UIManager"):Close("CommonHelp")
		return
	end

	--关闭群组邀请界面
	if IsUIFrameShown("GroupChatInviteFriendFrame") then
		getglobal("GroupChatInviteFriendFrame"):Hide()
		return;
	end

	--关闭群组详情界面
	if IsUIFrameShown("GroupFriendFrame") then
		getglobal("GroupFriendFrame"):Hide()
		return;
	end

	--关闭打赏界面1
	if IsUIFrameShown("ArchiveRewardFrame") then
		GetInst("UIManager"):GetCtrl("MapReward"):RewardFrameCloseBtnClicked();
		return;
	end

	--关闭打赏界面2
	if IsUIFrameShown("MapReward") then
		GetInst("UIManager"):GetCtrl("MapReward"):RewardSelectFrameCloseBtnClicked();
		return;
	end

	--关闭开发者脚本日志打印界面
	if IsUIFrameShown("scriptprint") then
		GetInst("UIManager"):GetCtrl("scriptprint"):CloseBtnClicked();
		return;
	end

	--关闭变量库
	if IsUIFrameShown("DeveloperEditTriggerVarLib") then
		local curCtrl = GetInst("UIManager"):GetCurCtrl("DeveloperEditTriggerVarLib")
		if curCtrl and curCtrl.CloseBtnClicked then curCtrl:CloseBtnClicked() end
		return;
	end

	if GetInst("SingleEditorFrameManager"):IsShown() then
		--关闭NewTriggerStoreAutoGen
		if GetInst("MiniUIManager"):IsShown("NewTriggerStoreAutoGen") then
			GetInst("MiniUIManager"):CloseUI("NewTriggerStoreAutoGen")
			return
		end
		--先关闭其他界面
		if GetInst("MiniUIManager"):IsShown("OriginalChooseAutoGen") then
			GetInst("MiniUIManager"):CloseUI("OriginalChooseAutoGen")
			return
		end
		if GetInst("SingleEditorFrameManager"):GetCtrl() then
			GetInst("SingleEditorFrameManager"):GetCtrl():SingleEditorFrameCloseBtn_OnClick()
		end
		return;
	end

	if IsUIFrameShown("ChooseModsFrame") then
		ChooseModsFrameCloseBtn_OnClick();
		return;
	end

	if ModsLib_CloseClick() then
		return
	end

	--LLTODO:关闭举报界面
	if IsUIFrameShown("InformFrame") then
		InformFrameClose_OnClick();
		return;
	end

	--:关闭云服公告界面
	if IsUIFrameShown("CSNoticeFrame") then
		getglobal("CSNoticeFrame"):Hide()
		return;
	end

	if isDead then
		if IsUIFrameShown("GameSetFrame") then
			getglobal("GameSetFrame"):Hide()

			if LuaInterface:getApiId() ~= 999 then
				local soclobby_ctrl = GetInst("MiniUIManager"):GetCtrl("soclobby")
				if soclobby_ctrl then
					GetInst("MiniUIManager"):ShowUI("soclobbyAutoGen")
					soclobby_ctrl:SettingClick(nil, nil)
					ClientCurGame:setInSetting(true)
				end
			else
				GetInst("ExitGameMenuInterface"):ShowExit()
				ClientCurGame:setInSetting(true);
			end
		else
			if IsUIFrameShown("SetMenuFrame") then
				getglobal("SetMenuFrame"):Hide();
				ClientCurGame:setInSetting(false);
			else
				if LuaInterface:getApiId() ~= 999 then
					if GetInst("MiniUIManager"):IsShown("soclobbyAutoGen") then
						GetInst("MiniUIManager"):HideUI("soclobbyAutoGen")
						ClientCurGame:setInSetting(false)
					else
						local soclobby_ctrl = GetInst("MiniUIManager"):GetCtrl("soclobby")
						if soclobby_ctrl then
							GetInst("MiniUIManager"):ShowUI("soclobbyAutoGen")
							soclobby_ctrl:SettingClick(nil, nil)
							ClientCurGame:setInSetting(true)
						end
					end
					return
				end

				if GetInst("MiniUIManager"):IsShown("ExitGameMenuAutoGen") then
					GetInst("MiniUIManager"):CloseUI("ExitGameMenuAutoGen")
					ClientCurGame:setInSetting(false);
				else
					GetInst("ExitGameMenuInterface"):ShowExit()
					ClientCurGame:setInSetting(true);
				end
			end
		end
		return;
	end

	--关闭交互界面：好友界面
	if IsUIFrameShown("FriendChatFrame") then
		getglobal("FriendChatFrame"):Hide();
		return;
	end
	
	if IsUIFrameShown("FriendFrame") then
		getglobal("FriendFrame"):Hide();
		return;
	end


    --刷怪方块界面需要返回上一层 
	if IsUIFrameShown("ChooseOrganismFrame") then
		getglobal("ChooseOrganismFrame"):Hide();
		getglobal("CreateMonsterFrame"):Show();
		return;
	end

	--传送点方块
	if IsUIFrameShown("AddTransferDestinationFrame") then
		getglobal("AddTransferDestinationFrame"):Hide()
		getglobal("TransferFrame"):Show()
		return
	end
	local chooseFrameShown = GetInst("MiniUIManager"):IsShown("OriginalChooseAutoGen")
	if IsUIFrameShown("TransferRuleSetFrame") and (not chooseFrameShown) then
		TransferRuleSetFrameClose()
		--getglobal("TransferFrame"):Show()
		return
	end 

	if IsUIFrameShown("TransferRuleSetFrame") and chooseFrameShown then
		GetInst("MiniUIManager"):CloseUI("OriginalChooseAutoGen")
		getglobal("TransferRuleSetFrame"):Show()
		getglobal("TransferFrame"):Show()
		return
	end

	--关闭编书台页面
	if IsUIFrameShown("BookEditorFrame") then
		BookEditorCtrl.AntiActive()
		return
	end

    --关闭地图页面
	if IsUIFrameShown("MapFrame") then
        getglobal("MapFrame"):Hide();	
    
        if getkv("Sleep_Notice_Frame_Show") then
            getglobal("SleepNoticeFrame"):Show()
            setkv("Sleep_Notice_Frame_Show", false)
        end
		if getkv("GUN_Magazine_Show") then
			if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.RELOAD) then
				getglobal("GunMagazine"):Show()
			end
			setkv("GUN_Magazine_Show", false)
		end

		return
    end
	
	 if PixelMapInterface and PixelMapInterface:IsShowMiniMap() then 
	        GetInst("MiniUIManager"):GetCtrl("pixelmap"):OnESCCall();
	        return;
	    end
    
	if IsUIFrameShown("MultiLangEditFrame") then
		if IsUIFrameShown("TranslatingTipsFrame") then
			return
		else
			MultiLangEditFrameCloseBtn_OnClick();
			return
		end
	end

	if IsNpcShopBuyFrameShown() then
		HideNpcShopBuyFrame()
		return
	end

	--关闭选中编辑生物界面
	if IsUIFrameShown("ActorSelectEditFrame") then
		getglobal("ActorSelectEditFrame"):Hide();
		return
	end

	--关闭触发器详情界面
	if IsUIFrameShown("DeveloperEditTriggerDetail") then
		if GetInst("MiniUIManager"):IsShown("PaletteFrameAutoGen") then
			local ctrl = GetInst("MiniUIManager"):GetCtrl("PaletteFrame")
			if ctrl then
				ctrl:CloseBtnClicked()
			end
		end
		--local paletteFrame = getglobal("PaletteFrame")
		--if paletteFrame and paletteFrame:IsShown() then
		--	PaletteFrameSaveBtn_OnClick() -- 隐藏界面
		--end
	
		local selectctrl = "DeveloperSelectCtrl"
		local selectctrl = GetInst("UIManager"):GetCurCtrl()
		if selectctrl and selectctrl.CloseBtnClicked then selectctrl:CloseBtnClicked() end

		local ctrlName = "DeveloperEditTriggerDetailCtrl"
		local curCtrl = GetInst("UIManager"):GetCurCtrl()
		if curCtrl and curCtrl.CloseBtnClicked then curCtrl:CloseBtnClicked() end
		
		--关闭NewTriggerStoreAutoGen
		if GetInst("MiniUIManager"):IsShown("NewTriggerStoreAutoGen") then
			GetInst("MiniUIManager"):CloseUI("NewTriggerStoreAutoGen")
		end
		return;
	elseif GetInst("MiniUIManager"):IsShown("EditScriptAutoGen") then
		GetInst("MiniUIManager"):CloseUI("EditScriptAutoGen")
	end

	-- 关闭手持触发器界面
	if GetInst("MiniUIManager"):IsShown("ObjInfoEditAutoGen") then
	--if IsUIFrameShown("DeveloperObjEdit") then
		--注意这里要用MVC形式来关闭 ui
		--GetInst("UIManager"):Close("DeveloperObjEdit")
		GetInst("MiniUIManager"):CloseUI("ObjInfoEditAutoGen")
		if not CurMainPlayer:isSightMode() then
			CurMainPlayer:setSightMode(true);
		end
		return
	end

	--关闭开局介绍编辑界面
	if IsUIFrameShown("MapRuleStartSettingIntroduceEdit") then
		getglobal("MapRuleStartSettingIntroduceEdit"):Hide();
		return;
	end

	--关闭开局介绍展示界面
	if IsUIFrameShown("GameStartShowMapIntroduce") then
		press_btn("GameStartShowMapIntroduceCloseBtn");
		return;
	end

	if IsUIFrameShown("SelectTeamFrame") then
		press_btn("SelectTeamFrameFooterRandomBtn");
		return;
	end

	-- 关闭开发者工具界面
	if GetInst("MiniUIManager"):IsShown("DevToolsAutoGen") then
		local curCtrl = GetInst("MiniUIManager"):GetCtrl("DevTools")
		if curCtrl then 
			--打开备注的时候先关闭备注界面
			local developerEditTrigger = GetInst("UIManager"):GetCtrl("DeveloperEditTrigger")
			if developerEditTrigger and developerEditTrigger:GetRemarkIsShown() then
				developerEditTrigger:RemarkSetPanelClose()
				return
			--打开了队伍设定
			elseif GetInst("UIManager"):GetCtrl("MapRuleTeamEdit") then
				local mapRuleSetCtrl = GetInst("UIManager"):GetCtrl("MapRuleTeamEdit")
				if mapRuleSetCtrl and mapRuleSetCtrl:getEditPageIsShow() then
					mapRuleSetCtrl:Close()
				end
			else
				curCtrl:OnCloseOuter()
			end
		end
		return
	end


	-- 关闭地图详情界面
	if IsUIFrameShown("ArchiveInfoFrameEx") then
		GetInst("UIManager"):Close("ArchiveInfoFrameEx");
		return 
	end

	-- 关闭新版广告商人-购买商品界面
	if IsUIFrameShown("ShopItemBuy") then
		GetInst("UIManager"):Close("ShopItemBuy");
		return 
	end

	-- 关闭新版广告商人界面
	if IsUIFrameShown("ShopAdNpc") then
		GetInst("UIManager"):GetCtrl("ShopAdNpc"):CloseBtn_OnClick(true);
		return 
	end

	-- 装扮详情页-周边商城界面
	if getglobal("ProductShopPanel") and getglobal("ProductShopPanel"):IsShown() then
		getglobal("ProductShopPanel"):Hide()
	end

	--关闭商城-装扮详情界面
	if IsUIFrameShown("ShopSkinDisplay") then
		local mvcFrame = GetInst("UIManager"):GetCtrl("ShopSkinDisplay");
		mvcFrame:CloseBtnClicked();
		return 
	end 
	
    -- 关闭充值限额引导界面
    if IsUIFrameShown("ShopRechargeGuide") then 
		local mvcFrame = GetInst("UIManager"):GetCtrl("ShopRechargeGuide");
		mvcFrame:CloseBtnClicked();
		return 
	end 


	--关闭分享界面
	if IsUIFrameShown("ShareOnOptionMenuFrame") then 
		getglobal("ShareOnOptionMenuFrame"):Hide()
		return
	end

	--关闭商城界面
	if IsUIFrameShown("Shop") then 
		local mvcFrame = GetInst("UIManager"):GetCtrl("Shop");
		mvcFrame:CloseBtnClicked();
		return 
	end 

	--关闭商城界面
	if IsUIFrameShown("Shop") then 
		GetInst("UIManager"):GetCtrl("Shop"):CloseBtnClicked()
		return 
	end 
	if GetInst("MiniUIManager"):IsShown("Store") then 
		GetInst("StoreViewSwitcher"):CloseStoreSystem()
		return
	end
	--关闭资源工坊商品详情界面
	if IsUIFrameShown("ResourceShopItemDetail") then 
		local mvcFrame = GetInst("UIManager"):GetCtrl("ResourceShopItemDetail");
		mvcFrame:CloseBtnClicked();
		return 
	end 


	--关闭资源工坊创建商品界面
	if IsUIFrameShown("ResourceShopKindSelect") then
		GetInst("UIManager"):Close("ResourceShopKindSelect")
		return
	end

	--关闭资源工坊插件包选择界面
	if IsUIFrameShown("ResourceShopPluginSelect") then
		GetInst("UIManager"):Close("ResourceShopPluginSelect")
		return
	end

	--关闭资源工坊界面
	if IsUIFrameShown("ResourceShop") then
		local mvcFrame = GetInst("UIManager"):GetCtrl("ResourceShop");
		mvcFrame:CloseBtnClicked();
		return 
	end 

	--关闭地图开局设置中的截图界面
	if IsUIFrameShown("MapRuleStartSettingScreenShotFrame") then
		getglobal("MapRuleStartSettingScreenShotFrame"):Hide();
		return;
	end

	--关闭QQ微信登录界面
	if IsUIFrameShown("QQWeChatLoginPC") then
		GetInst("UIManager"):Close("QQWeChatLoginPC")
		return
	end

	-- 关闭回流弹框
	if IsUIFrameShown("ComeBackFrame") then
		GetInst("UIManager"):Close("ComeBackFrame")
		return
	end

	if IsUIFrameShown("NewComeBackFrame") then
		GetInst("UIManager"):Close("NewComeBackFrame")
		return
	end

	-- 关闭回流系统界面
	if IsUIFrameShown("ComeBackEntrance") then
		GetInst("UIManager"):Close("ComeBackEntrance")
		return
	end

	if IsUIFrameShown("NewComeBackEntrance") then
		GetInst("UIManager"):Close("NewComeBackEntrance")
		return
	end
	
	-- 关闭猴子背包界面
	if IsUIFrameShown("MobInteractBackpack") then
		GetInst("UIManager"):Close("MobInteractBackpack")
		return
	end


	if IsInHomeLandMap and IsInHomeLandMap() then
		-- 关闭家园地图左侧好友列表
		if IsUIFrameShown("HomeChestFrameFriend") and HomeChestFrameFriendListCloseBtn_OnClick then
			HomeChestFrameFriendListCloseBtn_OnClick();
			return;
		end
		-- 关闭家园果实界面（只在家园地图中有效）
		if IsUIFrameShown("HomeChestFrame") and HomeChestFrameBackBtn_OnClick then
			HomeChestFrameBackBtn_OnClick();
			return;
		end
		-- 关闭家园果实界面（只在家园地图中有效）
		if GetInst("MiniUIManager"):GetCtrl("FruitMain") then
			GetInst("MiniUIManager"):CloseUI("FruitMainAutoGen",true)
			return;
		end
	end
	--合成
	if IsUIFrameShown("Craft") then
		GetInst("UIManager"):GetCtrl("Craft"):CloseBtnClicked()
		return
	end


	if not isFromGuide then
		if UGCGetInst("GameTaskMgr"):GetIsInSingPlayerGuide() then

			MessageBox(5,GetS(86034),function(btn)
				if btn == 'left' then
					UGCGetInst("GameTaskMgr"):PassSingPlayerGuide()
				end
			end)
			return
		end
	end

	-- 新手引导
	if GetInst("MiniUIManager"):GetUI("ForseGuideMain") and WorldMgr and WorldMgr.getGameMode and WorldMgr:getGameMode() ~= g_WorldType.OWTYPE_CREATE then
		MessageBox(5,GetS(86034),function(btn)
			if btn == 'left' then
				local guideControl = GetInst("ForceGuideStepControl")
				if guideControl then
					guideControl:ExitGuide()
				end
			end
		end)
		return
	end

	-- 特殊道具选择样式
	if GetInst("MiniUIManager"):GetCtrl("SpecialItem") then
		local isVisible = GetInst("MiniUIManager"):GetCtrl("SpecialItem"):IsSpecilaItemSelectViewVisible()
		if isVisible then
			GetInst('SceneEditorMsgHandler'):dispatcher(SceneEditorUIDef.common.show_specialitem_select_view)
			return
		end
	end
	
	if ClientGameMgr:getCurGame():isInGame() then
		--游戏中esc失效ui表
		local t_HideFrame = {
			"HomeChestFrame","PokedexFrame","DeveloperModeSet"
		}
		for _, v in pairs(t_HideFrame) do
			local hideui = getglobal(v)
			if hideui and hideui:IsShown() then
				return
			end
		end

		--游戏中esc失效新ui表
		local t_NewHideFrame = {
			"GameSettlementAutoGen",
			"ModsLibPkgAutoGen",
		}
		for _, v in pairs(t_NewHideFrame) do
			if GetInst("MiniUIManager"):IsShown(v) then
				return
			end
		end

		--工具模式:
		if CurWorld:isGameMakerMode() and CurWorld:isGameMakerToolMode() then
			--回滚
			if IsUIFrameShown("ToolModeFrameRevertBtn") then
				press_btn("ToolModeFrameRevertBtn");
				return;
			end

			--关闭显示板属性界面
			if GetInst("MiniUIManager"):IsShown("DisplayBoardMainAutoGen") then
				GetInst("MiniUIManager"):CloseUI("DisplayBoardMainAutoGen");
				--如果对象库界面不可见，那么将选中框也取消
				if not IsUIFrameShown("ToolObjLib") and TriggerObjLibMgr and TriggerObjLibMgr.getDisplayBoardMgr then
					local boardmgr = TriggerObjLibMgr:getDisplayBoardMgr()
					if boardmgr and boardmgr.ShowDisplayBoardSelectView then
						boardmgr:ShowDisplayBoardSelectView();
					end
				end
				return;
			end

			--关闭对象库
			if IsUIFrameShown("ToolObjLib") then
				press_btn("ToolObjLibBodyCloseBtn");
				return;
			end
		end

		if getglobal("AltarAwardsEdit"):IsShown() then
			GetInst("UIManager"):GetCtrl("AltarAwardsEdit"):CloseBtnClicked();
			return;
		end

		-- UI编辑器 开发者展示界面
		if UIEditorDef and UIEditorDef:escBtnClick() then
			return
		end

		-- 拍照模式
		if GetInst("MiniUIManager"):IsShown("main_cameraAutoGen") then
			local node = GetInst("MiniUIManager"):GetUI("main_cameraAutoGen")
            if not (node and node.ctrl and node.ctrl.view) then
                return
            end
			node.ctrl:Btn_closeClick()
			return
		end
	end

	if ClientCurGame:getName() == "SurviveGame" or ClientCurGame:getName() == "GameSurviveRecord" or ClientCurGame:getName() == "UGCModeGame"  then
		if CurWorld:getOWID() == NewbieWorldId or CurWorld:getOWID() == NewbieWorldId2 then
			PlayMainFrameGuideSkip_OnClick();
		elseif GetIWorldConfig():getGameData("hideui") == 1 then
			AccelKey_HideUIToggle();
			return;
		--elseif IsUIFrameShown("PaletteFrame") then
        --    getglobal("PaletteFrame"):Hide()
		elseif GetInst("MiniUIManager"):IsShown("PaletteFrameAutoGen") then
			GetInst('MiniUIManager'):CloseUI("PaletteFrameAutoGen")
		elseif IsUIFrameShown("lettersFramePage") then
            --getglobal("LettersFrame"):Hide()
			GetInst("MiniUIManager"):GetCtrl("lettersFramePage"):Btn_CloseClick()
			UIFrameMgr:setCurEditBox(nil);
		elseif IsUIFrameShown("ShopWareInfo") then
			GetInst("UIManager"):Close("ShopWareInfo");
		elseif GetInst("MiniUIManager"):IsShown("StoreWareInfoAutoGen") then
			GetInst('MiniUIManager'):CloseUI("StoreWareInfoAutoGen")
		elseif IsUIFrameShown("Shop") then
			local mvcFrame = GetInst("UIManager"):GetCtrl("Shop");
			mvcFrame:CloseBtnClicked();
		elseif IsUIFrameShown("CreateBackpackFrame") then
			getglobal("CreateBackpackFrame"):Hide()
			UIFrameMgr:setCurEditBox(nil);
			if GetInst("MiniUIManager"):IsShown("newItemTipsFrameAutoGen") then
				 GetInst("MiniUIManager"):GetCtrl("newItemTipsFrame"):onClose()
			end
		elseif IsUIFrameShown("BattleEndFrame") then
        	--getglobal("SetMenuFrame"):Show();
			--ClientCurGame:setInSetting(true);
			return;
		--elseif IsUIFrameShown("CameraFrame") then
        --    getglobal("CameraFrame"):Hide()
		elseif GetInst("MiniUIManager"):IsShown("CameraFrameAutoGen") then
			GetInst('MiniUIManager'):CloseUI("CameraFrameAutoGen")
        elseif IsUIFrameShown("InstructionParserFrame") then
        	getglobal("InstructionParserFrame"):Hide()
        	if IsUIFrameShown("MItemTipsFrame") then
        		getglobal("MItemTipsFrame"):Hide()	--隐藏名字框
        	end
        elseif IsUIFrameShown("SignalParserFrame") then
        	SignalParserFrameCloseBtn_OnClick();
        elseif IsUIFrameShown("EncryptFrame") then
        	EncryptFrameCloseBtn_OnClick();
        elseif IsUIFrameShown("ResourceCenterMoveFile") then
        	GetInst("UIManager"):Close("ResourceCenterMoveFile")
        elseif IsUIFrameShown("ResourceCenterSelectFolder") then
        	GetInst("UIManager"):Close("ResourceCenterSelectFolder")
        elseif IsUIFrameShown("ResourceCenter") then
        	GetInst("UIManager"):GetCtrl("ResourceCenter"):OnEscape()
		elseif IsUIFrameShown("SetMenuFrame") then
			getglobal("SetMenuFrame"):Hide()
		  	ClientCurGame:setInSetting(false);
		elseif GetInst("MiniUIManager"):IsShown("ExitGameMenu") then
			GetInst("MiniUIManager"):CloseUI("ExitGameMenuAutoGen")
			ClientCurGame:setInSetting(false);
		elseif GetInst("MiniUIManager"):IsShown("soclobbyAutoGen") then
			GetInst("MiniUIManager"):HideUI("soclobbyAutoGen")
			ClientCurGame:setInSetting(false);
		elseif IsUIFrameShown("ActionLibraryFrame") then
			getglobal("ActionLibraryFrame"):Hide();
			if not CurMainPlayer:isSightMode() then
				CurMainPlayer:setSightMode(true);
			end
		elseif IsUIFrameShown("BookFrame") then
			BookFrameCloseBtn_OnClick();		--关闭书封面
		elseif IsUIFrameShown("ContainerSensorValue") then
			GetInst("UIManager"):GetCtrl("ContainerSensorValue"):CloseBtnClicked(); --关闭感应方块界面
		-- elseif IsUIFrameShown("TombStone") then
        	-- 	GetInst("UIManager"):Close("TombStone")
		elseif GetInst("MiniUIManager"):IsShown("TombStoneFramePageAutoGen")then
			GetInst("MiniUIManager"):CloseUI("TombStoneFramePageAutoGen")
		else

			if IsInHomeLandMap and IsInHomeLandMap() then
				if CurMainPlayer and GetClientInfo():isPC()then
					CurMainPlayer:setActionInputActive(true)
					CurMainPlayer:setMoveInputActive(true)
				end
				-- 关闭家园宠物探险相关界面
				if IsUIFrameShown("PetMaterials") then
					GetInst("UIManager"):GetCtrl("PetMaterials"):CloseBtn_OnClick()
					return
				end
				if IsUIFrameShown("PetSelect") then
					GetInst("UIManager"):GetCtrl("PetSelect"):CloseBtn_OnClick()
					return
                end

                if IsUIFrameShown("PetNickModifyFrame") then
					getglobal("PetNickModifyFrame"):Hide()
					return
                end
                
				if IsUIFrameShown("FoodSelect") then
					GetInst("UIManager"):GetCtrl("FoodSelect"):CloseBtn_OnClick()
					return
				end
				if IsUIFrameShown("PetNest") then
					GetInst("UIManager"):GetCtrl("PetNest"):CloseBtn_OnClick()
					return
				end
				if IsUIFrameShown("PetExplore") then
					GetInst("UIManager"):GetCtrl("PetExplore"):CloseBtn_OnClick()
					return
				end
				if IsUIFrameShown("PetSynthesis") then
					GetInst("UIManager"):GetCtrl("PetSynthesis"):CloseBtn_OnClick()
					return
				end

				-- 关闭家园升级界面
				if IsUIFrameShown("HomeLevel") then
					GetInst("UIManager"):GetCtrl("HomeLevel"):CloseBtn_OnClick()
					return
				end

				-- 关闭家园地图商店界面
				if IsUIFrameShown("HomelandShop") then
					GetInst("UIManager"):GetCtrl("HomelandShop"):CloseBtnClicked()
					return
				end

				--关闭新家园出售界面
				if IsUIFrameShown("HomelandSell") then
					GetInst("UIManager"):GetCtrl("HomelandSell"):CloseBtnClicked()
					return;
				end

				--关闭新家园背包界面
				if IsUIFrameShown("HomelandBackpack") then
					GetInst("UIManager"):GetCtrl("HomelandBackpack"):HomelandBackpackCloseBtn_OnClick()
					return;
				end
				
				--关闭新家园商店界面
				if IsUIFrameShown("MysticalShop") then
					GetInst("UIManager"):GetCtrl("MysticalShop"):CloseBtnClicked();
					return;
				end
				
				--关闭新家园农场商店界面
				if IsUIFrameShown("FarmShop") then
					GetInst("UIManager"):GetCtrl("FarmShop"):CloseBtnClicked();
					return;
				end
				
				--关闭新家园农场解锁界面
				if IsUIFrameShown("FarmlandUnlock") then
					GetInst("UIManager"):GetCtrl("FarmlandUnlock"):CloseBtnClicked();
					return;
				end
				
				-- 关闭房员设置界面
				if IsUIFrameShown("RoomUIFrame") then
					RoomUIFrameCloseBtn_OnClick();
					return;
				end

				--关闭新家园灶台界面
				if IsUIFrameShown("HomelandStove") then
					GetInst("UIManager"):GetCtrl("HomelandStove"):HomelandStoveCloseBtn_OnClick();
					return;
				end

				--关闭新家园图鉴界面
				if IsUIFrameShown("HomelandArchive") then
					GetInst("UIManager"):GetCtrl("HomelandArchive"):HomelandArchiveCloseBtn_OnClick();
					return;
				end

				-- 关闭家园地图中家园记录事件界面
				if IsUIFrameShown("HomeEventRecord") then
					GetInst("UIManager"):GetCtrl("HomeEventRecord"):CloseBtn_OnClick()
					return;
				end

				-- 关闭家园聊天界面
				if IsUIFrameShown("HomeLandChat") then
					GetInst("UIManager"):GetCtrl("HomeLandChat"):CloseBtnClicked()
					return
				end

				-- 关闭家园设置界面
				if IsUIFrameShown("HomeLandSysSet") then
					GetInst("UIManager"):GetCtrl("HomeLandSysSet"):CloseBtnClicked()
					return
				end

				-- 关闭系统设置界面
				if getglobal("GameSetFrame"):IsShown() then
					getglobal("GameSetFrame"):Hide()
					return
				end

				--制作
				if IsUIFrameShown("HomeProducer") then
					GetInst("UIManager"):GetCtrl("HomeProducer"):HomeProducerClose_OnClick()
					return
                end
				--饲料-肥料
				if IsUIFrameShown("HomelandFodder") then
					GetInst("UIManager"):GetCtrl("HomelandFodder"):HomelandFodderCloseBtn_OnClick()
					return
                end
                -- 养殖场升级界面
                if IsUIFrameShown("HomelandRankUpgrade") then
					GetInst("UIManager"):GetCtrl("HomelandRankUpgrade"):HomelandRankUpgradeCloseBtn_OnClick()
					return
                end
                
                -- 宠物窝
                if IsUIFrameShown("PetNest") then
					GetInst("UIManager"):GetCtrl("PetNest"):CloseBtn_OnClick()
					return
                end

                -- 宠物探险界面
                if IsUIFrameShown("PetExplore") then
					GetInst("UIManager"):GetCtrl("PetExplore"):CloseBtn_OnClick()
					return
                end

				  --菜谱
  				if GetInst("MiniUIManager"):GetUI("MiniUIHomelandMenuMain") then
					GetInst("MiniUIManager"):CloseUI("MiniUIHomelandMenuMain")
					return
				end
				--升级途径
				if GetInst("MiniUIManager"):GetUI("MiniUIHomelandUpgradeMain") then
					GetInst("MiniUIManager"):CloseUI("MiniUIHomelandUpgradeMain")
					return
				end
				
				--详细升级途径
				if GetInst("MiniUIManager"):GetUI("MiniUIHomelandUpgradeDetailMain") then
					GetInst("MiniUIManager"):CloseUI("MiniUIHomelandUpgradeDetailMain")
					return
				end

                --家园工匠家具拆解
                if GetInst("MiniUIManager"):GetUI("MiniUIFurnitureMain") then
                    GetInst("MiniUIManager"):CloseUI("MiniUIFurnitureMain")
                    return
                end
                
                --家园工匠特惠家具
                if GetInst("MiniUIManager"):GetUI("MiniUISpecialFurnitureMain") then
                    GetInst("MiniUIManager"):CloseUI("MiniUISpecialFurnitureMain")
                    return
                end

				  --每日任务
  				if GetInst("MiniUIManager"):GetUI("MiniUITaskMain") then
					GetInst("MiniUIManager"):CloseUI("MiniUITaskMain")
					return
				end



				-- 注意：家园地图中按Esc键显示更多设置面板，家园地图中其他界面的处理一定要放在这条之前处理，否则会有顺序错误
				if getglobal("HomeMainMorePanel"):IsShown() then			
					GetInst("UIManager"):GetCtrl("HomeMain"):CloseMorePanel_OnClick()
					return;
				else
					GetInst("UIManager"):GetCtrl("HomeMain"):MoreSetting_OnClick()
					return;
				end
			end

			if ClientCurGame:isOperateUI() or isFromGuide then
				if IsUIFrameShown("RoleFrame") then
					getglobal("RoleFrame"):Hide();
				end

				if IsUIFrameShown("ActorEditFrame") then
					ActorEditFrameCloseBtn_OnClick();
				end
				if IsUIFrameShown("FullyCustomModelSelect") then
					GetInst("UIManager"):GetCtrl("FullyCustomModelSelect"):CloseBtnClicked()
				end
				if IsUIFrameShown("FullyCustomModelEditor") then
					GetInst("UIManager"):GetCtrl("FullyCustomModelEditor"):CloseBtnClicked()
				end

				if IsUIFrameShown("Furnace") then
					GetInst("UIManager"):GetCtrl("Furnace"):CloseBtnClicked()
				end
				
				HideAllFrame(nil, false);				
				getglobal("RoleAttrFrame"):Hide();
				getglobal("BackpackFrameMakeFrame"):Hide();
				getglobal("ChooseEnchantFrame"):Hide();
				getglobal("EnchantFrame"):Hide();
				if getglobal("CreateRoomFrame") then
					getglobal("CreateRoomFrame"):Hide();
				end
				--重置下传送弹窗显示数据
				GetInst("CloudPortalInterface"):SetShowPanel(false, 2);
			else
				-- ugc蓝图列表
				if GetInst("MiniUIManager"):IsShown("UgcBluePrintListAutoGen") then
					GetInst('MiniUIManager'):CloseUI("UgcBluePrintListAutoGen")
					return ;
				end
				
				-- ugc蓝图
				if GetInst("MiniUIManager"):IsShown("BluePrintMenuAutoGen") then
					UgcBluePrintEsc()
					return
				end

				--如果不是星标模式就退出星标模式
				if not CurMainPlayer:isSightMode() then
					CurMainPlayer:setSightMode(true);
				else
					if not haveui_close then
			   			--getglobal("SetMenuFrame"):Show();
						if LuaInterface:getApiId() ~= 999 then
							local soclobby_ctrl = GetInst("MiniUIManager"):GetCtrl("soclobby")
							if soclobby_ctrl then
								GetInst("MiniUIManager"):ShowUI("soclobbyAutoGen")
								soclobby_ctrl:SettingClick(nil, nil)
								ClientCurGame:setInSetting(true)
							end
							return
						end

						GetInst("ExitGameMenuInterface"):ShowExit()
			   			ClientCurGame:setInSetting(true);
					end
				end
				getglobal("PcGuideKeySightMode"):Hide();
			end
		end

	elseif ClientCurGame:getName() == "MainMenuStage" then
		local soclobby_ctrl = GetInst("MiniUIManager"):GetCtrl("soclobby")
		if soclobby_ctrl then
			local v = soclobby_ctrl.view.widgets.soclobby_c:getSelectedIndex()
			--登录状态
			if v == 2 then
				return
			end
		end

		--在进玩法场景前，都会进这个分支
		if GetInst("MiniUIManager"):GetUI("MiniUILoginHomeMain") -- 兼容海外
			then
			--登陆界面，如果MiniUIManager 的没有处理，在这里也不做处理
		elseif IsUIFrameShown("SetMenuFrame") then
			getglobal("SetMenuFrame"):Hide()
		else
			if LuaInterface:getApiId() ~= 999 then
				local soclobby_ctrl2 = GetInst("MiniUIManager"):GetCtrl("soclobby")
				if soclobby_ctrl2 then
					GetInst("MiniUIManager"):ShowUI("soclobbyAutoGen")
					soclobby_ctrl:SettingClick(nil, nil)
					ClientCurGame:setInSetting(true)
				end
			else
				if not IsHideMainMenuFrames() then
					--getglobal("SetMenuFrame"):Show()
					GetInst("ExitGameMenuInterface"):ShowExit()
				end 
			end
		end
	end

	--商品详情页
	if IsUIFrameShown("DeveloperStoreBuyItemFrame") then
		DeveloperStoreBuyItemFrameCloseBtn_OnClick()
		return
	end

	if GetInst("MiniUIManager"):IsShown("playermain") then
		local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
		local v = playermain_ctrl.view.widgets.c1:getSelectedIndex()
		if v ~=4 then  --4是重伤状态
			playermain_ctrl.view.widgets.c1:setSelectedIndex(0)
		end
		PixelMapInterface:ShowCompass()
		--只要是关闭状态遮罩都不能开
		playermain_ctrl:HideTouchBg()

		local soclockctrl = playermain_ctrl.menu:GetObj(MenuCtrlType_SOCLOCKCTRL)
		if soclockctrl and soclockctrl.passwdlockui:isVisible() then
			soclockctrl:cleanpasswdlockui()
		end
		return
	end
end

function AccelKey_Space()
	
end

function AccelKey_Left()
	if GetRecordPkgManager():isRecordPlaying() then
	   return;
	end
	
end

function AccelKey_HideUIToggle()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	if getglobal("FullyCustomModelSelect"):IsShown() or getglobal("FullyCustomModelEditor"):IsShown() then
		return
	end

	if IsShowTakePhotoMode() then
		return
	end

	if IsUIFrameShown("MapRuleStartSettingScreenShotFrame") then
		return;
	end
	if GetInst("ModsLibPkgManager"):IsModslibPkgScreenShotFrameShown() then
		return;
	end
	local state = GetIWorldConfig():getGameData("hideui");
	if state==1 then
		state = 0;
		if CurWorld then
			CurWorld:SetShowName(true)
			CurWorld:SetShowHpBar(true)
		end
	else
		state = 1;
		if MiniUI_GameSettlement.IsShown() then
			curBattleState = 1
		elseif GetInst("MiniUIManager"):IsShown("BattleDeathFrameAutoGen") then
			curBattleState = 2
		elseif GetInst("MiniUIManager"):IsShown("BattleEndShadeFrameAutoGen") then
			curBattleState = 3
		elseif GetInst("MiniUIManager"):IsShown("DeathFrameAutoGen") then
			curBattleState = 4
		else
			curBattleState = 0
		end
		if CurWorld then
			CurWorld:SetShowName(false)
			CurWorld:SetShowHpBar(false)
		end
	end
	GetIWorldConfig():setGameData("hideui", state);
	if state == 1 then
		getglobal("GameSetFrame"):Hide();
		HideAllUI();
		if SceneEditorUIInterface then
			SceneEditorUIInterface:HideRootNode()
		end
		if WorldMgr and WorldMgr:getGameMode() == g_WorldType.OWTYPE_SINGLE then
			GetInst("MiniUIManager"):HideUI("TaskTrackCtrl")
		end
	elseif state == 0 then
		for i=1, #(t_UIName) do
			local frame = getglobal(t_UIName[i]);
			frame:Show();
		end
		if SceneEditorUIInterface then
			SceneEditorUIInterface:ShowRootNode()
			if IsUGCEditingHighMode() then
				-- UGC内容重新显示
				GetInst("UGCCommon"):AfterHideAllUI();
				PlayMainFrameUIShow();
				GetInst("UGCCommon"):RefreshEditUI()
			end
		end
		local mvcFrame = GetInst("UIManager"):GetCtrl("StarStationInfo");
		if mvcFrame then
			local param = mvcFrame.model:GetIncomingParam()
			if CurMainPlayer and CurMainPlayer:isSittingInStarStationCabin() then
				GetInst("UIManager"):Open("StarStationInfo",param)
			end
		end
		if IsInHomeLandMap and IsInHomeLandMap() then
			getglobal("GongNengFrame"):Hide();
		end
		if IsUGCEditingHighMode() then
			getglobal("GongNengFrame"):Hide();
		end
		getglobal("GameSetFrame"):Hide();
		getglobal("UIHideFrame"):Hide();
		CurMainPlayer:setUIHide(false);
		-- if CUR_WORLD_MAPID == 1 then
		-- 	if not getglobal("InstanceTaskFrame"):IsShown() then
		-- 		getglobal("InstanceTaskFrame"):Show();
		-- 	end
		-- end
		if curBattleState == 1 or curBattleState == 3 then
			MiniUI_GameSettlement.ShowUI()
		elseif curBattleState == 2 then
			GetInst("MiniUIManager"):ShowUI("BattleDeathFrameAutoGen")
		elseif curBattleState == 4 then 
			GetInst("MiniUIManager"):ShowUI("DeathFrameAutoGen")
		end
		if MapEditManager:GetIsStartEdit() then 
			-- getglobal("MapEdit"):Show()
			-- HideAllFrame("MapEdit", true)
			GetInst("MiniUIManager"):ShowUI("MapEditAutoGen")
			HideAllFrameMiniUI("MapEditAutoGen", true)
		end
		if WorldMgr and WorldMgr:getGameMode() == g_WorldType.OWTYPE_SINGLE then
			--GetInst("MiniUIManager"):ShowUI("TaskTrackCtrl") --soc2024
		end
	end

	ClientMgrAppalyGameSetData()

end

function AccelKey_Right()
	
end

function AccelKey_Alt()
	if GetClientInfo():isEditorMode() then
		return
	end

	if GetRecordPkgManager():isRecordPlaying() then
	   return;
	end
	if getglobal("MItemTipsFrame"):IsShown() then
		if getglobal("MItemTipsFrameAltTitle"):IsShown() then
			getglobal("MItemTipsFrame"):SetClientID(1);
			UpdateMItemTipsFrameInfo();
		else
			getglobal("MItemTipsFrame"):Hide();
		end
	end
end

function AccelKey_Map_Old()
	if GetClientInfo():isEditorMode() then
		return
	end

	-- 录制模式 / 家园地图 屏蔽 M 键
	if GetRecordPkgManager():isRecordPlaying() or IsInHomeLandMap() then
	   return;
	end
	if IsUIFrameShown("DeveloperModeSet") then
		return;
	end
	if IsShowTakePhotoMode() then
		return
	end
	if GetInst("MiniUIManager"):IsShown("DevToolsAutoGen") then
		return
	end
	CurMainPlayer:setSightMode(true)
    if getglobal("MapFrame"):IsShown() then 
        getglobal("MapFrame"):Hide();
        if getkv("Sleep_Notice_Frame_Show") then
            getglobal("SleepNoticeFrame"):Show()
            setkv("Sleep_Notice_Frame_Show", false)
        end
		if getkv("GUN_Magazine_Show") then
			if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.RELOAD) then
				getglobal("GunMagazine"):Show()
			end
			setkv("GUN_Magazine_Show", false)
		end
	else
		if GetInst("MiniUIManager"):IsShown("SceneShotFrameAutoGen") then
			return;
		end
		if IsUIFrameShown("MapRuleStartSettingScreenShotFrame") then
			return;
		end
		if GetInst("ModsLibPkgManager"):IsModslibPkgScreenShotFrameShown() then
			return;
		end
		CurMainPlayer:setSightMode(false);
		CompassOpenMap_OnClick();
	end		
end

function AccelKey_Map(val)
	if GetClientInfo():isEditorMode() then
		return
	end

	if not CanUseUIShortCut() then return end

	if PixelMapInterface:IsEnterSocMap() and PixelMapInterface:IsSwitchOpenMap() then
		if val then
			PixelMapInterface:ShowMainMap()
		else
			local ctrl = GetInst("MiniUIManager"):GetCtrl("pixelmap")
			--出现复活界面不给隐藏
			if ctrl and ctrl:IsShowReviveFrame() then
				return
			end

			PixelMapInterface:ShowMainMiniMap()
		end
		return
	end

	if not val then
		return
	end

	--如果使用了ui库,ui库里设置了小地图隐藏，则快捷键打开小地图失效
	if UIEditorDef and UIEditorDef:rootNodeIsExist() then
		if not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP) then
			return;
		end
	end

	if not PixelMapInterface:UseNewSmallMap() then
		AccelKey_Map_Old();
		return ;
	end
	-- 录制模式 / 家园地图 屏蔽 M 键
	if GetRecordPkgManager():isRecordPlaying() or IsInHomeLandMap() then
	   return;
	end
	if IsUIFrameShown("DeveloperModeSet") then
		return;
	end
	if IsShowTakePhotoMode() then
		return
	end
	if GetInst("MiniUIManager"):IsShown("DevToolsAutoGen") then
		return
	end

    if PixelMapInterface:IsShowMiniMap() then 
		local ctrl = GetInst("MiniUIManager"):GetCtrl("pixelmap")
		--出现复活界面不给隐藏
		if ctrl:IsShowReviveFrame() then
			return
		end

        PixelMapInterface:HideMiniMap()
	else
		if IsUIFrameShown("MapFrame") then
	        getglobal("MapFrame"):Hide();	
	    else
			if GetInst("MiniUIManager"):IsShown("SceneShotFrameAutoGen") then
				return;
			end
	    	if IsUIFrameShown("MapRuleStartSettingScreenShotFrame") then
				return;
			end
			if GetInst("ModsLibPkgManager"):IsModslibPkgScreenShotFrameShown() then
				return;
			end
			PixelMapInterface:ShowMiniMap();
	    end
	end		
end

function AccelKey_RuleSet()
	if GetClientInfo():isEditorMode() then
		return
	end

	if IsUIFrameShown("DeveloperModeSet") then
		return;
	end
	if IsShowTakePhotoMode() then
		return
	end
	if IsUIFrameShown("FriendFrame") then
		getglobal("FriendFrame"):Hide();
	end

	-- 关闭开发者工具界面
	if GetInst("MiniUIManager"):IsShown("DevToolsAutoGen") then
		local curCtrl = GetInst("MiniUIManager"):GetCtrl("DevTools")
		if curCtrl then 
			curCtrl:CloseBtnClicked()
		end
	else
		if IsUIFrameShown("GongNengFrameRuleSetGNBtn") then
			GetInst('MiniUIManager'):AddPackage({'miniui/miniworld/commonTexture'},"DevToolsAutoGen")
			GetInst("MiniUIManager"):OpenUI("devToolsMain","miniui/miniworld/ugc_devTools","DevToolsAutoGen")
		end
	end
end


function AccelKey_Number(n)
	if GetClientInfo():isEditorMode() then
		return
	end

	if not ClientGameMgr:getCurGame():isInGame() then return end

	--强制新手引导过程中PC锁定除正确键位外的快捷栏
	local guideControl = GetInst("ForceGuideStepControl")
	if guideControl and guideControl:IsGuiding() and guideControl:GetShortCutIndex()~=n then
		return
	end

	--禁止操作时不允许切换快捷栏
	if CurMainPlayer then
		local attrib = CurMainPlayer:getLivingAttrib()
		if attrib and attrib:getBuffEffectBankInfo(2034) then
				return
		end
	end
	
	local worldDesc = AccountManager:getCurWorldDesc();
	if worldDesc and worldDesc.worldtype == 9 then
		--录像存档
		Log("Is Record Map:");
		if n >= 1 and n <= 7 then
			if GetRecordPkgManager():isEdit() then
				--编辑模式才可用快捷键1-7
				local replayTheater = GetInst("MiniUIManager"):GetCtrl("ReplayTheater")
				if GetInst("MiniUIManager"):IsShown("ReplayTheaterAutoGen") and replayTheater then
					if n == 2 then
						replayTheater:OnAccelKeyPreviewVideo()
					else
						replayTheater:ShortcutItemOnClick(n)
					end
				end
			else
				if n == 1 then
					--编辑模式才可用快捷键1-7
					local replayTheater = GetInst("MiniUIManager"):GetCtrl("ReplayTheater")
					if GetInst("MiniUIManager"):IsShown("ReplayTheaterAutoGen") and replayTheater then
						replayTheater:ShortcutItemOnClick(n)
					end
				end
			end

			return;
		end
	end

	--工具模式
	if CurWorld and (CurWorld:isGameMakerToolMode()) then
		--1. 回滚按钮显示的时候不让切换
		if getglobal("ToolModeFrameRevertBtn"):IsShown() then
			return;
		end

		--3. 不让切换工具模式的情况:
		if not GetInst("UIManager"):GetCtrl("ToolModeFrame"):CanSwitchToolMode() then
			return;
		end

		local on = true;
		if isAbroadEvn() then
			--根据白名单设置显示版的功能
			if ns_version then
				on = check_apiid_ver_conditions(ns_version.display_board_switch , false)
			end
		end

		if n == 1 then
			press_btn("ToolModeFrameToolFrameArea");
		elseif n == 2 then
			press_btn("ToolModeFrameToolFramePosition");
		elseif n == 3 then
			press_btn("ToolModeFrameToolFrameActor");
		elseif n == 4 and on then
			press_btn("ToolModeFrameToolFrameDisplayBoard");
		end
		return;
	end
	
	if ClientCurGame:isOperateUI() then
		if CurSelectGridIndex < 0 then return end
		
		srcindex = n-1+ClientBackpack:getShortcutStartIndex()
		if getglobal("CreateBackpackFrame"):IsShown() or (not ResourceCenterNewVersionSwitch and getglobal("MapModelLibFrame"):IsShown()) 
			or (ResourceCenterNewVersionSwitch and HasUIFrame("ResourceCenter") and getglobal("ResourceCenter"):IsShown()) then
			local itemId = CurSelectGridIndex + 1;
			local itemDef = ItemDefCsv:get(itemId);
			if itemDef.UnlockFlag > 0 then
				local unlock, hasUnlockInfo = isItemUnlockByItemId(itemDef.ID)
				if not unlock then
					ShowItemUnLockTips(hasUnlockInfo)
					return
				end
			end
			-- 审核文本清除相关
			if CurWorld and CurWorld:isGodMode() then
				local itemId =  ClientBackpack:getGridItem(srcindex)
				-- 信纸
				if itemId==11806 then
					CurMainPlayer:removeLetter(srcindex)
				-- 蓝图
				elseif itemId==ITEM_BLUEPRINT then
					CurMainPlayer:removeWorldStringByGridIndex(srcindex, ITEM_BLUEPRINT, BLUE_PRINT)
				-- 指令芯片
				elseif itemId==ITEM_INSTRUCTION then
					CurMainPlayer:removeWorldStringByGridIndex(srcindex, ITEM_INSTRUCTION, WSB_INSTRUCTION_CHIP)
				-- 书本
				elseif itemId==ITEM_BOOK then
					CurMainPlayer:removeWorldStringByGridIndex(srcindex, ITEM_BOOK, BOOK)
				end
			end
			
			CurMainPlayer:setItem(itemId, srcindex);
			if (not ResourceCenterNewVersionSwitch and getglobal("MapModelLibFrame"):IsShown()) or  
			(ResourceCenterNewVersionSwitch and HasUIFrame("ResourceCenter") and getglobal("ResourceCenter"):IsShown()) then
				statisticsUIInGame(30018, EnterRoomType);
			end
			SwapBlinkBtnName = "CreateBackpackFrameShortcutGrid"..n;
		elseif getglobal("HomelandBackpack"):IsShown() then
			local itemId = CurSelectGridIndex + 1;
			local homeItemDef = GetInst("HomeLandDataManager"):GetHomeLandDefinition("HomeItemDef", itemId, true) --这里就用老数据兼容 不是该版本的栏目 就不要移动到快捷栏
			if not homeItemDef then
				ShowGameTips(GetS(4028)) --提示升级到最新版本
				return
			end

			local itemDef = ItemDefCsv:get(itemId);
			if not itemDef then
				return;
			end

			local hasNum = GetInst("HomeLandDataManager"):GetBackpackItemTotalNum(itemId);
			if hasNum <= 0 then hasNum = 1; end

            CurMainPlayer:setItem(itemId, srcindex, hasNum, curSelectGridSid);
            
            for i=1,MAX_SHORTCUT do
                local ShortcutGrid = "HomelandBackpackShortcutGrid"..i
                local blinkTexture = getglobal(ShortcutGrid.."Check")
                if i ~= n and blinkTexture:IsShown() then 
                    blinkTexture:Hide()
                end
            end        
			SwapBlinkBtnName = "HomelandBackpackShortcutGrid"..n;
		elseif getglobal("RoleFrame"):IsShown() or GetInst("MiniUIManager"):IsShown("RoleFrame") or getglobal("StorageBoxFrame"):IsShown() then
			srcnum = ClientBackpack:getGridNum(srcindex)
		
			if srcnum >0 or ClientBackpack:getGridNum(CurSelectGridIndex) > 0 then
				if srcnum > 0 and IsGridIndexType(CurSelectGridIndex, EQUIP_START_INDEX) and not CanPutEquipItem(srcindex, CurSelectGridIndex) then
					return false
				end
				CurMainPlayer:swapItem(srcindex, CurSelectGridIndex)
			end
			--CurSelectGridIndex = -1;
		end

		if getglobal("MItemTipsFrame"):IsShown() then
			getglobal("MItemTipsFrame"):Hide();
		end

		--新资源tips弹框
		GetInst('SceneEditorMsgHandler'):dispatcher(SceneEditorResourceDef.event.resource_item_tips_close)
	else
		if CurMainPlayer:isInSpectatorMode() == true then
			if n == 1 then
				SpectatorLastPlayerBtn_OnClick();
			elseif n == 2 then
				SpectatorNextPlayerBtn_OnClick();
			elseif n == 3 and (ClientCurGame:getRuleOptionVal(41) == 2 or CurMainPlayer:getSpectatorMode() == 2)then
				SpectatorSwitchTypeBtn_OnClick();
			end
		else
			if CurMainPlayer:isShapeShift() then
				return
			end
			if  GetInst("MiniUIManager"):GetUI("MiniUIQQMusicPlayPianoMain") and 
				GetInst("MiniUIManager"):GetUI("MiniUIQQMusicPlayPianoMain"):GetScheduleKey() ~= nil then
				return
			end

			if not IsUGCEditing() or UGCModeMgr:GetGameType() ~= UGCGAMETYPE_BUILD then
				CurMainPlayer:setCurShortcut(n-1)
			end

			if CurWorld:getOWID() == NewbieWorldId and  n == 1 then
				
			end
		end
	end

end

function AccelKey_Num0()
	AccelKey_Number(10)	
end
function AccelKey_Num1()
	AccelKey_Number(1)
end
function AccelKey_Num2()
	AccelKey_Number(2)
end
function AccelKey_Num3()
	AccelKey_Number(3)
end
function AccelKey_Num4()
	AccelKey_Number(4)
end
function AccelKey_Num5()
	AccelKey_Number(5)
end
function AccelKey_Num6()
	AccelKey_Number(6)
end
function AccelKey_Num7()
	AccelKey_Number(7)
end
function AccelKey_Num8()
	AccelKey_Number(8)
end
function AccelKey_Num9()
	AccelKey_Number(9)
end
-- [ 键
function AccelKey_MicSwitch()
	if GetClientInfo():isEditorMode() then
		return
	end

	-- 家园地图中屏蔽 [ 键
	if IsInHomeLandMap() then
		return;
	end
	if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
		return
	end

	if isAbroadEvn() then
		--LLDO:13岁保护模式特殊处理: 不让点击, 点击飘字
		if IsProtectMode() then
			ShowGameTips(GetS(20211), 3);
			return;
		end
	end

	if GetChannelConfig():getVersionParamInt("GVoiceSDKEnabled", 1) == 1 then
		if not GetInst("TeamVocieManage"):isInTeamVocieRoom() then
			if GYouMeVoiceMgr:isJoinRoom() then
				if isAbroadEvn() then
				else
					GetInst("GameVoiceManage"):GVoiceMicSwitch()
				end
			else
				GVoiceJoinRoomBtn_OnClick();
			end
		end
		
	end
end
-- ] 键
function AccelKey_SpeakerSwitch()
	if GetClientInfo():isEditorMode() then
		return
	end

	-- 家园地图中屏蔽 ] 键
	if IsInHomeLandMap() then
		return;
	end
	if GetChannelConfig():getVersionParamInt("GVoiceSDKEnabled", 1) == 1 then
		if not GetInst("TeamVocieManage"):isInTeamVocieRoom() then
			if GYouMeVoiceMgr:isJoinRoom() then
				if isAbroadEvn() then
					GetInst("GameVoiceManage"):GVoiceSpeakerSwitch();
				end
			end
		end
	end
end

---- 动态刷新一个xml或者lua文件--------------------------------
---- 这里的改动只会在PC版本下生效
---- 对其他手机版本无影响，对PC版本也是安全的



g_fresh_file = {};        -- 放到了 ui/accelkeysF12.toc 文件中

g_fresh_time = 0;

function AccelKey_F12()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if LuaInterface and LuaInterface:isdebug() then 
		refresh_xml_or_lua(); --关闭F12刷新老UI导致的报错	
	end
end



--按F11切换全屏
function AccelKey_F11()
	if GetClientInfo():isEditorMode() then
		return
	end

	if GetClientInfo():isFullscreen() then
		getglobal("QQGameLogoFrame"):Hide();
		GetClientInfo():setFullscreen(false);
	else
		if isQQGamePc() then
			getglobal("QQGameLogoFrame"):Show();
		end
		GetClientInfo():setFullscreen(true);
	end
end

--按F11切换全屏
function AccelKey_I()

	if  GetInst("MiniUIManager"):IsShown("playermainAutoGen") then
		GetInst("MiniUIManager"):HideUI("playermainAutoGen")
	else
		GetInst("MiniUIManager"):ShowUI("playermainAutoGen")
	end
end

function AccelKey_CtrlQ()
	MiniLog("AccelKey_CtrlF11")
	GetInst("MiniUIManager"):HideUI("playermainAutoGen")
end

function AccelKey_CtrlW()

end

function AccelKey_CtrlE()

end

function AccelKey_KeyDescription()

end

--新手引导帮助/键位图
function AccelKey_F1()
	if GetInst("MiniUIManager"):IsShown("GMUIAutoGen") then
		GetInst("MiniUIManager"):CloseUI("GMUIAutoGen")
	else
		GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/fgui_helper"},"GMUI")
		GetInst("MiniUIManager"):OpenUI("GMUI", "miniui/miniworld/fgui_helper", "GMUIAutoGen", {disableOperateUI = false})
	end
end

--开始/停止录像
function AccelKey_F2()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	Log("AccelKey_F2:");
	if GetClientInfo():isEditorMode() then
		return
	end

	if IsShowTakePhotoMode() then
		return
	end
	if getglobal("GongNengFrameStartRecordBtn"):IsShown() then
		local worldDesc = AccountManager:getCurWorldDesc();
		if worldDesc and worldDesc.worldtype ~= 9 then
			--非录像存档
			GongNengFrameVideoRecordBtn_OnClick();
		end
	end
end

--进入工具模式; 退出工具模式
function AccelKey_F3()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end
	
    if ClientGameMgr:getCurGame():isInGame() then
        --家园屏蔽工具模式
        if IsInHomeLandMap() then
            return
        end
		--地图编辑的时候屏蔽对象编辑
		-- if  getglobal("MapEdit"):IsShown() then
		-- 	return
		-- end
		if GetInst("MiniUIManager"):IsShown("MapEditAutoGen") then
			return
		end

		if CurWorld then
			if not IsUGCEditMode() then
				if not getglobal("ToolModeFrameToolModeBtn"):IsShown() then
					print("111:");
					return;
				end

			if IsUIFrameShown("ToolModeFrame") and not IsUIFrameShown("DeveloperResourceLib") then
				press_btn("ToolModeFrameToolModeBtn");
			end
		end
	end
end
end

local iSimulateMobileScreenTestIndex = 1;
_G.UseTestServerIp = "127.0.0.1" 
_G.UseTestServerPort = 14835
function AccelKey_F5()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end
	
	if _G.UseTestServerIp then
		HideLobby()
		ShowLoadingFrame()
		
		print("AccelKey_F5: test server ", _G.UseTestServerIp, _G.UseTestServerPort);
		AccountManager:addRentHostAddress("1", _G.UseTestServerIp, _G.UseTestServerPort)
		AccountManager:requestConnectRentWorld(1000, "", 0, 1000, 0, "1")
	else
		if GetClientInfo():isPC() and GetClientInfo().SetScreenSize and GetClientInfo():getApiId() == 999  then
			local t_ScreenData = {
				{width=1560, height=720, edge=0},
				--{width=1560, height=720, edge=50},
				{width=1280, height=720, edge=0},
				{width=960, height=720, edge=0},
				--{width=960, height=720, edge=50},
			}
			local screenData = t_ScreenData[iSimulateMobileScreenTestIndex]
			GetClientInfo():SetScreenSize(screenData.width, screenData.height)
			ShowGameTipsWithoutFilter("width:"..screenData.width.."  height:"..screenData.height)
			GetInst("MiniUIManager"):resetAllAdaptScreen();

			iSimulateMobileScreenTestIndex = iSimulateMobileScreenTestIndex == #(t_ScreenData) and 1 or (iSimulateMobileScreenTestIndex+1);
		end
	end
end

function AccelKey_F6()
	if LuaInterface:getApiId() ~= 999 then
		return
	end
end

function AccelKey_PlayerList()
--	MultiPlayerInfoSwitch_OnClick();
end

function AccelKey_Store()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	if IsUIFrameShown("DeveloperModeSet") then
		return;
	end
	if GetRecordPkgManager():isRecordPlaying() then
	   return;
	end
	Log("LLLOG: AccelKey_Store:");
	GongNengFrameStoreGNBtn_OnClick();
end

function HideDevTools()
	if GetInst("MiniUIManager"):IsShown("DevToolsAutoGen") then
		local devToolsCtrl = GetInst("UIManager"):GetCtrl("DevTools")
		if devToolsCtrl then
			devToolsCtrl:CloseBtnClicked();
		end
	end
	HideMTipsInfo()--隐藏已显示的物品详情界面
end

function AccelKey_StoreSkin()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	if IsStandAloneMode() then
		ShowGameTips(GetS(25832),3)
		return
	end
	if IsUIFrameShown("MapRuleStartSettingScreenShotFrame") then
		return;
	end
	if GetInst("ModsLibPkgManager"):IsModslibPkgScreenShotFrameShown() then
		return;
	end

	if IsShowTakePhotoMode() then
		return
	end
	if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
		--社交大厅一期 屏蔽商城跳转逻辑
		return
	end

	Log("LLLOG: AccelKey_StoreSkin: O : Skin: 皮肤");
	HideDevTools()
	ShopJumpTabView(2)
	AccelKey_JumpFromResourceCenter()
end

function AccelKey_StoreInventory()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	if not CheckPlayerActionState(ENABLE_STORAGE) then
		return
	end
	
	if IsStandAloneMode() then
		ShowGameTips(GetS(25832),3)
		return
	end
	if IsUIFrameShown("MapRuleStartSettingScreenShotFrame") then
		return;
	end
	if GetInst("ModsLibPkgManager"):IsModslibPkgScreenShotFrameShown() then
		return;
	end
	if GetInst("UGCCommon") and GetInst("UGCCommon"):IsSidebarFrameShow(SceneEditorUIDef.MAIN_TAB.TYPE_RES) then
		return
	end
	if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
		--社交大厅一期 屏蔽商城跳转逻辑
		return
	end
	Log("LLLOG: AccelKey_StoreInventory: I : Stash: 仓库");
	HideDevTools()
	ShopJumpTabView(8)
	AccelKey_JumpFromResourceCenter()
end

function AccelKey_StoreChargeMoney()
	if LuaInterface:getApiId() ~= 999 then
		return
	end
	
	if GetClientInfo():isEditorMode() then
		return
	end

	if IsStandAloneMode() then
		ShowGameTips(GetS(25832),3)
		return
	end
	if IsUIFrameShown("MapRuleStartSettingScreenShotFrame") then
		return;
	end
	if GetInst("ModsLibPkgManager"):IsModslibPkgScreenShotFrameShown() then
		return;
	end
	if IsShowTakePhotoMode() then
		return
	end
	if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
		--社交大厅一期 屏蔽商城跳转逻辑
		return
	end
	Log("LLLOG: AccelKey_StoreChargeMoney: P : Buy MiniBean: 迷你币");
	HideDevTools()
	--GongNengFrameStoreGNBtn_OnClick();
	
	if GetInst("MiniUIManager"):IsShown("main_userinfocard") then
		if GetInst("MiniUIManager"):GetCtrl("main_userinfocard") then
			GetInst("MiniUIManager"):GetCtrl("main_userinfocard"):SetLayer(true)
		end
	end
	ShopJumpTabView(7)
	AccelKey_JumpFromResourceCenter()
	GetInst("PlayerInfoCardMgr"):CloseUI()
end

function AccelKey_JumpFromResourceCenter()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	if IsStandAloneMode() then
		ShowGameTips(GetS(25832),3)
		return
	end
	if HasUIFrame("ResourceCenter") and getglobal("ResourceCenter"):IsShown() then
		GetInst("UIManager"):Close("ResourceCenter")
	end 
	if IsUIFrameShown("ResourceShop") then
		local mvcFrame = GetInst("UIManager"):GetCtrl("ResourceShop");
		mvcFrame:CloseBtnClicked();
		return 
	end
end

function AccelKey_Friends()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if not CanUseUIShortCut() then return end

	if GetClientInfo():isEditorMode() then
		return
	end

	if IsStandAloneMode() then
		ShowGameTips(GetS(25832),3)
		return
	end
	if IsUIFrameShown("DeveloperModeSet") then
		return;
	end
	if GetInst("MiniUIManager"):GetUI("main_player_freeAutoGen") then
		return
	end
	if IsShowTakePhotoMode() then
		return
	end
	if GetInst("UGCCommon") and GetInst("UGCCommon"):IsToolMode() then
		return;
	end

	if IsUGCEditingHighMode() then
		return;
	end
	if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
		return
	end
	
	-- if not getglobal("NewRuleSetFrame"):IsShown() and not getglobal("MyModsEditorFrame"):IsShown() then
	if not GetInst("MiniUIManager"):IsShown("DevToolsAutoGen") and not IsUIFrameShown("MapRuleStartSettingScreenShotFrame") and not GetInst("ModsLibPkgManager"):IsModslibPkgScreenShotFrameShown() then
		local bShow = ModsLib_IsShown()
		if bShow then return end
		CurMainPlayer:setSightMode(true);
		if getglobal("RoomUIFrame"):IsShown() then
				getglobal("RoomUIFrame"):Hide();
		elseif getglobal("FriendUIFrame"):IsShown() then 
			getglobal("FriendUIFrame"):Hide();
		elseif getglobal("FriendFrame"):IsShown() then
			--LLTODO:关闭好友界面
			getglobal("FriendFrame"):Hide();
		else
			CurMainPlayer:setSightMode(false);
			InteractiveBtn_OnClick();
		end
	end 
end

function AccelKey_Mount()
	if GetClientInfo():isEditorMode() then
		return
	end

	if GetRecordPkgManager():isRecordPlaying() then
	   return;
	end
	if IsUIFrameShown("MapRuleStartSettingScreenShotFrame") then
		return;
	end
	if GetInst("ModsLibPkgManager"):IsModslibPkgScreenShotFrameShown() then
		return;
	end
	if GetInst("MiniUIManager"):GetUI("main_player_freeAutoGen") then
		return
	end

    AccRideCallBtn_OnClick();
end

function AccelKey_Battle()
	if GetClientInfo():isEditorMode() then
		return
	end

	if GetRecordPkgManager():isRecordPlaying() then
	   return;
	end
	if IsShowTakePhotoMode() then
		return
	end

	if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
		return
	end

	if CurWorld and CurWorld:isGameMakerRunMode() and GetInst("MiniUIManager"):IsShown("RankAutoGen") then
		if GetInst("MiniUIManager"):GetCtrl("Rank"):TabView() then
			return
		end
	end
	
	if getglobal("BattleBtn"):IsShown() then
		if GetInst("MiniUIManager"):IsShown("BattleFrameAutoGen") then --已结算
			GetInst("MiniUIManager"):CloseUI("BattleFrameAutoGen");
		else
			GetInst("MiniUIManager"):OpenUI("BattleFrame", "miniui/miniworld/ugc_battleFrame", "BattleFrameAutoGen", {});
		end
	end
end

function AccelKey_BuffStatus()
	if GetClientInfo():isEditorMode() then
		return
	end

	if GetRecordPkgManager():isRecordPlaying() then
	   return;
	end
	if IsShowTakePhotoMode() then
		return
	end
	local num = MainPlayerAttrib:getBuffNum();
	local showNum = 0;
	for i=1, num do
		local info = MainPlayerAttrib:getBuffInfo(i-1);
		--装备的buff不显示
		if info and info.def and info.def.BuffType == 1 then
			showNum = showNum + 1;
		end
	end

	if showNum > 0 then
		-- local BuffFrame = getglobal("BuffFrame")
		-- if BuffFrame:IsShown() then
		-- 	BuffFrame:Hide();
		-- else
		-- 	BuffFrame:Show();
		-- end
		if not GetInst("MiniUIManager"):IsShown("BuffFramePageAutoGen") then
			GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common","miniui/miniworld/c_ingame","miniui/miniworld/c_login"}, "BuffFramePageAutoGen")
			GetInst("MiniUIManager"):OpenUI("BuffFramePage", "miniui/miniworld/adventure", "BuffFramePageAutoGen");
		else
			GetInst("MiniUIManager"):CloseUI("BuffFramePageAutoGen")
		end
	end
end

function AccelKey_Achievement_Proc()
	if GetClientInfo():isEditorMode() then
		return
	end

	if GetInst("RoleAttrManager"):IsOpenNewRoleFrame() then
		GetInst("RoleAttrManager"):OpenRoleAttrFrame(3)
	else
		getglobal("RoleAttrFrame"):Show()
		RoleattrTabBtnTemplate_OnClick(3)
		GetInst("MiniUIManager"):OpenUI("task_main", "miniui/miniworld/newTaskSystem", "TaskMainCtrl")
	end
end
function AccelKey_Achievement()
	if GetClientInfo():isEditorMode() then
		return
	end

	if IsShowTakePhotoMode() then
		return
	end
	if  PlayAdventureNoteBtnCanShow() then 
		AdventureNoteBtn_OnClick();
	end
	if IsShowTaskSystem and IsShowTaskSystem() and GetInst("TaskSystemManager"):IsShowTask() then
		AdventureMode_OperCtrlPanel(1, "AccelKey_Achievement_Proc")
	end
end

function AccelKey_ModelLib()
	if GetClientInfo():isEditorMode() then
		return
	end

	if IsStandAloneMode() then
		ShowGameTips(GetS(25832),3)
		return
	end
	if IsInHomeLandMap and IsInHomeLandMap() then
		return --家园中不显示
	end

	if CurWorld and not CurWorld:isGodMode() then
		return;
	end
	if IsUIFrameShown("MapRuleStartSettingScreenShotFrame") then
		return;
	end
	if GetInst("ModsLibPkgManager"):IsModslibPkgScreenShotFrameShown() then
		return;
	end
	if IsUIFrameShown("DeveloperModeSet") then
		return;
	end
	if ResourceCenterNewVersionSwitch then
		GetInst("ResourceDataManager"):SetIsFromLobby(ResourceCenterOpenFrom.FromMap)
		if HasUIFrame("ResourceCenter") and getglobal("ResourceCenter"):IsShown() then
			GetInst("UIManager"):Close("ResourceCenter")
		else
			-- statisticsGameEventNew(1112, 3);
			if UGCModeMgr and UGCModeMgr:IsUGCMode() then
			else
				GetInst("UIManager"):Open("ResourceCenter",{UpdateView=true})
			end
		end
	else
		if getglobal("MapModelLibFrame"):IsShown() then
			getglobal("MapModelLibFrame"):Hide();
		else
			getglobal("MapModelLibFrame"):Show();
		end
	end
end


g_loaded_list = {}
--检查一个xml文件是否已经加载
function checkLoadOnce( xml_file_name )
	if 	g_loaded_list[xml_file_name] then
		--已经加载
	else
		gFunc_reloadXML( xml_file_name );
		g_loaded_list[xml_file_name] = 1;
	end		
end

function do_reload_lua( reload_path )
	if  string.match(reload_path, "^%s*%-%-") then 
		return --注释行不处理
	end 
	if  string.find(reload_path, "luascript/survivegameconfig.lua") then
		Log( "reload lua file=" .. reload_path );
		gFunc_reloadLUA( reload_path );
		ApplySurviveGameConfig();
	elseif string.find(reload_path, "require") then
		local ok, reqstr = pcall(loadstring, reload_path)
		if ok then reqstr() end
	elseif  string.find(reload_path, ".lua") then
		Log( "reload lua file=" .. reload_path );
		gFunc_reloadLUA( reload_path );
	else
		Log( "reload xml file=" .. reload_path );
		
		local xmlIsMvc = false;
		local xmlPath = reload_path;
		if string.find(xmlPath, "/mvc/") then--mvc--
			local match, fcount = string.gsub(xmlPath, "^.-/(%w+).xml$","%1")
			if fcount > 0 and GetInst("UIManager") then --match--
				xmlIsMvc = true;
				GetInst("UIManager"):DelCtrl( match );
			end
		end
		if not xmlIsMvc then
			gFunc_reloadXML( xmlPath );
		else
			GameUI:LoadXMLFile(xmlPath)	
		end
	end
end

function refresh_xml_or_lua()
	Log( "call refresh_xml_or_lua" );

	g_fresh_file = {};
	local fullpath = gFunc_ToPkgFullPath("ui/accelkeysF12_init.lua")
	local file = io.open(fullpath, "r");
    assert(file);
	for line in file:lines() do
    	Log(line);
    	--Log( string.sub(line,1,2) );
    	
    	if  string.sub(line,1,2) == "--" then
    		--是注释
    	elseif  string.find( line, ".xml") == nil and  string.find( line, ".lua") == nil then
			--非xml和lua
    	else
    	    table.insert( g_fresh_file, line );
    	end
    	
	end
	file:close();

	if g_fresh_file and #g_fresh_file>0 then
		--(1)重新加载一个XML文件，和其包含的lua文件			
		for i=1, #g_fresh_file do
			do_reload_lua(g_fresh_file[i])
		end
	end

	--(2)重新加载一个lua文件，几乎不会被用到
	--gFunc_reloadLUA( "ui/mobile/xxxxx.lua" );	
	--RuleFrame_OnLoad();
	--RuleOptionFrame_OnLoad();
	
	
	--(3)加载TOC，重加载所有的xml和lua，非常慢，也容易出错，不推荐
	--gFunc_reloadGameUI();
	--getglobal("LoginScreenFrame"):Hide();			
end


-- 每秒自动加载
function auto_refresh_xml( file_name )
	g_fresh_file = file_name;
	if ( os.time() > g_fresh_time ) then
		g_fresh_time = os.time()
		gFunc_reloadXML( g_fresh_file );
	end
end



function AccelKey_CtrlNum1()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	AccelKey_CtrlNumber(1);
end
function AccelKey_CtrlNum2()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	AccelKey_CtrlNumber(2);
end
function AccelKey_CtrlNum3()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	AccelKey_CtrlNumber(3);
end
function AccelKey_CtrlNum4()
	if LuaInterface:getApiId() ~= 999 then
		return
	end
end
function AccelKey_CtrlNum5()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	AccelKey_CtrlNumber(5);
end
function AccelKey_CtrlNum6()
	if LuaInterface:getApiId() ~= 999 then
		return
	end
end
function AccelKey_CtrlNum7()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	AccelKey_CtrlNumber(7);
end
function AccelKey_CtrlNum8()
	if LuaInterface:getApiId() ~= 999 then
		return
	end
	AccelKey_CtrlNumber(8);
end

function AccelKey_CtrlNumber(n)
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	-- 高级创造播放表情
	if IsUGCEditing() then
		return
	end
	
	print("ctrl+",n)
	
	if GetInst("actionExpressionManager"):IsOpenNew() then
		GetInst("actionExpressionManager"):PlayActionExpression(n)
	else
		PlayActionExpression(n)
	end
end

function AccelKey_MenuNumber(n)
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	n = n - 1
	if n == 0 then 
		n = 10
	end 
	if GetInst("actionExpressionManager"):IsOpenNew() then
		GetInst("actionExpressionManager"):SendPhrase(n)
	else
		SendPhraseBtnClicked(n)
	end
end

--按键N
function AccelKey_N()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	threadpool:work(function ()
		threadpool:wait(0.1) --需要等待0.1s，不然线程报错问题
		if GetInst("MiniUIManager"):IsShown("TopPurchaseInMapAutoGen") then
			GetInst("MiniUIManager"):GetCtrl("TopPurchaseInMap"):OnPressedN()
		end

		----地图内[Desc5]弹窗的广告按钮
		--if IsUIFrameShown("TopPurchaseInMapADBtn") then
		--	GetInst("UIManager"):GetCtrl("TopPurchaseInMap"):ADBtnClicked()
		--end
		--
		----地图内[Desc5]弹窗的迷你点按钮
		--if IsUIFrameShown("TopPurchaseInMapMiniPointBtn") then
		--	GetInst("UIManager"):GetCtrl("TopPurchaseInMap"):MiniPointBtnClicked()
		--end
	end)

end

--按键Y
function AccelKey_Y()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	if IsShowTakePhotoMode() then
		return
	end
	--触发器广告确定按钮
	if GetInst("MiniUIManager"):IsShown("TriggerAdInMapAutoGen") then
		GetInst("MiniUIManager"):GetCtrl("TriggerAdInMap"):OnSureBtnClick()
		return
	end
	-- if IsUIFrameShown("TriggerAdInMap") then
	-- 	GetInst("UIManager"):GetCtrl("TriggerAdInMap"):OkBtnClicked()
	-- 	return
	-- end
	threadpool:work(function ()
		threadpool:wait(0.1) --需要等待0.1s，不然线程报错问题
		if GetInst("MiniUIManager"):IsShown("TopPurchaseInMapAutoGen") then
			GetInst("MiniUIManager"):GetCtrl("TopPurchaseInMap"):OnPressedY()
		end

		----地图内[Desc5]弹窗的[Desc5]按钮
		--if IsUIFrameShown("TopPurchaseInMapBuyBtn") then
		--	GetInst("UIManager"):GetCtrl("TopPurchaseInMap"):BuyBtnClicked()
		--end
	end)
end

function AccelKey_MusicNumber(n,t)
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if not GetInst("MiniUIManager"):GetUI("main_player_freeAutoGen") then
		
		if n == InputKeyCode.SDLK_r and t == 0 then
			MusicPlayModeBtn_OnClick()
		end
		return
	end


	GetInst("MiniUIManager"):GetUI("main_player_freeAutoGen").ctrl:keyboardClick(n,t)  
end

function AccelKey_X()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	--展开动作栏
	if GetInst("MiniUIManager"):IsShown("SceneShotFrameAutoGen") then
		return;
	end
	if IsUIFrameShown("MapRuleStartSettingScreenShotFrame") then
		return;
	end
	if GetInst("ModsLibPkgManager"):IsModslibPkgScreenShotFrameShown() then
		return;
	end
	
	local resMainFrame = GetInst("MiniUIManager"):GetCtrl("ResourceBagMain")
	if resMainFrame and resMainFrame:IsBagShown() then
		return
	end

	CharacterActionBtn_OnClick();
	if getglobal("CharacterActionFrame"):IsShown() then
		CurMainPlayer:setSightMode(false);
	else
		CurMainPlayer:setSightMode(true);
	end
end

function AccelKey_R_LivingTool()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	if IsUIFrameShown("ToolModeFrame") then
		print("elmer_58585858")
		GetInst("UIManager"):GetCtrl("ToolModeFrame"):ActorCreateClick()
	end
end

function AccelKey_Z()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	--变形按钮
	print("AccelKey_Z")
	if GetClientInfo():isEditorMode() then
		return
	end

	if ClientGameMgr:getCurGame():isInGame() then
		AccRideChangeBtn_OnClick()
	end
end

function AccelKey_CtrlR()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end
	
	if ClientCurGame and ClientCurGame.getMainPlayer then
		ClientCurGame:getMainPlayer():teleportHome()
	end
end

function AccelKey_ScreenShotFrameFilter()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end
	
	if IsUIFrameShown("MapRuleStartSettingScreenShotFrame") then
		return true;
	end
	if GetInst("ModsLibPkgManager"):IsModslibPkgScreenShotFrameShown() then
		return;
	end
	return false;
end

-- 开发者悬浮窗快捷键
function AccelKey_DeveloperModeSet()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if GetClientInfo():isEditorMode() then
		return
	end

	if IsStandAloneMode() then
		ShowGameTips(GetS(25832),3)
		return
	end
	if IsInHomeLandMap and IsInHomeLandMap() then
		return
	end
	
	-- ugc模式不弹这个界面
	if UGCModeMgr and UGCModeMgr:IsUGCMode() then
		return
	end
	
	if ClientCurGame and ClientGameMgr:getCurGame():isInGame() and CurWorld and CurWorld:isGameMakerMode() and AccountManager:getMultiPlayer() == 0 then
		local uiName = "DeveloperModeSet"
		if IsUIFrameShown(uiName) then
			local DeveloperModeSetCtrl = GetInst("UIManager"):GetCtrl(uiName)
			if DeveloperModeSetCtrl and DeveloperModeSetCtrl.CloseBtnOnClicked then
				DeveloperModeSetCtrl:CloseBtnOnClicked()
			end
		else
			-- 关闭以下界面 -------
			-- 编辑模式下的商店
			if IsUIFrameShown("DeveloperStoreMapPurchaseFrame") then
				DeveloperStoreMapPurchaseFrameCloseBtn_OnClick()
			end
			-- 玩法模式下的商店
			if IsUIFrameShown("DeveloperStoreSkuFrame") then
				DeveloperStoreSkuFrameCloseBtn_OnClick()
			end
			-- 脚本,触发器
			if GetInst("MiniUIManager"):IsShown("DevToolsAutoGen") then
				local devToolsCtrl = GetInst("UIManager"):GetCtrl("DevTools")
				if devToolsCtrl then
					devToolsCtrl:CloseBtnClicked();
				end
			end
			-- 资源中心
			if ResourceCenterNewVersionSwitch then
				if HasUIFrame("ResourceCenter") and getglobal("ResourceCenter"):IsShown() then
					GetInst("UIManager"):Close("ResourceCenter")
				end
			else
				if getglobal("MapModelLibFrame"):IsShown() then
					getglobal("MapModelLibFrame"):Hide();
				end
			end
			-- 插件包
			ModsLib_CloseClick()
			-- 关闭对象库
			if IsUIFrameShown("ToolObjLib") then
				press_btn("ToolObjLibBodyCloseBtn");
			end

			GetInst("UIManager"):Open(uiName)
		end
	end
end

function AccelKey_F4(enable)
	if GetClientInfo():isEditorMode() then
		return
	end

	if not GetClientInfo():isPC() then
        return;
    end
	if enable then
		GetInst("MiniUIManager"):OpenUI("FGuiHelperMain", "miniui/miniworld/fgui_helper", "FguiHelper", {})
	else
		GetInst("MiniUIManager"):CloseUI("FguiHelper");
	end
end

function AccelKeyDownEvent(key)
	local context = MNSandbox.SandboxContext():SetData_Number("result", key):SetData_Bool("isKeyDown",true)
    SandboxLua.eventDispatcher:Emit(nil, GameEventType.PCKeyEvent, context)
	SandboxLua.eventDispatcher:Emit(nil, GameEventType.PCKeyDownEvent, context)
end

function AccelKeyUpEvent(key)
	local context = MNSandbox.SandboxContext():SetData_Number("result", key):SetData_Bool("isKeyDown",false)
    SandboxLua.eventDispatcher:Emit(nil, GameEventType.PCKeyEvent, context)
end

function AccelKey_MULTIPLY()
	if LuaInterface:getApiId() ~= 999 then
		return
	end

	if not GetClientInfo():isPC() then
        return;
    end
	if not (PlatformUtility:isDebug() and PlatformUtility:isPC()) then
		return
	end
	if GetInst("MiniUIManager"):IsShown("localTestTool") then
		GetInst("MiniUIManager"):CloseUI("localTestToolAutoGen")
	else
		GetInst("MiniUIManager"):OpenUI("localTestTool", "miniui/miniworld/localTestTool", "localTestToolAutoGen")
	end
end

-- function AcceKey_F7()
-- 	-- Before dumping, collect garbage first.
-- 	collectgarbage("collect")
-- 	memoryReferenceInfo.m_cMethods.DumpMemorySnapshot("./", "1-Before", -1)
-- end

-- function AcceKey_F8()
-- 	-- Dump memory snapshot again.
-- 	collectgarbage("collect")
-- 	memoryReferenceInfo.m_cMethods.DumpMemorySnapshot("./", "2-After", -1)
-- end
-- function AcceKey_F9() 
-- 	memoryReferenceInfo.m_cMethods.DumpMemorySnapshotComparedFile("./", "Compared", -1, 
-- 	"./LuaMemRefInfo-All-[1-Before].txt", 
-- 	"./LuaMemRefInfo-All-[2-After].txt")
-- end

function AccelKeyMouseWheel(delta)
	if IsUGCEditing() and UGCModeMgr:GetGameType() == UGCGAMETYPE_BUILD  then
		local resMainFrame = GetInst("MiniUIManager"):GetCtrl("ResourceBagMain")
		if resMainFrame and not resMainFrame:IsBagShown() then
			resMainFrame:OnMouseWheel(delta)
		end
	end
end

-- 根据界面的显示情况屏蔽快捷键
function IsAccelKeyEnable()
	local resMainFrame = GetInst("MiniUIManager"):GetCtrl("ResourceBagMain")
	if resMainFrame and resMainFrame:IsBagShown() then
		return false
	end

	return true
end

--@oper 1 / 0 : number
--@funcName 函数名 : string
function AdventureMode_OperCtrlPanel(oper, funcName)
	if type(oper) == "number" and type(funcName) == "string" then
		local b_result = false
		-- local is_adventureMode = WorldMgr and WorldMgr.getGameMode and (WorldMgr:getGameMode() == g_WorldType.OWTYPE_SINGLE)
		-- if is_adventureMode then
		-- 	b_result = not AdventureGuideMgr:isEnterInGuide()
		-- end
		if oper == 0 then
			if b_result then
				local context = MNSandbox.SandboxContext():SetData_String("CloseCtrlPanel", "")
				SandboxLua.eventDispatcher:Emit(nil, "ClientGameUserInputEvent", context)
			end
		elseif oper == 1 then
			if b_result and not CurMainPlayer:isDead() then
				local context = MNSandbox.SandboxContext():SetData_String("OpenCtrlPanel", "")
				SandboxLua.eventDispatcher:Emit(nil, "ClientGameUserInputEvent", context)
				threadpool:work(function()
					threadpool:wait(LuaConstants:get().botCtrlPanelDelayTime or 0.1);
					_G[funcName]();
				end)
			else
				_G[funcName]();
			end
		end
	end
end

function OpenBackpackSandboxEvent()
	SandboxLua.eventDispatcher:Emit(nil, "sandboxEvent_openBackpack", MNSandbox.SandboxContext())
end